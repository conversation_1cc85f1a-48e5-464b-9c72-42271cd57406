# Base image
FROM amd64/python:3.13-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY motion_module.py .

CMD ["python", "-u", "motion_module.py"]


#"createOptions": "{\"Env\":[\"CAMERA_USERNAME=root\",\"CAMERA_PASSWORD=M1ecrdry1!\"]}"
