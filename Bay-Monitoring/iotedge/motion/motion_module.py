# motion_module.py
import asyncio
import json
import logging
import os
import signal
import ssl
import time
from typing import Dict

import aiohttp
from azure.iot.device.aio import IoTHubModuleClient
from asyncio_mqtt import Client as MQTTClient, MqttError
import httpx

# ────────────────────────────────────────────────────────────
# Config – overridable by twin afterwards
# ────────────────────────────────────────────────────────────
MQTT_BROKER  = os.getenv("MQTT_BROKER", "***********")
MQTT_PORT    = int(os.getenv("MQTT_PORT", 1883))
MQTT_TOPIC   = os.getenv("MQTT_TOPIC", "motion")

CAMERA_API_IP = os.getenv("CAMERA_API_IP", "***********")   # twin can overwrite
API_URL       = f"http://{CAMERA_API_IP}:8000/cameras/"

FILTER_PREFIX    = "axis:CameraApplicationPlatform/VMD/Camera"
TIME_THRESHOLD_MS = 12_000

CAMERA_USERNAME  = os.getenv("CAMERA_USERNAME", "root")
CAMERA_PASSWORD  = os.getenv("CAMERA_PASSWORD", "M1ecrdry1!")

CLIENT_ID = "motion_listener"

logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s  %(levelname)-8s | %(message)s",
)
logger = logging.getLogger("motion-module")

ssl_ctx = ssl.create_default_context()
ssl_ctx.check_hostname = False
ssl_ctx.verify_mode = ssl.CERT_NONE

# state
camera_info: Dict[str, dict] = {}   # serial → {ip, username, password}
clusters: Dict[str, dict] = {}      # serial → {last_time, count}


# ────────────────────────────────────────────────────────────
# Helpers to apply twin config
# ────────────────────────────────────────────────────────────
def apply_config(cfg: dict):
    global MQTT_BROKER, MQTT_TOPIC, CAMERA_API_IP, API_URL
    MQTT_BROKER   = cfg.get("mqttBrokerIp", MQTT_BROKER)
    MQTT_TOPIC    = cfg.get("mqttTopic", MQTT_TOPIC)
    CAMERA_API_IP = cfg.get("cameraApiIp", CAMERA_API_IP)
    API_URL       = f"http://{CAMERA_API_IP}:8000/cameras/"
    logger.info(f"🔧 Config → broker={MQTT_BROKER}, topic={MQTT_TOPIC}, cameraAPI={API_URL}")


# ────────────────────────────────────────────────────────────
# Camera discovery
# ────────────────────────────────────────────────────────────
async def fetch_camera_info():
    global camera_info
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(API_URL, ssl=ssl_ctx, timeout=10) as resp:
                resp.raise_for_status()
                cameras = await resp.json()
        info = {
            cam["serial"]: {
                "ip": cam["camera_ip"],
                "username": CAMERA_USERNAME,
                "password": CAMERA_PASSWORD,
            }
            for cam in cameras
            if cam.get("serial") and cam.get("camera_ip")
        }
        camera_info = info
        logger.info(f"✅ Loaded {len(camera_info)} cameras from API")
    except Exception as exc:
        logger.error(f"Camera discovery failed: {exc}")
        camera_info = {}


# ────────────────────────────────────────────────────────────
# Guard‑tour logic
# ────────────────────────────────────────────────────────────
async def trigger_guard_tours(serial, iot_client):
    cam = camera_info.get(serial)
    if not cam:
        logger.error(f"No camera info for serial {serial}")
        return

    ip  = cam["ip"]
    auth = httpx.DigestAuth(cam["username"], cam["password"])

    async with httpx.AsyncClient(auth=auth, timeout=10) as client:
        # Pre‑auth handshake (ignored errors)
        try:
            await client.get(f"http://{ip}/axis-cgi/param.cgi?action=info")
        except Exception:
            pass

        group = 0
        while True:
            group_param = f"GuardTour.G{group}"
            start = f"http://{ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=yes"
            try:
                r = await client.get(start)
                if "Error" in r.text:
                    break
                logger.info(f"[{serial}] Started {group_param}")
            except Exception as exc:
                logger.error(f"Start tour error: {exc}")
                break

            # Let it run 2 min (adjustable)
            await asyncio.sleep(120)

            stop = f"http://{ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=no"
            try:
                await client.get(stop)
                logger.info(f"[{serial}] Stopped {group_param}")
            except Exception as exc:
                logger.error(f"Stop tour error: {exc}")
            group += 1

        logger.info(f"[{serial}] Guard‑tour sequence finished (groups={group})")

    # Notify downstream / cloud
    await iot_client.send_message_to_output(
        json.dumps({"event": "guardTourTriggered", "serial": serial}),
        "output1",
    )


# ────────────────────────────────────────────────────────────
# Handle each motion event
# ────────────────────────────────────────────────────────────
async def process_motion(payload, iot_client):
    topic_field = payload.get("topic", "")
    if not topic_field.startswith(FILTER_PREFIX):
        return

    serial = payload.get("serial")
    ts_ms  = payload.get("timestamp")
    if serial is None or ts_ms is None:
        return

    now = int(ts_ms)
    cluster = clusters.setdefault(serial, {"last_time": now, "count": 0})

    if now - cluster["last_time"] > TIME_THRESHOLD_MS:
        cluster["count"] = 1
    else:
        cluster["count"] += 1

    cluster["last_time"] = now
    logger.info(f"[{serial}] cluster count → {cluster['count']}")

    if cluster["count"] > 3:          # threshold hit
        logger.warning(f"[{serial}] Excess motion – triggering guard tours")
        cluster["count"] = 0
        asyncio.create_task(trigger_guard_tours(serial, iot_client))


# ────────────────────────────────────────────────────────────
# MQTT listener (asyncio‑mqtt)
# ────────────────────────────────────────────────────────────
async def mqtt_listener(iot_client):
    while True:             # auto‑reconnect loop
        try:
            async with MQTTClient(
                hostname=MQTT_BROKER,
                port=MQTT_PORT,
                client_id=CLIENT_ID,
            ) as mqtt:
                await mqtt.subscribe(MQTT_TOPIC)
                logger.info(f"📡 Subscribed '{MQTT_TOPIC}' on {MQTT_BROKER}")

                async for msg in mqtt.messages:
                    try:
                        payload = json.loads(msg.payload.decode())
                    except Exception:
                        continue
                    asyncio.create_task(process_motion(payload, iot_client))
        except MqttError as exc:
            logger.error(f"MQTT error: {exc} (reconnecting in 5 s)")
            await asyncio.sleep(5)


# ────────────────────────────────────────────────────────────
# Main entry point
# ────────────────────────────────────────────────────────────
async def main():
    # 1. connect to Edge Hub
    iot_client = IoTHubModuleClient.create_from_edge_environment()
    await iot_client.connect()
    logger.info("🔗 Connected to Edge Hub")

    # 2. initial twin load
    twin = await iot_client.get_twin()
    apply_config(twin["desired"].get("config", {}))

    # 3. watch for live twin patches
    async def twin_patch_handler(patch):
        if "config" in patch:
            apply_config(patch["config"])

    iot_client.on_twin_desired_properties_patch_received = twin_patch_handler

    # 4. discover cameras
    await fetch_camera_info()

    # 5. handle SIGTERM/SIGINT for graceful shutdown
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    def _stop():
        logger.info("👋 Termination requested")
        stop_event.set()

    for sig in (signal.SIGTERM, signal.SIGINT):
        try:
            loop.add_signal_handler(sig, _stop)
        except NotImplementedError:
            signal.signal(sig, lambda *_: _stop())

    # 6. run forever until stopped
    listener_task = asyncio.create_task(mqtt_listener(iot_client))
    await stop_event.wait()
    listener_task.cancel()
    await iot_client.disconnect()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as exc:
        logger.error(f"Fatal: {exc}")
