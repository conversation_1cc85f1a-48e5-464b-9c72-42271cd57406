# bay_schedule_module.py
import asyncio
import json
import logging
import os
import signal
from collections import defaultdict
from datetime import datetime
from typing import List

import aiohttp
import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from azure.iot.device.aio import IoTHubModuleClient

# ──────────────────────────────────────────────
# defaults (over‑ridden by twin)
# ──────────────────────────────────────────────
API_BASE_URL   = os.getenv("API_BASE_URL", "http://localhost:8000")
VACANT_TIMES   = ["09:00"]
OCCUPIED_TIMES = ["22:00"]
TIME_ZONE      = "Australia/Sydney"
API_TIMEOUT    = 30

logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s  %(levelname)-8s | %(message)s",
)
logger = logging.getLogger("bay-scheduler")

# scheduler lives for the whole process
scheduler = AsyncIOScheduler(timezone=pytz.timezone(TIME_ZONE))

# ──────────────────────────────────────────────
# helper – (re)apply config from twin
# ──────────────────────────────────────────────
def apply_config(cfg: dict):
    global API_BASE_URL, VACANT_TIMES, OCCUPIED_TIMES, TIME_ZONE

    API_BASE_URL   = cfg.get("apiBaseUrl", API_BASE_URL)
    VACANT_TIMES   = cfg.get("vacantTimes",   VACANT_TIMES)
    OCCUPIED_TIMES = cfg.get("occupiedTimes", OCCUPIED_TIMES)
    TIME_ZONE      = cfg.get("timeZone", TIME_ZONE)

    logger.info(
        f"⚙️  config → API={API_BASE_URL}, "
        f"vacantTimes={VACANT_TIMES}, occupiedTimes={OCCUPIED_TIMES}, tz={TIME_ZONE}"
    )

    # reschedule jobs
    scheduler.remove_all_jobs()
    try:
        scheduler.configure(timezone=pytz.timezone(TIME_ZONE))
    except Exception:
        logger.warning(f"Invalid tz '{TIME_ZONE}', falling back to UTC")
        scheduler.configure(timezone=pytz.utc)

    for t in VACANT_TIMES:
        _add_job(t, "vacant")
    for t in OCCUPIED_TIMES:
        _add_job(t, "occupied")


def _add_job(time_str: str, status: str):
    try:
        hour, minute = map(int, time_str.split(":"))
        scheduler.add_job(
            check_bays,
            "cron",
            hour=hour,
            minute=minute,
            args=[status],
            id=f"{status}-{time_str}",
            replace_existing=True,
        )
        logger.info(f"⏰ scheduled {status} check @ {time_str}")
    except Exception as exc:
        logger.error(f"Bad time '{time_str}' ignored: {exc}")


# ──────────────────────────────────────────────
# Bay‑check logic  (async rewrite of your script)
# ──────────────────────────────────────────────
async def check_bays(status_filter: str):
    run_ts = datetime.now(pytz.timezone(TIME_ZONE)).strftime("%Y-%m-%d %H:%M")
    logger.info(f"▶ bay check ({status_filter}) started @ {run_ts}")

    try:
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=API_TIMEOUT)
        ) as session:
            # 1. fetch camera list
            cams_r = await session.get(f"{API_BASE_URL}/cameras/")
            cams_r.raise_for_status()
            cameras = await cams_r.json()

            camera_last: defaultdict[str, float] = defaultdict(float)
            camera_queues = {
                cam["serial"]: [
                    bay
                    for bay in cam.get("bays", [])
                    if bay.get("status") == status_filter
                ]
                for cam in cameras
                if cam.get("serial")
            }
            total_checked = 0

            while camera_queues:
                progress = False
                now = asyncio.get_event_loop().time()
                for serial in list(camera_queues.keys()):
                    if now - camera_last[serial] < 10 and camera_last[serial] > 0:
                        continue

                    bay = camera_queues[serial].pop(0)
                    bay_id = bay.get("bay_id")
                    if not bay_id:
                        if not camera_queues[serial]:
                            del camera_queues[serial]
                        continue

                    logger.info(f"→ checking bay {bay_id} on cam {serial}")
                    try:
                        resp = await session.get(
                            f"{API_BASE_URL}/api/bays/search",
                            params={"bay_id": bay_id},
                        )
                        if resp.status == 200:
                            total_checked += 1
                        else:
                            logger.warning(
                                f"Bay {bay_id} returned {resp.status}: {await resp.text()}"
                            )
                    except Exception as exc:
                        logger.error(f"Bay {bay_id} check failed: {exc}")

                    camera_last[serial] = asyncio.get_event_loop().time()
                    if not camera_queues[serial]:
                        del camera_queues[serial]

                    progress = True
                    break  # process one bay per iteration

                if not progress and camera_queues:
                    await asyncio.sleep(1)

            logger.info(
                f"✅ bay check ({status_filter}) finished — total bays checked: {total_checked}"
            )

    except Exception as exc:
        logger.error(f"Bay‑check run failed: {exc}")


# ──────────────────────────────────────────────
# main life‑cycle
# ──────────────────────────────────────────────
async def main():
    # 1. Edge Hub connect
    iot = IoTHubModuleClient.create_from_edge_environment()
    await iot.connect()
    logger.info("🔗 connected to Edge Hub")

    # 2. initial twin config
    twin = await iot.get_twin()
    apply_config(twin["desired"].get("config", {}))

    # 3. handle live patches
    async def twin_patch(patch):
        if "config" in patch:
            apply_config(patch["config"])

    iot.on_twin_desired_properties_patch_received = twin_patch

    # 4. scheduler start
    scheduler.start()

    # 5. graceful shutdown
    loop = asyncio.get_running_loop()
    stop = asyncio.Event()

    def _sig(*_):
        logger.info("👋 termination requested")
        stop.set()

    for s in (signal.SIGTERM, signal.SIGINT):
        try:
            loop.add_signal_handler(s, _sig)
        except NotImplementedError:
            signal.signal(s, _sig)

    await stop.wait()
    scheduler.shutdown()
    await iot.disconnect()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as exc:
        logger.error(f"Fatal: {exc}")
