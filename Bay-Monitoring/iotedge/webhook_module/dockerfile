# Base image
FROM amd64/python:3.13-slim

WORKDIR /app

# Install system dependencies (if needed for any C extensions in pip packages)
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install test dependencies
RUN pip install --no-cache-dir pytest

# Copy all files including tests
COPY webhook_module.py .
COPY firebase_handler.py .
COPY test_webhook_module.py .

# Run tests as part of the build
RUN pytest -xvs test_webhook_module.py

# Entrypoint
CMD ["python", "-u", "webhook_module.py"]

# Example for module deployment configuration in deployment.template.json
# Add environment variables needed by your module here.
# "createOptions": "{\"Env\":[\"API_SERVER_IP=***********\",\"MQTT_BROKER=***********\",\"MQTT_PORT=1883\",\"WEBHOOK_PORT=8082\",\"GOOGLE_APPLICATION_CREDENTIALS={\\\"type\\\": \\\"service_account\\\", \\\"project_id\\\": \\\"your-project-id\\\", ...}\",\"FIREBASE_STORAGE_BUCKET_NAME=your-bucket-name.appspot.com\",\"CAMERA_USERNAME=root\",\"CAMERA_PASSWORD=M1ecrdry1!\"]}"
