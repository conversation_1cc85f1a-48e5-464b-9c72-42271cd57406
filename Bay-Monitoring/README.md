# README

## Introduction

This project is an intelligent parking management system designed to monitor parking bay availability, detect vehicles, recognize license plates, and manage bay allocations and alerts. It utilizes a combination of on-premise edge computing for real-time processing and a web application for management and overview.

## System Architecture

The system processes parking events through a pipeline of edge and cloud components. The overall data flow is as follows:

1.  **Motion Detection:** Edge modules monitor camera feeds for any motion.
2.  **Vehicle/Car Detection:** Upon motion detection, an AI model determines if a vehicle is present.
3.  **License Plate Recognition:** If a vehicle is detected, another AI model attempts to recognize the license plate.
4.  **Webhook Processing:** The results (vehicle presence, license plate if recognized, timestamp) are sent via webhook to a central processing unit.
5.  **Bay Status Update & Alerts:** The central system updates the parking bay status (occupied/vacant), records the license plate, and triggers alerts if necessary (e.g., unauthorized parking, bay reservation conflicts).

**Key Components:**

*   **Edge Modules (Azure IoT Edge):**
    *   `VehicleDetect`: Performs vehicle detection and license plate recognition using AI models.
    *   `BayAlerts`: Manages alerts based on bay status and rules.
    *   `Motion`: Detects motion in camera feeds to trigger further processing.
    *   `Schedule`: Manages bay reservations and scheduled access.
    *   `Webhook`: Forwards processed data from the edge to the backend.
*   **MQTT Broker:** Facilitates communication between edge modules.
*   **FastAPI Backend:** Handles incoming data from webhooks, processes business logic, and interacts with the database.
*   **MongoDB:** Stores data related to parking bays, vehicle logs, user accounts, and system configuration.
*   **Flask WebApp:** Provides a user interface for system management, monitoring bay status, viewing reports, and managing reservations.
*   **Firebase Storage:** Used for storing images or video clips related to parking events (e.g., images of detected vehicles).

```
[Placeholder for Text-Based System Architecture Diagram]
```

## Modules Overview

This project is composed of the following main components and modules:

### Edge Modules (Azure IoT Edge)

These modules run on edge devices, close to the cameras, to perform real-time processing of video feeds.

*   **VehicleDetect (`caryapa.py`):** This module is responsible for detecting vehicles and recognizing license plates. It processes video frames (or images) from camera streams.
    *   It uses YOLO (You Only Look Once) object detection models (e.g., `bestcar.pt`, `bestbike.pt`, `car_bike_empty_1_2.pt`) to identify cars and motorcycles.
    *   Upon detecting a vehicle, it attempts to extract and recognize the license plate using another specialized model/logic.
    *   The results, including the type of vehicle, license plate information (if available), and detection timestamp, are published to the MQTT broker.
*   **Webhook (`webhook_module.py`, `firebase_handler.py`):** This module acts as a bridge between the edge processing pipeline and the backend systems.
    *   It subscribes to relevant MQTT topics where detection results from `VehicleDetect` and other modules are published.
    *   It formats these messages and forwards them via HTTP POST requests (webhooks) to the FastAPI backend for further processing and storage.
    *   It integrates with Firebase Storage (via `firebase_handler.py`) to upload images or video clips associated with detection events, allowing for visual verification and record-keeping.
*   **BayAlerts (`alerts_module.py`):** This module monitors the status of parking bays and generates alerts based on predefined rules and real-time events.
    *   It likely subscribes to MQTT topics that provide information about bay occupancy (from `VehicleDetect` via the backend or directly) and reservation status (from `Schedule`).
    *   It can trigger alerts for events such as unauthorized parking in a reserved bay, a bay remaining occupied for too long, or other configurable conditions. Alerts might be sent via MQTT or other notification mechanisms.
*   **Motion (`motion_module.py`):** This module's primary function is to detect motion in the camera feeds.
    *   It continuously analyzes the video stream from a camera to identify significant changes or movement.
    *   When motion is detected, it publishes a message to the MQTT broker, typically triggering other modules like `VehicleDetect` to start more computationally intensive processing (vehicle detection, LPR). This helps in conserving resources by only running heavier tasks when necessary.
*   **Schedule (`bay_schedule_module.py`):** This module manages parking bay reservations and schedules.
    *   It likely maintains a schedule of bookings for different parking bays.
    *   It can publish information about current and upcoming reservations to the MQTT broker, allowing `BayAlerts` to check for conflicts or `VehicleDetect` to know if a detected car is expected.
    *   It might also receive commands (e.g., via MQTT or HTTP from the backend) to create, update, or cancel reservations.

### Backend Systems

*   **FastAPI Backend (`webapp/project_commissioning/api.py`):** This component serves as the main server-side application logic.
    *   It receives real-time parking event data (vehicle detection, LPR) from the Edge Webhook module.
    *   Manages camera configurations (CRUD operations - Create, Read, Update, Delete).
    *   Manages parking bay configurations (CRUD operations), including their association with specific cameras and physical locations.
    *   Handles the core bay status logic: determining if a bay is occupied, vacant, reserved, or in an error state based on incoming data and schedules.
    *   Stores all relevant data persistently in MongoDB, including camera details, bay layouts, vehicle detection logs, user information, and system settings.
    *   Provides a comprehensive set of API endpoints for the Flask WebApp and potentially other clients. These endpoints allow for:
        *   Fetching live images or video streams from cameras (proxied or direct).
        *   Searching and retrieving parking event data (e.g., by license plate, bay ID, tenant ID, time range).
        *   Managing user accounts and permissions.
        *   Updating system configurations.
*   **MongoDB:** The primary database for the system. It stores information about:
    *   Parking bay configurations (location, type, camera association, ROI coordinates, etc.)
    *   Current and historical status of each bay (occupied, vacant, reserved, timestamp of last change)
    *   Vehicle detection logs (license plates, vehicle type, timestamps, image/video references, associated bay)
    *   User accounts, roles, and permissions
    *   Tenant information (if applicable for multi-tenant deployments)
    *   Reservation details and schedules
    *   System settings, audit logs, and alert history.

### Communication

*   **MQTT Server (Eclipse Mosquitto):** An MQTT broker is used as the central messaging backbone for communication between the various edge modules. Modules publish events (e.g., motion detected, vehicle detected) to specific topics, and other modules subscribe to these topics to receive the information they need to perform their functions. This decouples the modules and allows for a flexible and scalable architecture on the edge.

### Web Application

*   **Flask WebApp (`webapp/project_commissioning/app.py`):** This is the primary user interface for interacting with and managing the intelligent parking system. It communicates with the FastAPI backend to display data and trigger actions.
    *   **System Management:** Provides dashboards and tools for administrators to oversee the entire system.
    *   **Camera Management:**
        *   View list of configured cameras and their current status.
        *   Add new cameras, edit existing camera details (e.g., stream URL, location).
        *   Delete cameras.
        *   View live feeds from cameras.
    *   **Bay Management:**
        *   View a map or list of all parking bays and their real-time occupancy status (e.g., color-coded).
        *   Add new parking bays, define their ROI (Region of Interest) on a camera feed, and link them to cameras.
        *   Edit bay details (e.g., bay ID, type, reservation status).
        *   Delete bays.
    *   **Data Viewing and Search:**
        *   View logs of parking events.
        *   Search for vehicles by license plate.
        *   Filter bay history by bay ID or tenant.
    *   **Bulk Operations:**
        *   Import camera configurations from a CSV file.
        *   Export camera configurations to a CSV file.
        *   Import parking bay definitions from a CSV file.
        *   Export parking bay data to a CSV file.
    *   **User and Reservation Management (Potentially):** Depending on the full scope, it might also include features for managing user accounts, roles, and parking reservations.

## Setup and Installation

To set up and install the project, follow these general steps. More specific instructions for edge modules might be found within their respective directories.

### Prerequisites

*   **Python:** Version 3.7+ is recommended.
*   **Docker & Docker Compose:** Required for building and running containerized versions of the application, especially the Edge modules.
*   **Azure IoT Edge runtime:** Optional. Only needed if you plan to deploy the full solution with Azure IoT Edge modules on an edge device.
*   **MongoDB:** An active MongoDB instance (local or cloud-hosted like MongoDB Atlas) is required for data storage.
*   **Firebase Project:** A Google Firebase project is needed for features like storing images/videos of detected vehicles.

### Cloning the Repository

```bash
git clone [repository URL] # Replace [repository URL] with the actual URL
cd [project-directory]   # Replace [project-directory] with the cloned directory name
```

### FastAPI Backend & Flask WebApp Setup

These instructions are primarily for setting up the web application components (FastAPI backend and Flask frontend).

1.  **Create and Activate a Python Virtual Environment:**
    It's highly recommended to use a virtual environment to manage project dependencies.
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows use `venv\Scripts\activate`
    ```

2.  **Install Python Dependencies:**
    The main web application dependencies are likely in `webapp/project_commissioning/`. Some IoT Edge modules might have their own `requirements.txt`.
    ```bash
    # For the main web application
    pip install -r webapp/project_commissioning/requirements.txt 

    # For individual Python-based IoT Edge modules (example)
    # pip install -r edge/EdgeSolution/modules/VehicleDetect/requirements.txt 
    # (Adjust paths as per your project structure)
    ```

3.  **Set Environment Variables:**
    Crucial configuration parameters are managed via environment variables. **Do not hardcode them in the source code.** Create a `.env` file in the root of the `webapp/project_commissioning/` directory (or where your main application loads them, e.g., using `python-dotenv`) and add necessary variables.
    Example `.env` file content:
    ```env
    MONGO_URI="your_mongodb_connection_string"
    FIREBASE_STORAGE_BUCKET="your-firebase-storage-bucket-name.appspot.com"
    FIREBASE_API_KEY="your_firebase_api_key"
    FIREBASE_AUTH_DOMAIN="your-firebase-auth-domain"
    FIREBASE_PROJECT_ID="your-firebase-project-id"
    # Add other necessary variables like Azure AD details if used for authentication
    # AZURE_AD_CLIENT_ID="your_azure_ad_client_id"
    # AZURE_AD_CLIENT_SECRET="your_azure_ad_client_secret"
    # AZURE_AD_TENANT_ID="your_azure_ad_tenant_id"
    # SECRET_KEY="a_strong_random_secret_key_for_flask" 
    ```
    Ensure this `.env` file is listed in your `.gitignore` to prevent committing sensitive credentials.

4.  **Run the FastAPI Backend:**
    The FastAPI application serves as the API backend.
    ```bash
    uvicorn webapp.project_commissioning.api:app --host 0.0.0.0 --port 8000 --reload
    ```
    The `--reload` flag is useful for development as it automatically reloads the server on code changes.

5.  **Run the Flask WebApp:**
    The Flask application serves as the frontend user interface.
    ```bash
    python webapp.project_commissioning.app.py
    ```
    Ensure the Flask app is configured to communicate with the FastAPI backend (usually via environment variables pointing to the FastAPI address).

### IoT Edge Modules Setup

This section details the setup specific to the Azure IoT Edge modules.

*   **Deployment Configuration:** The primary configuration for deploying edge modules is defined in `edge/EdgeSolution/deployment.template.json`. This file specifies:
    *   Which modules to deploy (e.g., `VehicleDetect`, `Webhook`, `Motion`, `BayAlerts`, `Schedule`).
    *   The Docker image for each module, typically hosted in a container registry like Azure Container Registry (ACR). The registry details (server, username, password) are usually configured in the `.env` file for the Azure IoT Edge solution or directly within the Azure IoT Hub device configuration.
    *   Routes for message passing between modules and to IoT Hub using the MQTT broker.
    *   Desired properties for each module, which act as configurations passed to the module instance at runtime.
*   **Container Registry:** You will need a container registry (e.g., Azure Container Registry, Docker Hub) to store the Docker images for each edge module. The `deployment.template.json` references these images.
*   **Environment Variables & Desired Properties:** These are critical for configuring the behavior of each module.
    *   **Environment variables** can be set within the `deployment.template.json` for each module (under `env`). These often include settings like the MQTT broker IP address/hostname, API endpoints for communication (e.g., the Webhook module needs to know the FastAPI backend URL), camera stream URLs, and credentials.
    *   **Desired properties** are also set in `deployment.template.json` within the `properties.desired` section for each module. These are used to pass module-specific settings, such as AI model paths, confidence thresholds, processing intervals, region of interest coordinates for cameras, etc.
    *   For example, the `VehicleDetect` module would need paths to YOLO models, and the `Webhook` module would need the target URL for the backend API. These are typically passed as desired properties or environment variables.

### Firebase Setup

Firebase is used in this project, primarily for storing images or video clips associated with parking events (handled by `modules/firebase_handler.py` or a similar utility).

1.  **Firebase Project:** Ensure you have an active Firebase project.
2.  **Service Account Key:**
    *   To allow the backend application (and potentially edge modules if they upload directly) to interact with your Firebase project services (like Cloud Storage), you need a service account key.
    *   Generate this JSON key from your Firebase project settings (Project settings -> Service accounts -> Generate new private key).
    *   Securely store this key file.
3.  **Set `GOOGLE_APPLICATION_CREDENTIALS`:**
    *   The path to this service account key JSON file must be set as an environment variable named `GOOGLE_APPLICATION_CREDENTIALS`.
    *   For the FastAPI backend, you would typically set this in your `.env` file or system environment.
    *   Example: `GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/firebase-service-account-key.json"`
4.  **Firebase Storage Bucket:**
    *   The name of your Firebase Storage bucket needs to be configured. This is often done via an environment variable (e.g., `FIREBASE_STORAGE_BUCKET` as mentioned in the FastAPI setup).
    *   The `modules/firebase_handler.py` or similar code will use this bucket name to upload files. Ensure it matches the bucket name in your Firebase project (e.g., `your-project-id.appspot.com`).

### YOLO Models Setup

The `VehicleDetect` module (e.g., implemented in `caryapa.py`) relies on YOLO (You Only Look Once) model files (`.pt` format) for object detection.

*   **Required Model Files:**
    *   You will need the specific `.pt` model files used by the application, such as `car_nocar_1_2.pt`, `car_bike_empty_1_2.pt`, `bestcar.pt`, `bestbike.pt`, etc.
    *   These files contain the trained weights for the neural networks.
*   **Placement and Accessibility:**
    *   These model files must be placed in a directory that is accessible to the `VehicleDetect` module at runtime.
    *   When deploying the `VehicleDetect` module as a Docker container (common in IoT Edge), these models should be:
        *   Included directly within the Docker image: Copied into the image during the Docker build process (see the module's Dockerfile). This is the most common method for edge deployments.
        *   Mounted via a volume: If models are very large or updated frequently, Docker volumes can be used to map a directory from the host edge device into the container. This would be configured in the `createOptions` of the module in `deployment.template.json`.
    *   The path to these models within the container is then configured for `caryapa.py` (often via desired properties in `deployment.template.json`).

### Edge Module Setup (General)

For deploying and running the Azure IoT Edge modules:
*   Refer to the Azure IoT Edge documentation.
*   Each module (e.g., `VehicleDetect`, `Webhook`) will have its own Dockerfile (e.g., `edge/EdgeSolution/modules/VehicleDetect/Dockerfile`).
*   Build and push module images to a container registry (like Azure Container Registry).
*   Configure the `deployment.template.json` or `deployment.debug.template.json` in the `edge/EdgeSolution/` directory to specify module images, routes, and desired properties.
*   Deploy the configuration to your IoT Edge device.

## Running Tests

To ensure the project is functioning correctly, run the following commands:

```bash
# Example for Python projects (e.g., using pytest)
pytest

# Example for Node.js projects (e.g., using Jest)
npm test
```

[Mention any specific test environments or configurations if needed.]

The primary unit tests for the backend webhook processing logic are located in `tests/test_webhook.py`. These tests cover various scenarios to ensure the reliability of the data ingestion and processing pipeline, including:
*   Selecting the license plate with the highest confidence score when multiple are detected.
*   Handling cases where no license plates are detected (empty results).
*   Processing results that may contain null or missing values for certain fields.

To run these tests, navigate to the root of the repository and execute the following command:

```bash
python -m unittest tests/test_webhook.py
```

[Further details on setting up test environments or other test suites can be added here as the project evolves.]

## Contributing

We welcome contributions to enhance the functionality and reliability of this intelligent parking management system. Please follow these guidelines to contribute:

1.  **Fork the Repository:**
    Start by forking the main repository to your own GitHub account.

2.  **Create a New Branch:**
    Before making any changes, create a new branch from the `main` branch (or `develop` if it's actively used as the primary development branch - please check existing branches or ask a maintainer if unsure). Name your branch descriptively, for example:
    *   For new features: `git checkout -b feature/brief-feature-description`
    *   For bug fixes: `git checkout -b bugfix/issue-number-or-description` (e.g., `bugfix/issue-123-fix-lpr-accuracy`)

3.  **Make Your Changes:**
    Implement your feature or bug fix. Ensure your code is clear, well-commented where necessary, and follows the existing coding style of the project.

4.  **Test Your Changes:**
    *   Ensure your changes are well-tested.
    *   If you are adding new functionality, please include new unit tests or integration tests that cover your changes.
    *   Run existing tests (e.g., `python -m unittest tests/test_webhook.py`) to ensure your changes haven't introduced regressions.

5.  **Commit Your Changes:**
    Commit your changes with a clear and descriptive commit message. A good commit message explains the "what" and "why" of your changes. If your changes address a specific issue, reference it in the commit message (e.g., `Fixes #123: Improved LPR accuracy for skewed plates`).
    ```bash
    git add .
    git commit -m "feat: Add support for new camera type X" 
    # or "fix: Resolve issue with bay status not updating"
    ```
    (Consider using a convention like Conventional Commits if the project adopts one.)

6.  **Push to Your Fork:**
    Push your changes to your forked repository:
    ```bash
    git push origin feature/your-feature-name
    ```

7.  **Create a Pull Request (PR):**
    *   Navigate to the original project repository on GitHub.
    *   You should see a prompt to create a Pull Request from your recently pushed branch.
    *   Create a Pull Request targeting the `main` branch of the original repository (or `develop` if that is the designated integration branch).
    *   **Ensure your PR description clearly explains the changes you've made, the problem they solve (if a bug fix), and their purpose.** Include any relevant information that would help reviewers understand and test your contribution. Link to any relevant issues.

8.  **Code Review:**
    *   Your PR will be reviewed by maintainers. Be prepared to discuss your changes and make adjustments based on feedback.
    *   Once approved, your changes will be merged.

[Mention any specific coding standards, commit message formats (e.g., Conventional Commits), or other contribution requirements if they exist. For example, "Please ensure your code passes linting checks."]

---

Thank you for your interest in contributing to this project!
