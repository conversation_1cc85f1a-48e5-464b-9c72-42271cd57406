import asyncio
import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call
from modules.webhook import process_webhook_data

class TestWebhookProcessing(unittest.TestCase):

    @patch('modules.webhook.mqtt_client.publish', new_callable=MagicMock)
    @patch('modules.webhook.firebase_handler.upload_to_firebase_storage', new_callable=MagicMock, return_value="http://mockurl/image.jpg")
    @patch('modules.webhook.crop_detected_objects', new_callable=MagicMock, return_value=(b"cropped_plate_bytes", b"cropped_vehicle_bytes"))
    @patch('modules.webhook.update_bay', new_callable=AsyncMock)
    @patch('modules.webhook.requests.get')
    @patch('modules.webhook.logger', new_callable=MagicMock)
    def test_selects_highest_scoring_plate(self, mock_logger, mock_requests_get, mock_update_bay, 
                                            mock_crop_detected_objects, mock_upload_to_firebase, mock_mqtt_publish):
        # --- Mock API Response for camera details ---
        mock_api_response = MagicMock()
        mock_api_response.json.return_value = {
            'camera_ip': '*******',
            'bays': [{'id': 'bay1', 'ptz_id': 'preset1', 'status': 'vacant', 'plate': None}] 
        }
        mock_api_response.status_code = 200
        def side_effect_requests_get(url, timeout=None, auth=None):
            if f"/cameras/testcam" in url: # Loosened to match any part of testcam
                return mock_api_response
            # Mock response for guard tour stopping if needed, though not essential for this test's core logic
            elif "/axis-cgi/param.cgi" in url:
                mock_guard_response = MagicMock()
                mock_guard_response.status_code = 200 # or 404 if tour not found
                return mock_guard_response
            return MagicMock(status_code=404) # Default for other unexpected calls
        mock_requests_get.side_effect = side_effect_requests_get

        # --- Mock Data Construction ---
        mock_data_input = { # This is the 'data' part of the full webhook payload
            'data': {
                'camera_id': 'testcam_preset1', # serial_ptzid
                'results': [
                    {'plate': 'LOW123', 'score': 0.80, 'region': {'code': 'us-ca', 'score': 0.7}, 'vehicle': {'score': 0.9, 'type': 'Sedan', 'box': {'xmin':0,'ymin':0,'xmax':1,'ymax':1}}},
                    {'plate': 'HIGH789', 'score': 0.95, 'region': {'code': 'us-nv', 'score': 0.8}, 'vehicle': {'score': 0.92, 'type': 'SUV', 'box': {'xmin':0,'ymin':0,'xmax':1,'ymax':1}}}, # Highest score
                    {'plate': 'MID456', 'score': 0.85, 'region': {'code': 'us-az', 'score': 0.75}, 'vehicle': {'score': 0.88, 'type': 'Truck', 'box': {'xmin':0,'ymin':0,'xmax':1,'ymax':1}}}
                ],
                'filename': 'test_image.jpg', # Needed for crop_detected_objects if it uses it
                'timestamp': '2023-10-26T12:00:00Z' # Needed if your function uses it
            }
        }
        
        # Expected values based on the highest scoring plate
        expected_plate = 'HIGH789'
        expected_vehicle_score = 0.92
        # expected_region_code = 'US-NV' # The function extracts region_code but doesn't pass it to update_bay directly

        # --- Execute the Function ---
        # process_webhook_data is async, so we need to run it in an event loop
        asyncio.run(process_webhook_data(mock_data_input, image_content=b"fake_image_bytes"))

        # --- Assertions ---
        mock_logger.info.assert_any_call(f"Highest scoring plate: {expected_plate} with score: 0.95")
        mock_logger.info.assert_any_call(f"Vehicle detection score for best plate: {expected_vehicle_score}")
        mock_logger.info.assert_any_call(f"Region code for best plate: US-NV")

        # Assert that crop_detected_objects was called (because a plate was detected)
        mock_crop_detected_objects.assert_called_once_with(b"fake_image_bytes", mock_data_input['data'])

        # Assert firebase uploads were called for plate and vehicle
        self.assertGreaterEqual(mock_upload_to_firebase.call_count, 2) # Called for plate and vehicle
        
        # Check the call for plate image
        # Example: find call for plate image upload
        plate_image_call_args = None
        for call_args in mock_upload_to_firebase.call_args_list:
            if "plate-images/HIGH789_US-NV_" in call_args[0][1]: # Check blob name
                plate_image_call_args = call_args
                break
        self.assertIsNotNone(plate_image_call_args, "Firebase upload for plate image not found or name mismatch")
        self.assertEqual(plate_image_call_args[0][0], b"cropped_plate_bytes") # Check image content

        # Check the call for vehicle image
        vehicle_image_call_args = None
        for call_args in mock_upload_to_firebase.call_args_list:
             if "vehicle-images/HIGH789_US-NV_" in call_args[0][1]: # Check blob name
                vehicle_image_call_args = call_args
                break
        self.assertIsNotNone(vehicle_image_call_args, "Firebase upload for vehicle image not found or name mismatch")
        self.assertEqual(vehicle_image_call_args[0][0], b"fake_image_bytes") # Vehicle image uses original image content


        # Assert that update_bay was called once
        mock_update_bay.assert_called_once()
        
        # Get the arguments with which update_bay was called
        call_args_list = mock_update_bay.call_args_list
        self.assertEqual(len(call_args_list), 1) # Ensure it was called once
        
        # Actual arguments passed to update_bay
        actual_args = call_args_list[0][0] # Positional arguments
        actual_kwargs = call_args_list[0][1] # Keyword arguments

        # Assert specific arguments passed to update_bay
        # args[0] is bay_to_update, args[1] is plate_number, args[2] is vehicle_score
        # These are passed as keyword arguments now in the refactored update_bay call
        self.assertEqual(actual_kwargs.get('plate_number'), expected_plate)
        self.assertEqual(actual_kwargs.get('vehicle_score'), expected_vehicle_score)
        self.assertEqual(actual_kwargs.get('serial'), 'testcam')
        self.assertEqual(actual_kwargs.get('ptz_id'), 'preset1')
        self.assertIsNotNone(actual_kwargs.get('image_url')) # vehicle_image_url
        self.assertIsNotNone(actual_kwargs.get('cropped_url')) # plate_image_url
        self.assertEqual(actual_kwargs.get('image_url'), "http://mockurl/image.jpg") # from mock_upload_to_firebase
        self.assertEqual(actual_kwargs.get('cropped_url'), "http://mockurl/image.jpg")


    @patch('modules.webhook.mqtt_client.publish', new_callable=MagicMock)
    @patch('modules.webhook.firebase_handler.upload_to_firebase_storage', new_callable=MagicMock)
    @patch('modules.webhook.crop_detected_objects', new_callable=MagicMock)
    @patch('modules.webhook.update_bay', new_callable=AsyncMock)
    @patch('modules.webhook.requests.get')
    @patch('modules.webhook.logger', new_callable=MagicMock)
    def test_no_results_found(self, mock_logger, mock_requests_get, mock_update_bay,
                              mock_crop_detected_objects, mock_upload_to_firebase, mock_mqtt_publish):
        # Mock API Response
        mock_api_response = MagicMock()
        mock_api_response.json.return_value = {
            'camera_ip': '*******',
            'bays': [{'id': 'bay1', 'ptz_id': 'preset1', 'status': 'vacant', 'plate': None}]
        }
        mock_api_response.status_code = 200
        mock_requests_get.return_value = mock_api_response # Simplified for this test

        mock_data_input = {
            'data': {
                'camera_id': 'testcam_preset1',
                'results': [] # Empty results
            }
        }

        asyncio.run(process_webhook_data(mock_data_input, image_content=b"fake_image_bytes"))

        mock_logger.info.assert_any_call("No 'results' array found or array is empty in webhook data for this event.")
        mock_update_bay.assert_called_once() # update_bay is still called to set bay to vacant if it was occupied
        
        call_args_list = mock_update_bay.call_args_list
        actual_kwargs = call_args_list[0][1]

        self.assertIsNone(actual_kwargs.get('plate_number'))
        self.assertEqual(actual_kwargs.get('vehicle_score'), 0.0) # Default vehicle score
        mock_crop_detected_objects.assert_not_called()
        mock_upload_to_firebase.assert_not_called()


    @patch('modules.webhook.mqtt_client.publish', new_callable=MagicMock)
    @patch('modules.webhook.firebase_handler.upload_to_firebase_storage', new_callable=MagicMock)
    @patch('modules.webhook.crop_detected_objects', new_callable=MagicMock)
    @patch('modules.webhook.update_bay', new_callable=AsyncMock)
    @patch('modules.webhook.requests.get')
    @patch('modules.webhook.logger', new_callable=MagicMock)
    def test_plate_with_null_score(self, mock_logger, mock_requests_get, mock_update_bay,
                                   mock_crop_detected_objects, mock_upload_to_firebase, mock_mqtt_publish):
        mock_api_response = MagicMock()
        mock_api_response.json.return_value = {
            'camera_ip': '*******',
            'bays': [{'id': 'bay1', 'ptz_id': 'preset1', 'status': 'vacant', 'plate': None}]
        }
        mock_api_response.status_code = 200
        mock_requests_get.return_value = mock_api_response

        mock_data_input = {
            'data': {
                'camera_id': 'testcam_preset1',
                'results': [
                    {'plate': 'GOODPLATE', 'score': 0.90, 'region': {'code': 'us-ca'}, 'vehicle': {'score': 0.9}},
                    {'plate': 'BADSCORE', 'score': None, 'region': {'code': 'us-nv'}, 'vehicle': {'score': 0.8}} 
                ]
            }
        }

        asyncio.run(process_webhook_data(mock_data_input, image_content=b"fake_image_bytes"))

        mock_logger.warning.assert_any_call("Invalid or missing score 'None' for plate 'BADSCORE'. Skipping.")
        mock_update_bay.assert_called_once()
        actual_kwargs = mock_update_bay.call_args[1]
        self.assertEqual(actual_kwargs.get('plate_number'), 'GOODPLATE')
        self.assertEqual(actual_kwargs.get('vehicle_score'), 0.9)


    @patch('modules.webhook.mqtt_client.publish', new_callable=MagicMock)
    @patch('modules.webhook.firebase_handler.upload_to_firebase_storage', new_callable=MagicMock)
    @patch('modules.webhook.crop_detected_objects', new_callable=MagicMock)
    @patch('modules.webhook.update_bay', new_callable=AsyncMock)
    @patch('modules.webhook.requests.get')
    @patch('modules.webhook.logger', new_callable=MagicMock)
    def test_plate_with_null_plate_value(self, mock_logger, mock_requests_get, mock_update_bay,
                                         mock_crop_detected_objects, mock_upload_to_firebase, mock_mqtt_publish):
        mock_api_response = MagicMock()
        mock_api_response.json.return_value = {
            'camera_ip': '*******',
            'bays': [{'id': 'bay1', 'ptz_id': 'preset1', 'status': 'vacant', 'plate': None}]
        }
        mock_api_response.status_code = 200
        mock_requests_get.return_value = mock_api_response

        mock_data_input = {
            'data': {
                'camera_id': 'testcam_preset1',
                'results': [
                    {'plate': 'GOODPLATE', 'score': 0.90, 'region': {'code': 'us-ca'}, 'vehicle': {'score': 0.9}},
                    {'plate': None, 'score': 0.99, 'region': {'code': 'us-nv'}, 'vehicle': {'score': 0.8}}
                ]
            }
        }

        asyncio.run(process_webhook_data(mock_data_input, image_content=b"fake_image_bytes"))

        mock_logger.warning.assert_any_call("Skipping item with null plate and score 0.99.")
        mock_update_bay.assert_called_once()
        actual_kwargs = mock_update_bay.call_args[1]
        self.assertEqual(actual_kwargs.get('plate_number'), 'GOODPLATE')


if __name__ == '__main__':
    unittest.main()
