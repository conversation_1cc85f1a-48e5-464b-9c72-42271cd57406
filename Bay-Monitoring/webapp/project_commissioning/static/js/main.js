// Toggle bay details visibility
function toggleBays(serial) {
    const baysDiv = document.getElementById('bays-' + serial);
    const toggleText = document.getElementById('toggle-text-' + serial);
    if (baysDiv.style.display === 'none') {
        baysDiv.style.display = 'block';
        toggleText.textContent = 'Hide';
    } else {
        baysDiv.style.display = 'none';
        toggleText.textContent = 'Show';
    }
}

// Reset events counter for a camera
function resetEvents(serial) {
    if (confirm('Are you sure you want to reset the events counter for this camera?')) {
        fetch(`/camera/${serial}/events/reset`, {
            method: 'POST',
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Failed to reset events counter');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to reset events counter');
        });
    }
}

// Variables for tenant search debouncing
let tenantSearchTimeout = null;
const TENANT_SEARCH_DELAY = 300; // milliseconds to wait before searching

// Function to perform partial tenant/name search as user types
function handleTenantSearchInput() {
    const searchInput = document.getElementById('tenantSearch');
    if (!searchInput) return; // Exit if element doesn't exist
    
    const query = searchInput.value.trim();
    
    // Clear any existing timeout to prevent multiple rapid requests
    if (tenantSearchTimeout) {
        clearTimeout(tenantSearchTimeout);
    }
    
    const dropdown = document.getElementById('tenantSearchDropdown');
    if (!dropdown) return; // Exit if element doesn't exist
    
    const loadingIndicator = document.getElementById('tenantDropdownLoading');
    const dropdownContent = document.getElementById('tenantDropdownContent');
    
    // Make sure all elements exist before proceeding
    if (!loadingIndicator || !dropdownContent) return;
    
    // Check query length based on type
    const isLikelyTenantId = /^[A-Za-z0-9_]+$/.test(query) && query.includes('_');
    const minChars = isLikelyTenantId ? 5 : 3; // 5 chars for tenant IDs, 3 for names
    
    // Hide dropdown if query is too short
    if (query.length < minChars) {
        dropdown.style.display = 'none';
        return;
    }
    
    // Show dropdown with loading indicator
    dropdown.style.display = 'block';
    loadingIndicator.style.display = 'block';
    dropdownContent.style.display = 'none';
    
    // Set timeout for search to avoid making API calls on every keystroke
    tenantSearchTimeout = setTimeout(() => {
        fetch(`/api/tenants/partial_search?query=${encodeURIComponent(query)}`)
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Search failed');
                    });
                }
            })
            .then(data => {
                // Make sure elements still exist (page could have changed)
                if (!loadingIndicator || !dropdownContent) return;
                
                // Hide loading indicator
                loadingIndicator.style.display = 'none';
                dropdownContent.style.display = 'block';
                
                // Process search results
                if (data.matches && data.matches.length > 0) {
                    let html = '';
                    
                    // Create HTML for each match
                    data.matches.forEach(match => {
                        const displayValue = match.name || match.tenant_id;
                        const highlightedDisplay = highlightTenantMatch(displayValue, query);
                        
                        html += `
                        <div class="search-result-item" onclick="selectTenant('${match.tenant_id}')">
                            <div class="search-result-tenant">${highlightedDisplay}</div>
                            <div class="search-result-details">
                                Bay: ${match.bay_id || 'None'} | Status: ${match.status || 'Unknown'}
                            </div>
                            <div class="search-result-details">
                                Plate: ${match.plate || 'None'} | Last Seen: ${match.last_seen || 'N/A'}
                            </div>
                        </div>`;
                    });
                    
                    // Show additional count if there are more results
                    if (data.total_count > data.matches.length) {
                        html += `
                        <div class="search-no-results">
                            And ${data.total_count - data.matches.length} more results. Type more characters to refine search.
                        </div>`;
                    }
                    
                    dropdownContent.innerHTML = html;
                } else {
                    // Show no results message
                    dropdownContent.innerHTML = `
                    <div class="search-no-results">
                        No matches found for "${query}"
                    </div>`;
                }
            })
            .catch(error => {
                console.error('Tenant search error:', error);
                
                // Make sure elements still exist
                if (!loadingIndicator || !dropdownContent) return;
                
                loadingIndicator.style.display = 'none';
                dropdownContent.style.display = 'block';
                dropdownContent.innerHTML = `
                <div class="search-no-results">
                    Error: ${error.message}
                </div>`;
            });
    }, TENANT_SEARCH_DELAY);
}

// Function to highlight the matching part of the tenant name or ID
function highlightTenantMatch(text, query) {
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();
    const index = lowerText.indexOf(lowerQuery);
    
    if (index === -1) return text;
    
    return text.substring(0, index) + 
           '<span class="text-primary">' + 
           text.substring(index, index + query.length) + 
           '</span>' + 
           text.substring(index + query.length);
}

// Function to select a tenant from the dropdown
function selectTenant(tenantId) {
    document.getElementById('tenantSearch').value = tenantId;
    document.getElementById('tenantSearchDropdown').style.display = 'none';
    
    // Trigger full search immediately when a tenant is selected
    searchTenant(tenantId);
}

// Search for a tenant by ID or name
function searchTenant(tenantIdOrName) {
    // If no tenantId is provided, get it from the input field
    const tenant = tenantIdOrName || document.getElementById('tenantSearch').value.trim();
    
    if (!tenant) {
        alert('Please enter a tenant name or ID');
        return;
    }

    // Helper function to safely handle element operations
    function safelySetDisplay(elementId, displayValue) {
        const element = document.getElementById(elementId);
        if (element) element.style.display = displayValue;
    }

    function safelySetTextContent(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) element.textContent = text;
    }

    // Reset modal state
    safelySetDisplay('tenantSearchLoading', 'block');
    safelySetDisplay('tenantSearchResults', 'none');
    safelySetDisplay('tenantSearchSuccess', 'none');
    safelySetDisplay('tenantSearchError', 'none');

    // Show modal
    console.log(document.getElementById('tenantSearchModal'));
    const modalElement = document.getElementById('tenantSearchModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }

    // Make API request
    fetch(`/api/tenants/search?query=${encodeURIComponent(tenant)}`)
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                return response.json().then(data => {
                    throw new Error(data.detail || 'Tenant not found');
                });
            }
        })
        .then(data => {
            // Hide loading state, show results
            safelySetDisplay('tenantSearchLoading', 'none');
            safelySetDisplay('tenantSearchResults', 'block');
            safelySetDisplay('tenantSearchSuccess', 'block');
            
            // Update results
            safelySetTextContent('tenantName', data.name || data.tenant_id);
            safelySetTextContent('resultName', data.name || 'N/A');
            safelySetTextContent('resultTenantID2', data.tenant_id || 'N/A');
            safelySetTextContent('resultBayID2', data.bay_id || 'N/A');
            safelySetTextContent('resultPlate', data.plate || 'None');
            safelySetTextContent('resultStatus2', data.status || 'Unknown');
            
            // Format timestamp if exists
            if (data.time_parked) {
                try {
                    const date = new Date(data.time_parked);
                    // Check if date is valid
                    if (!isNaN(date.getTime())) {
                        // Format date as DD/MM/YYYY, HH:MM:SS to match the design
                        const day = date.getDate().toString().padStart(2, '0');
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear();
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        const seconds = date.getSeconds().toString().padStart(2, '0');
                        
                        const formattedDate = `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`;
                        safelySetTextContent('resultTimeParked2', formattedDate);
                    } else {
                        safelySetTextContent('resultTimeParked2', data.time_parked);
                    }
                } catch (e) {
                    console.error("Error parsing date:", e);
                    safelySetTextContent('resultTimeParked2', data.time_parked);
                }
            } else {
                safelySetTextContent('resultTimeParked2', 'Not parked');
            }
            
            // Convert base64 to image source if available (might be bay image)
            const imageElement = document.getElementById('tenantImage');
            if (imageElement && data.image) {
                const imageData = `data:image/jpeg;base64,${data.image}`;
                imageElement.src = imageData;
            } else if (imageElement && data.bay_id) {
                // If no image but we have bay_id, try to fetch bay image
                fetchBayImageForTenant(data.bay_id, imageElement);
            } else if (imageElement) {
                // No image and no bay_id
                imageElement.src = '/static/img/no-image.jpg'; // Fallback image
            }
            
            // Show error message if provided by API but still show the info
            if (data.error) {
                const successElement = document.getElementById('tenantSearchSuccess');
                if (successElement) {
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-warning mt-3';
                    errorAlert.textContent = data.error;
                    successElement.appendChild(errorAlert);
                }
            }
        })
        .catch(error => {
            // Hide loading state, show error
            safelySetDisplay('tenantSearchLoading', 'none');
            safelySetDisplay('tenantSearchResults', 'block');
            safelySetDisplay('tenantSearchError', 'block');
            safelySetTextContent('tenantErrorMessage', error.message);
        });
}

// Function to fetch bay image for a tenant
function fetchBayImageForTenant(bayId, imageElement) {
    fetch(`/api/bays/search?bay_id=${encodeURIComponent(bayId)}`)
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to fetch bay image');
            }
        })
        .then(data => {
            if (data.image) {
                const imageData = `data:image/jpeg;base64,${data.image}`;
                imageElement.src = imageData;
            } else {
                imageElement.src = '/static/img/no-image.jpg'; // Fallback image
            }
        })
        .catch(error => {
            console.error('Error fetching bay image:', error);
            imageElement.src = '/static/img/no-image.jpg'; // Fallback image
        });
}

// Function to export tenant details to PDF
function exportTenantToPdf() {
    console.log("Export Tenant PDF button clicked"); // Debug log
    
    // Check if jsPDF is already defined in window scope
    if (typeof window.jspdf === 'undefined' && typeof jsPDF === 'undefined') {
        console.log("jsPDF not found, loading library..."); // Debug log
        
        // Show loading message
        alert("Preparing PDF export. This may take a moment...");
        
        // If jsPDF is not loaded, load it dynamically
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = function() {
            console.log("jsPDF loaded successfully"); // Debug log
            
            // Also load html2canvas for image capture
            const canvasScript = document.createElement('script');
            canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            canvasScript.onload = function() {
                console.log("html2canvas loaded successfully"); // Debug log
                // Once both scripts are loaded, proceed with PDF generation
                generateTenantPdf();
            };
            canvasScript.onerror = function() {
                console.error("Failed to load html2canvas"); // Debug error
                alert("Failed to load required library (html2canvas). Please check your internet connection and try again.");
            };
            document.head.appendChild(canvasScript);
        };
        script.onerror = function() {
            console.error("Failed to load jsPDF"); // Debug error
            alert("Failed to load required library (jsPDF). Please check your internet connection and try again.");
        };
        document.head.appendChild(script);
    } else {
        console.log("jsPDF already loaded, generating PDF..."); // Debug log
        // If jsPDF is already loaded, proceed directly
        generateTenantPdf();
    }
}

function generateTenantPdf() {
    try {
        console.log("Starting Tenant PDF generation"); // Debug log
        
        // Get tenant details
        const tenantName = document.getElementById('resultName')?.textContent || 'Unknown';
        const tenantId = document.getElementById('resultTenantID2')?.textContent || 'Unknown';
        const bayId = document.getElementById('resultBayID2')?.textContent || 'Unknown';
        const plate = document.getElementById('resultPlate')?.textContent || 'Unknown';
        const status = document.getElementById('resultStatus2')?.textContent || 'Unknown';
        const timeParked = document.getElementById('resultTimeParked2')?.textContent || 'Unknown';
        
        console.log("Tenant details retrieved:", { tenantName, tenantId, bayId, status, timeParked }); // Debug log
        
        // Get tenant image
        const tenantImage = document.getElementById('tenantImage');
        
        // Create the PDF - check which version of jsPDF is available
        let doc;
        if (typeof jsPDF !== 'undefined') {
            console.log("Using global jsPDF constructor"); // Debug log
            doc = new jsPDF();
        } else if (typeof window.jspdf !== 'undefined') {
            console.log("Using window.jspdf.jsPDF constructor"); // Debug log
            doc = new window.jspdf.jsPDF();
        } else {
            throw new Error("jsPDF library not properly loaded");
        }
        
        // PDF layout
        doc.setFontSize(20);
        doc.text(`Tenant Report - ${tenantName}`, 105, 20, { align: 'center' });
        console.log("Added title to PDF"); // Debug log
        
        // Check if image is available and valid
        if (tenantImage && tenantImage.complete && tenantImage.naturalHeight !== 0 && typeof html2canvas !== 'undefined') {
            console.log("Image found, attempting to capture with html2canvas"); // Debug log
            
            // Convert the image to a canvas - use a promise for better error handling
            html2canvas(tenantImage).then(canvas => {
                console.log("Image captured with html2canvas"); // Debug log
                
                try {
                    // Add the canvas image to the PDF
                    const imgData = canvas.toDataURL('image/jpeg');
                    const imgWidth = 180;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    doc.addImage(imgData, 'JPEG', 15, 30, imgWidth, imgHeight);
                    console.log("Image added to PDF"); // Debug log
                    
                    // Add details below the image
                    const yStart = imgHeight + 40;
                    finalizeTenantPdf(doc, tenantName, tenantId, bayId, plate, status, timeParked, yStart);
                } catch (err) {
                    console.error("Error adding image to PDF:", err); // Debug error
                    // Fall back to text-only PDF
                    finalizeTenantPdf(doc, tenantName, tenantId, bayId, plate, status, timeParked, 40);
                }
            }).catch(err => {
                console.error("html2canvas error:", err); // Debug error
                // Fall back to text-only PDF
                finalizeTenantPdf(doc, tenantName, tenantId, bayId, plate, status, timeParked, 40);
            });
        } else {
            console.log("No valid image found or html2canvas not available, creating text-only PDF"); // Debug log
            // No image available or html2canvas not loaded, proceed with text-only PDF
            finalizeTenantPdf(doc, tenantName, tenantId, bayId, plate, status, timeParked, 40);
        }
    } catch (error) {
        console.error("Error in generateTenantPdf function:", error); // Debug error
        alert(`Failed to generate PDF: ${error.message}`);
    }
}

function finalizeTenantPdf(doc, tenantName, tenantId, bayId, plate, status, timeParked, yStart) {
    try {
        console.log("Finalizing Tenant PDF with text content"); // Debug log
        
        // Create a table-like layout for the information
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text("Tenant Information", 15, yStart);
        
        doc.setFont(undefined, 'normal');
        doc.text(`Name: ${tenantName}`, 15, yStart + 10);
        doc.text(`Tenant ID: ${tenantId}`, 105, yStart + 10);
        
        doc.text(`Bay ID: ${bayId}`, 15, yStart + 20);
        doc.text(`License Plate: ${plate}`, 105, yStart + 20);
        
        doc.text(`Status: ${status}`, 15, yStart + 30);
        doc.text(`Time Parked: ${timeParked}`, 105, yStart + 30);
        
        // Add timestamp for the report
        const reportDate = new Date();
        doc.setFontSize(10);
        doc.text(`Report generated: ${reportDate.toLocaleString()}`, 15, yStart + 45);
        
        // Generate sanitized filename
        // Remove any invalid filename characters
        const sanitizedTenantName = tenantName.replace(/[/\\?%*:|"<>]/g, '_');
        const sanitizedTenantId = tenantId.replace(/[/\\?%*:|"<>]/g, '_');
        const filename = `Tenant_${sanitizedTenantId}_${bayId}.pdf`;
        
        console.log("Saving Tenant PDF with filename:", filename); // Debug log
        
        // Save the PDF
        doc.save(filename);
        
        // Show success message
        console.log("Tenant PDF saved successfully"); // Debug log
        const successElement = document.getElementById('tenantSearchSuccess');
        if (successElement) {
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-info mt-3';
            successAlert.textContent = `PDF exported successfully as ${filename}`;
            successElement.appendChild(successAlert);
            
            // Remove the message after 5 seconds
            setTimeout(() => {
                if (successAlert.parentNode === successElement) {
                    successElement.removeChild(successAlert);
                }
            }, 5000);
        }
    } catch (error) {
        console.error("Error in finalizeTenantPdf function:", error); // Debug error
        alert(`Failed to save PDF: ${error.message}`);
    }
}

// Add event listeners for tenant search
document.addEventListener('DOMContentLoaded', function() {
    // Set up the tenant search input event
    const tenantSearchInput = document.getElementById('tenantSearch');
    if (tenantSearchInput) {
        // Set up the input event for partial search
        tenantSearchInput.addEventListener('input', handleTenantSearchInput);
        
        // Add event listener for Enter key
        tenantSearchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                const dropdown = document.getElementById('tenantSearchDropdown');
                if (dropdown) {
                    dropdown.style.display = 'none';
                }
                searchTenant();
            }
        });
        
        // Prevent Enter key from submitting forms unexpectedly
        tenantSearchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
            }
        });
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const searchInput = document.getElementById('tenantSearch');
        const dropdown = document.getElementById('tenantSearchDropdown');
        
        if (searchInput && dropdown && event.target !== searchInput && !dropdown.contains(event.target)) {
            dropdown.style.display = 'none';
        }
    });
    
    // Add event listener for the modal being fully shown
    const tenantSearchModal = document.getElementById('tenantSearchModal');
    if (tenantSearchModal) {
        tenantSearchModal.addEventListener('shown.bs.modal', function() {
            // Re-attach the event listener for the export button
            const exportTenantPdfBtn = document.getElementById('exportTenantPdfBtn');
            if (exportTenantPdfBtn) {
                // Remove any existing listeners to prevent duplicates
                exportTenantPdfBtn.removeEventListener('click', exportTenantToPdf);
                // Add the listener
                exportTenantPdfBtn.addEventListener('click', exportTenantToPdf);
                console.log("Export Tenant PDF button listener attached"); // Debug log
            } else {
                console.warn("Export Tenant PDF button not found in the DOM"); // Debug warning
            }
        });
    }
});

// Search for a license plate
function searchPlate() {
    const plateNumber = document.getElementById('plateSearch').value.trim();
    
    if (!plateNumber) {
        alert('Please enter a license plate number');
        return;
    }

    // Reset modal state
    const plateSearchLoading = document.getElementById('plateSearchLoading');
    if (plateSearchLoading) {
        plateSearchLoading.style.display = 'block';
    }
    
    const plateSearchResults = document.getElementById('plateSearchResults');
    if (plateSearchResults) {
        plateSearchResults.style.display = 'none';
    }
    
    const plateSearchSuccess = document.getElementById('plateSearchSuccess');
    if (plateSearchSuccess) {
        plateSearchSuccess.style.display = 'none';
    }
    
    const plateSearchError = document.getElementById('plateSearchError');
    if (plateSearchError) {
        plateSearchError.style.display = 'none';
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('plateSearchModal'));
    modal.show();

    // Build query parameters
    let queryParams = `plate=${encodeURIComponent(plateNumber)}`;

    // Make API request
    fetch(`/plates/search?${queryParams}`)
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                return response.json().then(data => {
                    throw new Error(data.detail || 'Plate not found');
                });
            }
        })
        .then(data => {
            // Hide loading state, show results
            const plateSearchLoading = document.getElementById('plateSearchLoading');
            if (plateSearchLoading) {
                plateSearchLoading.style.display = 'none';
            }
            
            const plateSearchResults = document.getElementById('plateSearchResults');
            if (plateSearchResults) {
                plateSearchResults.style.display = 'block';
            }
            
            const plateSearchSuccess = document.getElementById('plateSearchSuccess');
            if (plateSearchSuccess) {
                plateSearchSuccess.style.display = 'block';
            }
            
            // Update results
            // First check if elements exist before setting their content
            const plateBayNumber = document.getElementById('plateBayNumber');
            if (plateBayNumber) {
                plateBayNumber.textContent = data.bay_id;
            }
            
            const plateNumber = document.getElementById('plateNumber');
            if (plateNumber) {
                plateNumber.textContent = data.plate_number || plateNumber;
            }
            
            const resultTenantID = document.getElementById('resultTenantID');
            if (resultTenantID) {
                resultTenantID.textContent = data.tenant_id || 'N/A';
            }
            
            const resultCardholderName = document.getElementById('resultCardholderName');
            if (resultCardholderName) {
                resultCardholderName.textContent = data.cardholder_name || 'N/A';
            }
            
            const resultBayID = document.getElementById('resultBayID');
            if (resultBayID) {
                resultBayID.textContent = data.bay_id || 'N/A';
            }
            
            const resultStatus = document.getElementById('resultStatus');
            if (resultStatus) {
                resultStatus.textContent = data.status || 'Occupied';
            }
            
            const resultTimeParked = document.getElementById('resultTimeParked');
            if (resultTimeParked) {
                console.log("DEBUG - time_parked from API:", data.time_parked);
                
                // First try to display the time_parked from the plate search API
                if (data.time_parked) {
                    try {
                        // Try to parse the date
                        const date = new Date(data.time_parked);
                        // Check if date is valid
                        if (!isNaN(date.getTime())) {
                            resultTimeParked.textContent = date.toLocaleString();
                        } else {
                            // If date is invalid, just display the raw value
                            resultTimeParked.textContent = data.time_parked;
                        }
                    } catch (e) {
                        console.error("Error parsing date:", e);
                        // If there's an error parsing the date, just display the raw value
                        resultTimeParked.textContent = data.time_parked;
                    }
                } else {
                    resultTimeParked.textContent = 'Loading...';
                    
                    // If time_parked is not available from the plate search API, try to get it from the bay search API
                    if (data.bay_id) {
                        fetch(`/api/bays/search?bay_id=${encodeURIComponent(data.bay_id)}`)
                            .then(response => {
                                if (response.ok) {
                                    return response.json();
                                } else {
                                    throw new Error('Failed to fetch bay details');
                                }
                            })
                            .then(bayData => {
                                console.log("DEBUG - Bay data:", bayData);
                                if (bayData.time_parked) {
                                    try {
                                        // Try to parse the date
                                        const date = new Date(bayData.time_parked);
                                        // Check if date is valid
                                        if (!isNaN(date.getTime())) {
                                            resultTimeParked.textContent = date.toLocaleString();
                                        } else {
                                            // If date is invalid, just display the raw value
                                            resultTimeParked.textContent = bayData.time_parked;
                                        }
                                    } catch (e) {
                                        console.error("Error parsing date from bay data:", e);
                                        // If there's an error parsing the date, just display the raw value
                                        resultTimeParked.textContent = bayData.time_parked;
                                    }
                                } else {
                                    resultTimeParked.textContent = 'Not available';
                                }
                            })
                            .catch(error => {
                                console.error("Error fetching bay details:", error);
                                resultTimeParked.textContent = 'Not available';
                            });
                    } else {
                        resultTimeParked.textContent = 'Not available';
                    }
                }
            }
            
            // Convert base64 to image source
            const imageData = `data:image/jpeg;base64,${data.image}`;
            const plateImage = document.getElementById('plateImage');
            if (plateImage) {
                plateImage.src = imageData;
            }
            
            // Show error message if provided by API but still show the image
            if (data.error) {
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-warning mt-3';
                errorAlert.textContent = data.error;
                const plateSearchSuccess = document.getElementById('plateSearchSuccess');
                if (plateSearchSuccess) {
                    plateSearchSuccess.appendChild(errorAlert);
                }
            }
        })
        .catch(error => {
            // Hide loading state, show error
            const plateSearchLoading = document.getElementById('plateSearchLoading');
            if (plateSearchLoading) {
                plateSearchLoading.style.display = 'none';
            }
            
            const plateSearchResults = document.getElementById('plateSearchResults');
            if (plateSearchResults) {
                plateSearchResults.style.display = 'block';
            }
            
            const plateSearchError = document.getElementById('plateSearchError');
            if (plateSearchError) {
                plateSearchError.style.display = 'block';
            }
            
            const plateErrorMessage = document.getElementById('plateErrorMessage');
            if (plateErrorMessage) {
                plateErrorMessage.textContent = error.message;
            }
        });
}

function fetchBayImage(bayId) {
    // Show loading indicator
    const loadingElement = document.getElementById(`bay-image-loading-${bayId}`);
    const imageElement = document.getElementById(`bay-image-${bayId}`);
    const errorElement = document.getElementById(`bay-image-error-${bayId}`);
    
    loadingElement.style.display = 'block';
    imageElement.style.display = 'none';
    errorElement.style.display = 'none';
    
    // Make API request to get bay image
    fetch(`/api/bays/search?bay_id=${encodeURIComponent(bayId)}`)
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                return response.json().then(data => {
                    throw new Error(data.detail || 'Failed to fetch bay image');
                });
            }
        })
        .then(data => {
            // Hide loading, show image
            loadingElement.style.display = 'none';
            imageElement.style.display = 'block';
            
            // Convert base64 to image source
            const imageData = `data:image/jpeg;base64,${data.image}`;
            imageElement.src = imageData;
            
            // Show error message if provided by API but still show the image
            if (data.error) {
                errorElement.textContent = data.error;
                errorElement.style.display = 'block';
            }
        })
        .catch(error => {
            // Hide loading, show error
            loadingElement.style.display = 'none';
            errorElement.textContent = error.message;
            errorElement.style.display = 'block';
        });
}

// Modify the searchBay function to include null checks
// Search for a bay by ID
// Search for a bay by ID
function searchBay() {
    const bayId = document.getElementById('baySearch').value.trim();
    if (!bayId) {
        alert('Please enter a bay ID');
        return;
    }

    // Helper function to safely handle element operations
    function safelySetDisplay(elementId, displayValue) {
        const element = document.getElementById(elementId);
        if (element) element.style.display = displayValue;
    }

    function safelySetTextContent(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) element.textContent = text;
    }

    // Reset modal state
    safelySetDisplay('baySearchLoading', 'block');
    safelySetDisplay('baySearchResults', 'none');
    safelySetDisplay('baySearchSuccess', 'none');
    safelySetDisplay('baySearchError', 'none');

    // Show modal
    const modalElement = document.getElementById('baySearchModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }

    // Make API request
    fetch(`/api/bays/search?bay_id=${encodeURIComponent(bayId)}`)
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                return response.json().then(data => {
                    throw new Error(data.detail || 'Bay not found');
                });
            }
        })
        .then(data => {
            // Hide loading state, show results
            safelySetDisplay('baySearchLoading', 'none');
            safelySetDisplay('baySearchResults', 'block');
            safelySetDisplay('baySearchSuccess', 'block');
            
            // Update results - duplicate bay number for both displays
            safelySetTextContent('bayNumber', data.bay_id);
            safelySetTextContent('bayNumber2', data.bay_id);
            safelySetTextContent('bayStatus', data.status || 'Unknown');
            safelySetTextContent('bayTenantID', data.tenant_id || 'None');
            safelySetTextContent('bayPlate', data.plate || 'None');
            safelySetTextContent('bayCardholderName', data.cardholder_name || 'None');
            
            // Format timestamp if exists
            if (data.time_parked) {
                try {
                    const date = new Date(data.time_parked);
                    // Check if date is valid
                    if (!isNaN(date.getTime())) {
                        // Format date as DD/MM/YYYY, HH:MM:SS to match the design
                        const day = date.getDate().toString().padStart(2, '0');
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear();
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        const seconds = date.getSeconds().toString().padStart(2, '0');
                        
                        const formattedDate = `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`;
                        safelySetTextContent('bayTimeParked', formattedDate);
                    } else {
                        safelySetTextContent('bayTimeParked', data.time_parked);
                    }
                } catch (e) {
                    console.error("Error parsing date:", e);
                    safelySetTextContent('bayTimeParked', data.time_parked);
                }
            } else {
                safelySetTextContent('bayTimeParked', 'Not parked');
            }
            
            // Convert base64 to image source
            const imageElement = document.getElementById('bayImage');
            if (imageElement && data.image) {
                const imageData = `data:image/jpeg;base64,${data.image}`;
                imageElement.src = imageData;
            }
            
            // Show error message if provided by API but still show the image
            if (data.error) {
                const successElement = document.getElementById('baySearchSuccess');
                if (successElement) {
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-warning mt-3';
                    errorAlert.textContent = data.error;
                    successElement.appendChild(errorAlert);
                }
            }
        })
        .catch(error => {
            // Hide loading state, show error
            safelySetDisplay('baySearchLoading', 'none');
            safelySetDisplay('baySearchResults', 'block');
            safelySetDisplay('baySearchError', 'block');
            safelySetTextContent('bayErrorMessage', error.message);
        });
}

// Function to export bay details to PDF
// Function to export bay details to PDF
function exportBayToPdf() {
    console.log("Export PDF button clicked"); // Debug log
    
    // Check if jsPDF is already defined in window scope
    if (typeof window.jspdf === 'undefined' && typeof jsPDF === 'undefined') {
        console.log("jsPDF not found, loading library..."); // Debug log
        
        // Show loading message
        alert("Preparing PDF export. This may take a moment...");
        
        // If jsPDF is not loaded, load it dynamically
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = function() {
            console.log("jsPDF loaded successfully"); // Debug log
            
            // Also load html2canvas for image capture
            const canvasScript = document.createElement('script');
            canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            canvasScript.onload = function() {
                console.log("html2canvas loaded successfully"); // Debug log
                // Once both scripts are loaded, proceed with PDF generation
                generatePdf();
            };
            canvasScript.onerror = function() {
                console.error("Failed to load html2canvas"); // Debug error
                alert("Failed to load required library (html2canvas). Please check your internet connection and try again.");
            };
            document.head.appendChild(canvasScript);
        };
        script.onerror = function() {
            console.error("Failed to load jsPDF"); // Debug error
            alert("Failed to load required library (jsPDF). Please check your internet connection and try again.");
        };
        document.head.appendChild(script);
    } else {
        console.log("jsPDF already loaded, generating PDF..."); // Debug log
        // If jsPDF is already loaded, proceed directly
        generatePdf();
    }
}

function generatePdf() {
    try {
        console.log("Starting PDF generation"); // Debug log
        
        // Get bay details
        const bayId = document.getElementById('bayNumber2')?.textContent || 'Unknown';
        const tenantId = document.getElementById('bayTenantID')?.textContent || 'Unknown';
        const cardholderName = document.getElementById('bayCardholderName')?.textContent || 'Unknown';
        const status = document.getElementById('bayStatus')?.textContent || 'Unknown';
        const plate = document.getElementById('bayPlate')?.textContent || 'Unknown';
        const timeParked = document.getElementById('bayTimeParked')?.textContent || 'Unknown';
        
        console.log("Bay details retrieved:", { bayId, tenantId, status, plate, timeParked }); // Debug log
        
        // Get bay image
        const bayImage = document.getElementById('bayImage');
        
        // Create the PDF - check which version of jsPDF is available
        let doc;
        if (typeof jsPDF !== 'undefined') {
            console.log("Using global jsPDF constructor"); // Debug log
            doc = new jsPDF();
        } else if (typeof window.jspdf !== 'undefined') {
            console.log("Using window.jspdf.jsPDF constructor"); // Debug log
            doc = new window.jspdf.jsPDF();
        } else {
            throw new Error("jsPDF library not properly loaded");
        }
        
        // PDF layout
        doc.setFontSize(20);
        doc.text(`Bay ${bayId} - Report`, 105, 20, { align: 'center' });
        console.log("Added title to PDF"); // Debug log
        
        // Check if image is available and valid
        if (bayImage && bayImage.complete && bayImage.naturalHeight !== 0 && typeof html2canvas !== 'undefined') {
            console.log("Image found, attempting to capture with html2canvas"); // Debug log
            
            // Convert the image to a canvas - use a promise for better error handling
            html2canvas(bayImage).then(canvas => {
                console.log("Image captured with html2canvas"); // Debug log
                
                try {
                    // Add the canvas image to the PDF
                    const imgData = canvas.toDataURL('image/jpeg');
                    const imgWidth = 180;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    doc.addImage(imgData, 'JPEG', 15, 30, imgWidth, imgHeight);
                    console.log("Image added to PDF"); // Debug log
                    
                    // Add details below the image
                    const yStart = imgHeight + 40;
                    finalizePdf(doc, bayId, tenantId, status, cardholderName, plate, timeParked, yStart);
                } catch (err) {
                    console.error("Error adding image to PDF:", err); // Debug error
                    // Fall back to text-only PDF
                    finalizePdf(doc, bayId, tenantId, status, cardholderName, plate, timeParked, 40);
                }
            }).catch(err => {
                console.error("html2canvas error:", err); // Debug error
                // Fall back to text-only PDF
                finalizePdf(doc, bayId, tenantId, status, cardholderName, plate, timeParked, 40);
            });
        } else {
            console.log("No valid image found or html2canvas not available, creating text-only PDF"); // Debug log
            // No image available or html2canvas not loaded, proceed with text-only PDF
            finalizePdf(doc, bayId, tenantId, status, cardholderName, plate, timeParked, 40);
        }
    } catch (error) {
        console.error("Error in generatePdf function:", error); // Debug error
        alert(`Failed to generate PDF: ${error.message}`);
    }
}

function finalizePdf(doc, bayId, tenantId, status, cardholderName, plate, timeParked, yStart) {
    try {
        console.log("Finalizing PDF with text content"); // Debug log
        
        // Create a table-like layout for the information
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text("Bay Information", 15, yStart);
        
        doc.setFont(undefined, 'normal');
        doc.text(`Bay ID: ${bayId}`, 15, yStart + 10);
        doc.text(`Tenant ID: ${tenantId}`, 105, yStart + 10);
        
        doc.text(`Status: ${status}`, 15, yStart + 20);
        doc.text(`Cardholder Name: ${cardholderName}`, 105, yStart + 20);
        
        doc.text(`License Plate: ${plate}`, 15, yStart + 30);
        doc.text(`Time Parked: ${timeParked}`, 105, yStart + 30);
        
        // Add timestamp for the report
        const reportDate = new Date();
        doc.setFontSize(10);
        doc.text(`Report generated: ${reportDate.toLocaleString()}`, 15, yStart + 45);
        
        // Generate sanitized filename
        // Remove any invalid filename characters
        const sanitizedTenantId = tenantId.replace(/[/\\?%*:|"<>]/g, '_');
        const sanitizedTimeParked = timeParked.replace(/[/\\?%*:|"<>, :]/g, '_');
        const filename = `${sanitizedTenantId}_${bayId}_${sanitizedTimeParked}.pdf`;
        
        console.log("Saving PDF with filename:", filename); // Debug log
        
        // Save the PDF
        doc.save(filename);
        
        // Show success message
        console.log("PDF saved successfully"); // Debug log
        const successElement = document.getElementById('baySearchSuccess');
        if (successElement) {
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-info mt-3';
            successAlert.textContent = `PDF exported successfully as ${filename}`;
            successElement.appendChild(successAlert);
            
            // Remove the message after 5 seconds
            setTimeout(() => {
                if (successAlert.parentNode === successElement) {
                    successElement.removeChild(successAlert);
                }
            }, 5000);
        }
    } catch (error) {
        console.error("Error in finalizePdf function:", error); // Debug error
        alert(`Failed to save PDF: ${error.message}`);
    }
}

// Make sure event listener is properly attached when the modal opens
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for the modal being fully shown
    const baySearchModal = document.getElementById('baySearchModal');
    if (baySearchModal) {
        baySearchModal.addEventListener('shown.bs.modal', function() {
            // Re-attach the event listener for the export button
            const exportPdfBtn = document.getElementById('exportPdfBtn');
            if (exportPdfBtn) {
                // Remove any existing listeners to prevent duplicates
                exportPdfBtn.removeEventListener('click', exportBayToPdf);
                // Add the listener
                exportPdfBtn.addEventListener('click', exportBayToPdf);
                console.log("Export PDF button listener attached"); // Debug log
            } else {
                console.warn("Export PDF button not found in the DOM"); // Debug warning
            }
        });
    }
});

// Helper function to safely set text content
function safelySetTextContent(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    } else {
        console.warn(`Element with ID '${elementId}' not found in the DOM`);
    }
}

function openImageModal(bayId) {
    const imageElement = document.getElementById(`bay-image-${bayId}`);
    const enlargedImage = document.getElementById('enlarged-image');
    
    // Only proceed if the image is actually loaded
    if (imageElement.style.display !== 'none' && imageElement.src) {
        enlargedImage.src = imageElement.src;
        const modal = new bootstrap.Modal(document.getElementById('imageEnlargeModal'));
        document.getElementById('imageEnlargeModalLabel').textContent = `Bay ${bayId} Image`;
        modal.show();
    }
}

// Event listeners for Enter key press
document.addEventListener('DOMContentLoaded', function() {
    // Only add event listeners if elements exist
    const plateSearchInput = document.getElementById('plateSearch');
    if (plateSearchInput) {
        plateSearchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchPlate();
            }
        });
    }

    const baySearchInput = document.getElementById('baySearch');
    if (baySearchInput) {
        baySearchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchBay();
            }
        });
    }
});

// Variables for search debouncing
let searchTimeout = null;
const SEARCH_DELAY = 300; // milliseconds to wait before searching

// Function to perform partial plate search as user types
function handlePlateSearchInput() {
    const searchInput = document.getElementById('plateSearch');
    if (!searchInput) return; // Exit if element doesn't exist
    
    const query = searchInput.value.trim();
    
    // Clear any existing timeout to prevent multiple rapid requests
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    const dropdown = document.getElementById('plateSearchDropdown');
    if (!dropdown) return; // Exit if element doesn't exist
    
    const loadingIndicator = document.getElementById('plateDropdownLoading');
    const dropdownContent = document.getElementById('plateDropdownContent');
    
    // Make sure all elements exist before proceeding
    if (!loadingIndicator || !dropdownContent) return;
    
    // Hide dropdown if query is too short
    if (query.length < 3) {
        dropdown.style.display = 'none';
        return;
    }
    
    // Show dropdown with loading indicator
    dropdown.style.display = 'block';
    loadingIndicator.style.display = 'block';
    dropdownContent.style.display = 'none';
    
    // Set timeout for search to avoid making API calls on every keystroke
    searchTimeout = setTimeout(() => {
        fetch(`http://***********:8000/plates/partial_search?query=${encodeURIComponent(query)}`)
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Search failed');
                    });
                }
            })
            .then(data => {
                // Make sure elements still exist (page could have changed)
                if (!loadingIndicator || !dropdownContent) return;
                
                // Hide loading indicator
                loadingIndicator.style.display = 'none';
                dropdownContent.style.display = 'block';
                
                // Process search results
                if (data.matches && data.matches.length > 0) {
                    let html = '';
                    
                    // Create HTML for each match
                    data.matches.forEach(match => {
                        html += `
                        <div class="search-result-item" onclick="selectPlate('${match.plate}')">
                            <div class="search-result-plate">${highlightMatch(match.plate, query)}</div>
                            <div class="search-result-details">
                                Bay: ${match.bay_id} | Status: ${match.status} | Tenant: ${match.tenant || 'None'}
                            </div>
                            <div class="search-result-details">
                                Tenant ID: ${match.tenant_id || 'None'} | Cardholder: ${match.cardholder_name || 'None'}
                            </div>
                        </div>`;
                    });
                    
                    // Show additional count if there are more results
                    if (data.total_count > data.matches.length) {
                        html += `
                        <div class="search-no-results">
                            And ${data.total_count - data.matches.length} more results. Type more characters to refine search.
                        </div>`;
                    }
                    
                    dropdownContent.innerHTML = html;
                } else {
                    // Show no results message
                    dropdownContent.innerHTML = `
                    <div class="search-no-results">
                        No matches found for "${query}"
                    </div>`;
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                
                // Make sure elements still exist
                if (!loadingIndicator || !dropdownContent) return;
                
                loadingIndicator.style.display = 'none';
                dropdownContent.style.display = 'block';
                dropdownContent.innerHTML = `
                <div class="search-no-results">
                    Error: ${error.message}
                </div>`;
            });
    }, SEARCH_DELAY);
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const searchInput = document.getElementById('plateSearch');
    const dropdown = document.getElementById('plateSearchDropdown');
    
    // Check if both elements exist before checking conditions
    if (searchInput && dropdown && event.target !== searchInput && !dropdown.contains(event.target)) {
        dropdown.style.display = 'none';
    }
});

// Add the event listeners after the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Only set up listeners if elements exist
    const plateSearchInput = document.getElementById('plateSearch');
    const plateSearchDropdown = document.getElementById('plateSearchDropdown');
    
    if (plateSearchInput) {
        // Set up the input event for partial search
        plateSearchInput.addEventListener('input', handlePlateSearchInput);
        
        // Keep the existing event listener for Enter key
        plateSearchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                if (plateSearchDropdown) {
                    plateSearchDropdown.style.display = 'none';
                }
                searchPlate();
            }
        });
        
        // Prevent Enter key from submitting forms unexpectedly
        plateSearchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
            }
        });
    }
});

// Function to highlight the matching part of the plate number
function highlightMatch(text, query) {
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();
    const index = lowerText.indexOf(lowerQuery);
    
    if (index === -1) return text;
    
    return text.substring(0, index) + 
           '<span class="text-primary">' + 
           text.substring(index, index + query.length) + 
           '</span>' + 
           text.substring(index + query.length);
}

// Function to select a plate from the dropdown
function selectPlate(plate) {
    document.getElementById('plateSearch').value = plate;
    document.getElementById('plateSearchDropdown').style.display = 'none';
    
    // Trigger full search immediately when a plate is selected
    searchPlate();
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const searchInput = document.getElementById('plateSearch');
    const dropdown = document.getElementById('plateSearchDropdown');
    
    if (event.target !== searchInput && !dropdown.contains(event.target)) {
        dropdown.style.display = 'none';
    }
});

// Function to export plate details to PDF
function exportPlateToPdf() {
    console.log("Export Plate PDF button clicked"); // Debug log
    
    // Check if jsPDF is already defined in window scope
    if (typeof window.jspdf === 'undefined' && typeof jsPDF === 'undefined') {
        console.log("jsPDF not found, loading library..."); // Debug log
        
        // Show loading message
        alert("Preparing PDF export. This may take a moment...");
        
        // If jsPDF is not loaded, load it dynamically
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = function() {
            console.log("jsPDF loaded successfully"); // Debug log
            
            // Also load html2canvas for image capture
            const canvasScript = document.createElement('script');
            canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            canvasScript.onload = function() {
                console.log("html2canvas loaded successfully"); // Debug log
                // Once both scripts are loaded, proceed with PDF generation
                generatePlatePdf();
            };
            canvasScript.onerror = function() {
                console.error("Failed to load html2canvas"); // Debug error
                alert("Failed to load required library (html2canvas). Please check your internet connection and try again.");
            };
            document.head.appendChild(canvasScript);
        };
        script.onerror = function() {
            console.error("Failed to load jsPDF"); // Debug error
            alert("Failed to load required library (jsPDF). Please check your internet connection and try again.");
        };
        document.head.appendChild(script);
    } else {
        console.log("jsPDF already loaded, generating PDF..."); // Debug log
        // If jsPDF is already loaded, proceed directly
        generatePlatePdf();
    }
}

function generatePlatePdf() {
    try {
        console.log("Starting Plate PDF generation"); // Debug log
        
        // Get plate details
        const plateNumber = document.getElementById('plateNumber')?.textContent || 'Unknown';
        const bayId = document.getElementById('resultBayID')?.textContent || 'Unknown';
        const tenantId = document.getElementById('resultTenantID')?.textContent || 'Unknown';
        const cardholderName = document.getElementById('resultCardholderName')?.textContent || 'Unknown';
        const status = document.getElementById('resultStatus')?.textContent || 'Unknown';
        const timeParked = document.getElementById('resultTimeParked')?.textContent || 'Unknown';
        
        console.log("Plate details retrieved:", { plateNumber, bayId, tenantId, status, timeParked }); // Debug log
        
        // Get plate image
        const plateImage = document.getElementById('plateImage');
        
        // Create the PDF - check which version of jsPDF is available
        let doc;
        if (typeof jsPDF !== 'undefined') {
            console.log("Using global jsPDF constructor"); // Debug log
            doc = new jsPDF();
        } else if (typeof window.jspdf !== 'undefined') {
            console.log("Using window.jspdf.jsPDF constructor"); // Debug log
            doc = new window.jspdf.jsPDF();
        } else {
            throw new Error("jsPDF library not properly loaded");
        }
        
        // PDF layout
        doc.setFontSize(20);
        doc.text(`Vehicle Report - ${plateNumber}`, 105, 20, { align: 'center' });
        console.log("Added title to PDF"); // Debug log
        
        // Check if image is available and valid
        if (plateImage && plateImage.complete && plateImage.naturalHeight !== 0 && typeof html2canvas !== 'undefined') {
            console.log("Image found, attempting to capture with html2canvas"); // Debug log
            
            // Convert the image to a canvas - use a promise for better error handling
            html2canvas(plateImage).then(canvas => {
                console.log("Image captured with html2canvas"); // Debug log
                
                try {
                    // Add the canvas image to the PDF
                    const imgData = canvas.toDataURL('image/jpeg');
                    const imgWidth = 180;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    doc.addImage(imgData, 'JPEG', 15, 30, imgWidth, imgHeight);
                    console.log("Image added to PDF"); // Debug log
                    
                    // Add details below the image
                    const yStart = imgHeight + 40;
                    finalizePlatePdf(doc, plateNumber, bayId, tenantId, status, cardholderName, timeParked, yStart);
                } catch (err) {
                    console.error("Error adding image to PDF:", err); // Debug error
                    // Fall back to text-only PDF
                    finalizePlatePdf(doc, plateNumber, bayId, tenantId, status, cardholderName, timeParked, 40);
                }
            }).catch(err => {
                console.error("html2canvas error:", err); // Debug error
                // Fall back to text-only PDF
                finalizePlatePdf(doc, plateNumber, bayId, tenantId, status, cardholderName, timeParked, 40);
            });
        } else {
            console.log("No valid image found or html2canvas not available, creating text-only PDF"); // Debug log
            // No image available or html2canvas not loaded, proceed with text-only PDF
            finalizePlatePdf(doc, plateNumber, bayId, tenantId, status, cardholderName, timeParked, 40);
        }
    } catch (error) {
        console.error("Error in generatePlatePdf function:", error); // Debug error
        alert(`Failed to generate PDF: ${error.message}`);
    }
}

function finalizePlatePdf(doc, plateNumber, bayId, tenantId, status, cardholderName, timeParked, yStart) {
    try {
        console.log("Finalizing Plate PDF with text content"); // Debug log
        
        // Create a table-like layout for the information
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text("Vehicle Information", 15, yStart);
        
        doc.setFont(undefined, 'normal');
        doc.text(`License Plate: ${plateNumber}`, 15, yStart + 10);
        doc.text(`Bay ID: ${bayId}`, 105, yStart + 10);
        
        doc.text(`Status: ${status}`, 15, yStart + 20);
        doc.text(`Tenant ID: ${tenantId}`, 105, yStart + 20);
        
        doc.text(`Cardholder Name: ${cardholderName}`, 15, yStart + 30);
        doc.text(`Time Parked: ${timeParked}`, 105, yStart + 30);
        
        // Add timestamp for the report
        const reportDate = new Date();
        doc.setFontSize(10);
        doc.text(`Report generated: ${reportDate.toLocaleString()}`, 15, yStart + 45);
        
        // Generate sanitized filename
        // Remove any invalid filename characters
        const sanitizedPlateNumber = plateNumber.replace(/[/\\?%*:|"<>]/g, '_');
        const sanitizedTenantId = tenantId.replace(/[/\\?%*:|"<>]/g, '_');
        const sanitizedTimeParked = timeParked.replace(/[/\\?%*:|"<>, :]/g, '_');
        const filename = `${sanitizedPlateNumber}_${bayId}_${sanitizedTenantId}.pdf`;
        
        console.log("Saving Plate PDF with filename:", filename); // Debug log
        
        // Save the PDF
        doc.save(filename);
        
        // Show success message
        console.log("Plate PDF saved successfully"); // Debug log
        const successElement = document.getElementById('plateSearchSuccess');
        if (successElement) {
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-info mt-3';
            successAlert.textContent = `PDF exported successfully as ${filename}`;
            successElement.appendChild(successAlert);
            
            // Remove the message after 5 seconds
            setTimeout(() => {
                if (successAlert.parentNode === successElement) {
                    successElement.removeChild(successAlert);
                }
            }, 5000);
        }
    } catch (error) {
        console.error("Error in finalizePlatePdf function:", error); // Debug error
        alert(`Failed to save PDF: ${error.message}`);
    }
}

// Add event listener for the plate search modal
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for the modal being fully shown
    const plateSearchModal = document.getElementById('plateSearchModal');
    if (plateSearchModal) {
        plateSearchModal.addEventListener('shown.bs.modal', function() {
            // Re-attach the event listener for the export button
            const exportPlatePdfBtn = document.getElementById('exportPlatePdfBtn');
            if (exportPlatePdfBtn) {
                // Remove any existing listeners to prevent duplicates
                exportPlatePdfBtn.removeEventListener('click', exportPlateToPdf);
                // Add the listener
                exportPlatePdfBtn.addEventListener('click', exportPlateToPdf);
                console.log("Export Plate PDF button listener attached"); // Debug log
            } else {
                console.warn("Export Plate PDF button not found in the DOM"); // Debug warning
            }
        });
    }
});

//document.addEventListener('DOMContentLoaded', function() {
 //   // Make sure this runs after the page is fully loaded
 //   var myModal = new bootstrap.Modal(document.getElementById('tenantSearchModal'), {
 //     backdrop: 'static',
 //     keyboard: false
 //   });
 // });

// Add the event listeners after the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set up the input event for partial search
    document.getElementById('plateSearch').addEventListener('input', handlePlateSearchInput);
    
    // Keep the existing event listener for Enter key
    document.getElementById('plateSearch').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            document.getElementById('plateSearchDropdown').style.display = 'none';
            searchPlate();
        }
    });
    
    // Prevent Enter key from submitting forms unexpectedly
    document.getElementById('plateSearch').addEventListener('keydown', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
        }
    });
});

