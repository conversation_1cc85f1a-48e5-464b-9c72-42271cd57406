:root {
    --background: #0c0a09;
    --foreground: #fafafa;
    --card: #1c1917;
    --card-foreground: #fafafa;
    --primary: #a855f7;
    --primary-foreground: #fafafa;
    --muted: #57534e;
    --muted-foreground: #a8a29e;
    --accent: #292524;
    --accent-foreground: #fafafa;
    --destructive: #ef4444;
    --destructive-foreground: #fafafa;
    --border: #292524;
    --input: #292524;
    --radius: 0.5rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding: 0;
}

.navbar {
    background-color: var(--card);
    border-bottom: 1px solid var(--border);
    padding: 1rem 1.5rem;
}

.sidebar {
    background-color: var(--card);
    border-right: 1px solid var(--border);
    height: 100vh;
    position: fixed;
    width: 280px;
    padding: 1.5rem 1rem;
}

.content {
    margin-left: 280px;
    padding: 2rem;
}

.card {
    background-color: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-header {
    background-color: var(--card);
    border-bottom: 1px solid var(--border);
    padding: 1rem 1.5rem;
}

.input-group {
    border-radius: var(--radius);
    overflow: hidden;
}

.form-control {
    background-color: var(--input);
    border: 1px solid var(--border);
    color: var(--foreground);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    background-color: var(--input);
    border-color: var(--primary);
    box-shadow: none;
    color: var(--foreground);
}

.btn {
    border-radius: var(--radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #9333ea;
    border-color: #9333ea;
}

.btn-outline-secondary {
    border-color: var(--border);
    color: var(--foreground);
}

.btn-outline-secondary:hover {
    background-color: var(--accent);
    border-color: var(--accent);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.nav-pills .nav-link {
    border-radius: var(--radius);
    color: var(--foreground);
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
}

.nav-pills .nav-link:hover {
    background-color: var(--accent);
}

.nav-pills .nav-link.active {
    background-color: var(--primary);
    color: var(--primary-foreground);
}

.table {
    color: var(--foreground);
}

.table th {
    border-bottom-color: var(--border);
    color: var(--muted-foreground);
    font-weight: 500;
    padding: 1rem;
}

.table td {
    border-bottom-color: var(--border);
    padding: 1rem;
}

.plate-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius);
}

.search-modal .modal-content {
    background-color: var(--card);
    border-color: var(--border);
    color: var(--foreground);
}

.search-modal .modal-header,
.search-modal .modal-footer {
    border-color: var(--border);
}

.search-modal .modal-header .btn-close {
    filter: invert(1);
}

.alert {
    border-radius: var(--radius);
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #4ade80;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #f87171;
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
}

.search-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.search-dropdown {
    position: absolute;
    width: calc(100% - 100px); /* Adjust width to match input field (not the button) */
    max-height: 300px;
    overflow-y: auto;
    background-color: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    z-index: 1000;
    margin-top: 2px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    left: 0; /* Align with the left edge of the search card */
    top: 100%; /* Position below the input group */
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border);
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background-color: var(--accent);
}

.search-result-plate {
    font-weight: 600;
}

.search-result-details {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.search-no-results {
    padding: 0.75rem 1rem;
    color: var(--muted-foreground);
    text-align: center;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border);
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background-color: var(--accent);
}

.search-result-plate {
    font-weight: 600;
}

.search-result-details {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.search-no-results {
    padding: 0.75rem 1rem;
    color: var(--muted-foreground);
    text-align: center;
}

.search-card {
    position: relative; /* Add this to make the dropdown position relative to the card */
}

.search-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 999px;
    font-weight: 500;
}

.badge-purple {
    background-color: rgba(168, 85, 247, 0.1);
    color: #c084fc;
}

.badge-blue {
    background-color: rgba(59, 130, 246, 0.1);
    color: #60a5fa;
}

.badge-green {
    background-color: rgba(34, 197, 94, 0.1);
    color: #4ade80;
}

.badge-red {
    background-color: rgba(239, 68, 68, 0.1);
    color: #f87171;
}

.camera-card {
    transition: all 0.2s ease;
}

.camera-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.spinner-border {
    color: var(--primary);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

@media (max-width: 992px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    .content {
        margin-left: 0;
    }
}

.content-full {
    padding: 2rem;
    width: 100%;
    background-color: var(--background);
}

/* Adjust existing styles to work without sidebar */
@media (max-width: 992px) {
    .content-full {
        padding: 1.5rem;
    }
}