import base64
import time
import requests
from requests.auth import H<PERSON><PERSON>igestAuth
from fastapi import Fast<PERSON><PERSON>, HTTPException, Response
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional
from pymongo import MongoClient
from bson import ObjectId
import os

import time
import requests
from requests.auth import HTTPDigestAuth

# Add this near the top of your api.py file, after the other imports
from fastapi.middleware.cors import CORSMiddleware

# Initialize FastAPI app
app = FastAPI()

# Add this right after creating the FastAPI app (app = FastAPI())
# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development - restrict this in production
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Configure MongoDB
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
client = MongoClient(MONGO_URI)
db = client['parking_db']
cameras_collection = db['cameras']
config_collection = db['config']
locks_collection = db["locks"]  # Assuming db is your MongoDB database instance

# --- 1. Clear the Collection on Startup ---
try:
    print(f"Clearing all documents from '{locks_collection.name}'...")
    delete_result = locks_collection.delete_many({})
    print(f"Cleared {delete_result.deleted_count} documents.")
except Exception as e:
    print(f"Error clearing collection: {e}")

# Pydantic models
class BayModel(BaseModel):
    bay_id: str
    tenant: Optional[str] = None
    status: str = 'vacant'  # Default to 'vacant'
    plate: Optional[str] = None
    parked_tenant: Optional[str] = None
    time_parked: Optional[str] = None  # Default to None, ISO format timestamp if occupied
    ptz_id: Optional[str] = None  # PTZ ID for each bay
    type: Optional[str] = "car"  # New field for bay type with default value "car"
    tenantID: Optional[str] = None  # New field for tenant ID
    cardholderName: Optional[str] = None  # New field for cardholder name

class CameraModel(BaseModel):
    serial: str
    camera_ip: str  # IP address of the camera in format ********
    bays: List[BayModel]
    events: Optional[int] = 0  # Counter for events

class ConfigModel(BaseModel):
    key: str
    value: str

def clear_duplicate_plates(plate, current_bay_id):
    """
    Clears the given plate from all bays except the one with current_bay_id.
    Checks across ALL cameras, not just the current camera.
    
    Args:
        plate (str): The license plate to check for duplicates
        current_bay_id (str): The ID of the bay that should keep this plate
        
    Returns:
        int: Number of bays that were cleared
    """
    if not plate or not plate.strip():
        return 0  # Empty plate, nothing to clear
    
    # Make sure current_bay_id is a string for consistent comparison
    current_bay_id = str(current_bay_id)
    
    print(f"Starting duplicate plate clearing for plate '{plate}', preserving bay '{current_bay_id}'")
    
    cleared_count = 0
    # Get all cameras from the database
    all_cameras = cameras_collection.find({})
    
    # Loop through all cameras
    for camera in all_cameras:
        camera_serial = camera.get("serial", "Unknown")
        camera_modified = False
        updated_bays = []
        
        # Process each bay in the camera
        for bay in camera["bays"]:
            bay_id = str(bay.get("bay_id", ""))
            
            # Check if this bay has the plate we're looking for and is NOT the current bay
            if bay.get("plate") == plate and bay_id != current_bay_id:
                print(f"Clearing duplicate plate '{plate}' from camera '{camera_serial}', bay '{bay_id}'")
                
                # Clear the plate and related fields
                bay["plate"] = None
                bay["parked_tenant"] = None
                bay["time_parked"] = None
                bay["status"] = "vacant"
                bay["tenantID"] = None
                bay["cardholderName"] = None
                
                camera_modified = True
                cleared_count += 1
                print(f"Successfully cleared bay '{bay_id}' in camera '{camera_serial}'")
            
            updated_bays.append(bay)
        
        # If we made changes to any bay in this camera, update the entire document
        if camera_modified:
            update_result = cameras_collection.update_one(
                {"serial": camera_serial},
                {"$set": {"bays": updated_bays}}
            )
            
            if update_result.modified_count == 0:
                print(f"Warning: Failed to update camera '{camera_serial}' in database")
    
    print(f"Cleared {cleared_count} bay(s) with duplicate plate '{plate}'")
    return cleared_count

# Additional helper function to handle race conditions
def lock_plate_for_assignment(plate, bay_id):
    """
    Creates a temporary lock when assigning a plate to prevent race conditions
    during nearly simultaneous updates.
    
    Args:
        plate (str): The license plate being assigned
        bay_id (str): The bay ID receiving this plate
        
    Returns:
        bool: True if lock acquired, False if plate is already being assigned elsewhere
    """
    # You could implement this using a separate collection for locks
    # or using MongoDB's findAndModify operation for atomic operations
    
    # Example using a locks collection:
    existing_lock = locks_collection.find_one({"plate": plate, "locked": True})
    if existing_lock and existing_lock["bay_id"] != bay_id:
        return False
        
    # Set or update the lock
    locks_collection.update_one(
        {"plate": plate},
        {"$set": {"bay_id": bay_id, "locked": True, "timestamp": datetime.now()}},
        upsert=True
    )
    
    # Set a TTL index on the locks collection to automatically expire locks
    locks_collection.create_index("timestamp", expireAfterSeconds=60)
    
    return True

# Add this helper function at the top level in api.py to avoid code duplication
def get_camera_image_for_bay(camera_ip, preset_position):
    # Read Axis camera credentials from environment variables (defaults provided)
    axis_user = os.getenv("AXIS_USER", "root")
    axis_password = os.getenv("AXIS_PASSWORD", "M1ecrdry1!")

    # Step 1: Move camera to the preset position (with better error handling)
    ptz_url = f"http://{camera_ip}/axis-cgi/com/ptz.cgi"
    payload = {"gotoserverpresetno": preset_position, "camera": "1"}
    try:
        move_response = requests.post(
            ptz_url, 
            data=payload, 
            auth=HTTPDigestAuth(axis_user, axis_password),
            timeout=5  # Add timeout
        )
        move_response.raise_for_status()  # Raises exception for 4XX/5XX responses
    except requests.exceptions.RequestException as e:
        print(f"Camera movement error: {str(e)}")
        # Return a transparent pixel as placeholder
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=", "Camera unavailable"

    # Wait for camera movement (reduced time)
    time.sleep(3)  # Reduced from 3 seconds

    # Step 2: Request the image from the camera (with better error handling)
    image_url = f"http://{camera_ip}/axis-cgi/jpg/image.cgi?resolution=1920x1080&compression=0"
    try:
        image_response = requests.get(
            image_url, 
            auth=HTTPDigestAuth(axis_user, axis_password),
            timeout=5  # Add timeout
        )
        image_response.raise_for_status()  # Raises exception for 4XX/5XX responses
        
        # Encode the image content as Base64
        encoded_image = base64.b64encode(image_response.content).decode("utf-8")
        return encoded_image, None
        
    except requests.exceptions.RequestException as e:
        print(f"Image retrieval error: {str(e)}")
        # Return a transparent pixel as placeholder
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=", "Camera image unavailable"



# Helper function to convert MongoDB document to JSON
def camera_serializer(camera) -> dict:
    return {
        "id": str(camera["_id"]),
        "serial": camera["serial"],
        "camera_ip": camera["camera_ip"],
        "bays": camera["bays"],
        "events": camera.get("events", 0)  # Default to 0 if not present
    }

# Create a new camera entry
@app.post("/cameras/", response_model=CameraModel)
async def create_camera(camera: CameraModel):
    if cameras_collection.find_one({"serial": camera.serial}):
        raise HTTPException(status_code=400, detail="Camera with this serial already exists")
    
    camera_dict = camera.dict()
    
    # Initialize events counter to 0
    camera_dict['events'] = 0
    
    # Start PTZ IDs from 2 for new bays
    next_ptz = 2
    for bay in camera_dict['bays']:
        bay['status'] = 'vacant'
        bay['plate'] = None
        bay['parked_tenant'] = None
        bay['time_parked'] = None
        if not bay.get('ptz_id'):  # Only assign if PTZ ID is not already set
            bay['ptz_id'] = str(next_ptz)
            next_ptz += 1
    
    camera_dict['camera_ip'] = camera.camera_ip
    cameras_collection.insert_one(camera_dict)
    return camera

# Update a specific value of a camera
@app.patch("/cameras/{serial}")
async def update_camera(serial: str, new_serial: Optional[str] = None, camera_ip: Optional[str] = None, events: Optional[int] = None):
    # First check if the camera exists
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    update_data = {}
    
    # Validate and add new serial if provided
    if new_serial is not None:
        if new_serial != serial:  # Only check if actually changing the serial
            # Check if new serial is already in use
            existing_camera = cameras_collection.find_one({"serial": new_serial})
            if existing_camera:
                raise HTTPException(status_code=400, detail="Camera with this serial already exists")
            update_data["serial"] = new_serial

    # Validate and add new IP if provided
    if camera_ip is not None:
        if camera_ip != camera["camera_ip"]:  # Only check if actually changing the IP
            # Check if new IP is already in use
            existing_camera = cameras_collection.find_one({"camera_ip": camera_ip})
            if existing_camera and existing_camera["serial"] != serial:
                raise HTTPException(status_code=400, detail="Camera with this IP already exists")
            update_data["camera_ip"] = camera_ip
    
    if events is not None:
        if events < 0:
            raise HTTPException(status_code=400, detail="Events count cannot be negative")
        update_data["events"] = events

    result = cameras_collection.update_one({"serial": serial}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=500, detail="Failed to update camera")

    return {"message": "Camera updated successfully", "new_serial": new_serial if new_serial else serial}

# Get details of a camera by serial
@app.get("/cameras/{serial}", response_model=CameraModel)
async def get_camera(serial: str):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Print debug info
    #print(f"Camera data from DB: {camera}")
    serialized = camera_serializer(camera)
    #print(f"Serialized camera data: {serialized}")
    return serialized

@app.patch("/cameras/{serial}/bays")
async def update_specific_bay(
    serial: str, 
    ptz_id: str, 
    tenant: Optional[str] = None, 
    status: Optional[str] = None, 
    plate: Optional[str] = None, 
    parked_tenant: Optional[str] = None, 
    time_parked: Optional[str] = None, 
    bay_id: Optional[str] = None,
    new_ptz_id: Optional[str] = None,
    type: Optional[str] = None,
    tenantID: Optional[str] = None,
    cardholderName: Optional[str] = None
):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    # If updating PTZ ID, check if new PTZ ID is already in use
    if new_ptz_id is not None:
        for bay in camera["bays"]:
            if bay["ptz_id"] == new_ptz_id and bay["ptz_id"] != ptz_id:
                raise HTTPException(status_code=400, detail=f"PTZ ID {new_ptz_id} is already in use")

    # Find the bay being updated and its current bay_id
    current_bay_id = None
    for bay in camera["bays"]:
        if bay["ptz_id"] == ptz_id:
            current_bay_id = bay.get("bay_id")
            break
    
    #Plate lock not running
    #if plate and not lock_plate_for_assignment(plate, current_bay_id):
    #    raise HTTPException(status_code=409, detail="This license plate is currently being assigned to another bay")

    # Check if we're setting status to vacant OR clearing the plate
    # Either of these conditions should trigger clearing related data
    force_clear_plate = (status == "vacant" or plate is not None and plate == None)
    
    # If we're updating the plate, we need special handling:
    if plate is not None:
        if plate:  # If plate has a non-empty value, check for duplicates
            # This will clear any duplicates except in the current bay
            print("checking duplicate plates")
            cleared_count = clear_duplicate_plates(plate, current_bay_id)
            if cleared_count > 0:
                print(f"Cleared {cleared_count} bay(s) with duplicate plate {plate}")
                # IMPORTANT: Reload camera data to get the current state after clearing duplicates
                camera = cameras_collection.find_one({"serial": serial})
    
    updated_bays = []
    bay_found = False
    for bay in camera["bays"]:
        if bay["ptz_id"] == ptz_id:
            bay_found = True
            if tenant is not None:
                bay["tenant"] = tenant
                
            # Update status first to check if we need to clear plate
            if status is not None:
                bay["status"] = status
                
            # Handle plate values - either explicitly set or auto-clear
            if plate is not None:
                bay["plate"] = plate
                # If setting plate to None, also clear related fields
                if plate is None:
                    bay["parked_tenant"] = None
                    bay["time_parked"] = None
                    bay["tenantID"] = None
                    bay["cardholderName"] = None
                    # If not explicitly changing status, also set to vacant
                    if status is None:
                        bay["status"] = "vacant"
                        print(f"Auto-set status to vacant for bay {bay.get('bay_id')} due to plate being cleared")
            elif force_clear_plate:  # Auto-clear plate when setting status to vacant
                bay["plate"] = None
                bay["parked_tenant"] = None
                bay["time_parked"] = None
                bay["tenantID"] = None
                bay["cardholderName"] = None
                print(f"Auto-cleared plate data for bay {bay.get('bay_id')} due to vacant status")
            
            # Handle other field updates
            if parked_tenant is not None:
                bay["parked_tenant"] = parked_tenant
            elif force_clear_plate:  # Also clear parked_tenant if not specified
                bay["parked_tenant"] = None
                
            if time_parked is not None:
                bay["time_parked"] = time_parked
            elif force_clear_plate:  # Also clear time_parked if not specified
                bay["time_parked"] = None
                
            if new_ptz_id is not None:
                bay["ptz_id"] = new_ptz_id
            if type is not None:
                bay["type"] = type
            if tenantID is not None:
                bay["tenantID"] = tenantID
            elif force_clear_plate:  # Clear tenantID if setting to vacant
                bay["tenantID"] = None
            if cardholderName is not None:
                bay["cardholderName"] = cardholderName
            elif force_clear_plate:  # Clear cardholderName if setting to vacant
                bay["cardholderName"] = None
                
        updated_bays.append(bay)

    if not bay_found:
        raise HTTPException(status_code=404, detail="Bay not found")

    cameras_collection.update_one({"serial": serial}, {"$set": {"bays": updated_bays}})
    return {"message": "Bay updated successfully"}
    
# Assign a tenant to a specific bay
@app.patch("/cameras/{serial}/bays/tenant")
async def assign_tenant_to_bay(serial: str, ptz_id: str, tenant: str, bay_id: Optional[str] = None):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    updated_bays = []
    bay_found = False
    for bay in camera["bays"]:
        if bay["ptz_id"] == ptz_id:
            bay_found = True
            bay["tenant"] = tenant
        updated_bays.append(bay)

    if not bay_found:
        raise HTTPException(status_code=404, detail="Bay not found")

    cameras_collection.update_one({"serial": serial}, {"$set": {"bays": updated_bays}})
    return {"message": "Tenant assigned to bay successfully"}

# Update a specific bay without replacing all bays
# @app.patch("/cameras/{serial}/bays")
# async def update_specific_bay(
#     serial: str, 
#     ptz_id: str, 
#     tenant: Optional[str] = None, 
#     status: Optional[str] = None, 
#     plate: Optional[str] = None, 
#     parked_tenant: Optional[str] = None, 
#     time_parked: Optional[str] = None, 
#     bay_id: Optional[str] = None,
#     new_ptz_id: Optional[str] = None,
#     type: Optional[str] = None
# ):
#     camera = cameras_collection.find_one({"serial": serial})
#     if camera is None:
#         raise HTTPException(status_code=404, detail="Camera not found")

#     # If updating PTZ ID, check if new PTZ ID is already in use
#     if new_ptz_id is not None:
#         for bay in camera["bays"]:
#             if bay["ptz_id"] == new_ptz_id and bay["ptz_id"] != ptz_id:
#                 raise HTTPException(status_code=400, detail=f"PTZ ID {new_ptz_id} is already in use")

#     updated_bays = []
#     bay_found = False
#     for bay in camera["bays"]:
#         if bay["ptz_id"] == ptz_id:
#             bay_found = True
#             if tenant is not None:
#                 bay["tenant"] = tenant
#             if status is not None:
#                 bay["status"] = status
#             if plate is not None:
#                 bay["plate"] = plate
#             if parked_tenant is not None:
#                 bay["parked_tenant"] = parked_tenant
#             if time_parked is not None:
#                 bay["time_parked"] = time_parked
#             if new_ptz_id is not None:
#                 bay["ptz_id"] = new_ptz_id
#             if type is not None:
#                 bay["type"] = type
#         updated_bays.append(bay)

#     if not bay_found:
#         raise HTTPException(status_code=404, detail="Bay not found")

#     cameras_collection.update_one({"serial": serial}, {"$set": {"bays": updated_bays}})
#     return {"message": "Bay updated successfully"}

# Update PTZ ID for a bay
@app.patch("/cameras/{serial}/bays/ptz")
async def update_bay_ptz_id(serial: str, update_data: dict):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    old_ptz_id = update_data.get('old_ptz_id')
    new_ptz_id = update_data.get('new_ptz_id')
    bay_id = update_data.get('bay_id')

    if not all([old_ptz_id, new_ptz_id, bay_id]):
        raise HTTPException(status_code=400, detail="Missing required fields")

    # Check if new PTZ ID is already in use
    for bay in camera["bays"]:
        if bay["ptz_id"] == new_ptz_id and bay["bay_id"] != bay_id:
            raise HTTPException(status_code=400, detail=f"PTZ ID {new_ptz_id} is already in use")

    # Update the PTZ ID
    updated_bays = []
    bay_found = False
    for bay in camera["bays"]:
        if bay["ptz_id"] == old_ptz_id and bay["bay_id"] == bay_id:
            bay_found = True
            bay["ptz_id"] = new_ptz_id
        updated_bays.append(bay)

    if not bay_found:
        raise HTTPException(status_code=404, detail="Bay not found")

    cameras_collection.update_one({"serial": serial}, {"$set": {"bays": updated_bays}})
    return {"message": "PTZ ID updated successfully"}

# Add new bays to an existing camera
@app.post("/cameras/{serial}/bays")
async def add_bays_to_camera(serial: str, bays: List[BayModel]):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    existing_bays = camera.get("bays", [])
    
    # Find the highest existing PTZ ID
    highest_ptz = 1  # Start at 1 since it's the home position
    for bay in existing_bays:
        try:
            ptz_num = int(bay.get("ptz_id", "1"))
            highest_ptz = max(highest_ptz, ptz_num)
        except ValueError:
            continue
    
    # Process new bays and assign PTZ IDs starting after the highest existing one
    new_bays = []
    next_ptz = max(2, highest_ptz + 1)  # Ensure we start at least from 2
    
    for bay in bays:
        bay_dict = bay.dict()
        if not bay_dict.get("ptz_id"):  # Only assign if PTZ ID is not already set
            bay_dict["ptz_id"] = str(next_ptz)
            next_ptz += 1
        new_bays.append(bay_dict)
    
    updated_bays = existing_bays + new_bays

    cameras_collection.update_one({"serial": serial}, {"$set": {"bays": updated_bays}})
    return {"message": "Bays added to camera successfully"}

# Delete a bay from a camera
@app.delete("/cameras/{serial}/bay")
async def delete_bay(serial: str, ptz_id: str):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    # Filter out the bay with the specified ptz_id
    updated_bays = [bay for bay in camera["bays"] if bay["ptz_id"] != ptz_id]

    # If the number of bays didn't change, the bay wasn't found
    if len(updated_bays) == len(camera["bays"]):
        raise HTTPException(status_code=404, detail="Bay not found")

    cameras_collection.update_one({"serial": serial}, {"$set": {"bays": updated_bays}})
    return {"message": "Bay deleted successfully"}

# List all cameras
@app.get("/cameras/", response_model=List[CameraModel])
async def list_cameras():
    cameras = cameras_collection.find()
    return [camera_serializer(camera) for camera in cameras]

# Delete a camera by serial
@app.delete("/cameras/{serial}")
async def delete_camera(serial: str):
    # First check if the camera exists
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Delete the camera (this will also delete all associated bays since they're embedded)
    result = cameras_collection.delete_one({"serial": serial})
    if result.deleted_count == 0:
        raise HTTPException(status_code=500, detail="Failed to delete camera")
    
    return {"message": "Camera and all associated bays deleted successfully"}

# Get bay details by bay_id
#@app.get("/bays/{bay_id}")
#async def get_bay(bay_id: str):
#    cameras = cameras_collection.find()
#    for camera in cameras:
#        for bay in camera["bays"]:
#            if bay["bay_id"] == bay_id:
#                return {
#                    "bay_id": bay["bay_id"],
#                    "tenant": bay.get("tenant"),
#                    "status": bay["status"],
#                    "plate": bay.get("plate"),
#                    "parked_tenant": bay.get("parked_tenant"),
#                    "time_parked": bay.get("time_parked"),
#                    "ptz_id": bay.get("ptz_id")
 #               }
#    raise HTTPException(status_code=404, detail="Bay not found")

# Config API to get and set configuration values
@app.get("/config/{key}")
async def get_config(key: str):
    config = config_collection.find_one({"key": key})
    if config is None:
        raise HTTPException(status_code=404, detail="Configuration not found")
    return {"key": config["key"], "value": config["value"]}

@app.post("/config/")
async def set_config(config: ConfigModel):
    existing_config = config_collection.find_one({"key": config.key})
    if existing_config:
        config_collection.update_one({"key": config.key}, {"$set": {"value": config.value}})
    else:
        config_collection.insert_one(config.dict())
    return {"message": "Configuration set successfully"}

# Increment events counter for a camera
@app.post("/cameras/{serial}/events/increment")
async def increment_events(serial: str):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Increment events counter, initializing to 1 if it doesn't exist
    result = cameras_collection.update_one(
        {"serial": serial},
        {"$inc": {"events": 1}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(status_code=500, detail="Failed to increment events counter")
    
    # Get the updated events count
    updated_camera = cameras_collection.find_one({"serial": serial})
    return {"message": "Events counter incremented successfully", "events": updated_camera.get("events", 0)}

# Get events count for a camera
@app.get("/cameras/{serial}/events")
async def get_events(serial: str):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    return {"events": camera.get("events", 0)}

@app.patch("/cameras/{serial}/events/reset")
async def reset_events(serial: str):
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    result = cameras_collection.update_one(
        {"serial": serial},
        {"$set": {"events": 0}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(status_code=500, detail="Failed to reset events counter")
    
    return {"message": "Events counter reset successfully", "events": 0}

# New endpoint: Get image from camera for given serial and bay id
@app.get("/cameras/{serial}/bays/{bay_id}/image")
def get_camera_image(serial: str, bay_id: str):
    # Lookup the camera by serial
    camera = cameras_collection.find_one({"serial": serial})
    if camera is None:
        raise HTTPException(status_code=404, detail="Camera not found")

    # Find the bay using the provided bay_id
    bay = None
    for b in camera["bays"]:
        if b["bay_id"] == bay_id:
            bay = b
            break
    if bay is None:
        raise HTTPException(status_code=404, detail="Bay not found")

    # Use the bay's ptz_id as the preset position.
    preset_position = bay.get("ptz_id")
    if not preset_position:
        raise HTTPException(status_code=400, detail="Preset position not defined for this bay")

    camera_ip = camera["camera_ip"]

    # Read Axis camera credentials from environment variables (defaults provided)
    axis_user = os.getenv("AXIS_USER", "root")
    axis_password = os.getenv("AXIS_PASSWORD", "M1ecrdry1!")

    # Step 1: Move camera to the preset position
    ptz_url = f"http://{camera_ip}/axis-cgi/com/ptz.cgi"
    payload = {"gotoserverpresetno": preset_position, "camera": "1"}
    try:
        move_response = requests.post(ptz_url, data=payload, auth=HTTPDigestAuth(axis_user, axis_password))
        if move_response.status_code != 200:
            raise HTTPException(status_code=500, detail="Failed to move camera to preset position")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error moving camera: {str(e)}")

    # Wait a few seconds for the camera to move to the preset position.
    time.sleep(3)  # Adjust this delay as needed.

    # Step 2: Request the image from the camera
    image_url = f"http://{camera_ip}/axis-cgi/jpg/image.cgi?resolution=1920x1080&compression=0"
    try:
        image_response = requests.get(image_url, auth=HTTPDigestAuth(axis_user, axis_password))
        if image_response.status_code != 200:
            raise HTTPException(status_code=500, detail="Failed to retrieve image from camera")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving image: {str(e)}")

    # Return the image content with the appropriate media type.
    return Response(content=image_response.content, media_type="image/jpeg")

# Update the plate search endpoint to use the helper function
@app.get("/plates/search")
def search_plate(plate: str, tenant_id: Optional[str] = None, cardholder_name: Optional[str] = None):
    # Iterate over all cameras and bays to find a matching plate
    found_camera = None
    found_bay = None
    
    try:
        for camera in cameras_collection.find():
            for bay in camera.get("bays", []):
                # Check if plate matches
                if bay.get("plate") == plate:
                    # If tenant_id is provided, check if it matches
                    if tenant_id and bay.get("tenantID") != tenant_id:
                        continue
                    
                    # If cardholder_name is provided, check if it matches
                    if cardholder_name and bay.get("cardholderName") != cardholder_name:
                        continue
                    
                    found_camera = camera
                    found_bay = bay
                    break
            if found_camera:
                break

        if not found_camera or not found_bay:
            # This should return a 404 directly, not be caught by the outer exception handler
            raise HTTPException(status_code=404, detail="Plate not found")

        camera_ip = found_camera["camera_ip"]
        preset_position = found_bay.get("ptz_id")
        serial = found_camera.get("serial")
        
        if not preset_position:
            raise HTTPException(status_code=400, detail="Preset position not defined for this bay")

        encoded_image, error = get_camera_image_for_bay(camera_ip, preset_position)
        
        # Print the time_parked value for debugging
        print(f"DEBUG - time_parked from database: {found_bay.get('time_parked')}")
        print(f"DEBUG - found_bay data: {found_bay}")
        
        response = {
            "bay_id": found_bay["bay_id"],
            "image": encoded_image,
            "ptz_id": preset_position,
            "serial": serial,
            "plate_number": found_bay.get("plate"),
            "tenant_id": found_bay.get("tenantID"),
            "cardholder_name": found_bay.get("cardholderName"),
            "status": found_bay.get("status", "occupied"),
            "time_parked": found_bay.get("time_parked")
        }
        
        if error:
            response["error"] = error
            
        return response
        
    except HTTPException as http_ex:
        # Re-raise HTTP exceptions directly without wrapping them
        raise http_ex
    except Exception as e:
        # Log and wrap other unexpected exceptions as 500 errors
        print(f"Unexpected error in plate search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/plates/partial_search")
def partial_search_plate(query: str):
    # Verify minimum length requirement
    if len(query) < 3:
        raise HTTPException(status_code=400, detail="Query must be at least 3 characters long")
    
    # Search for partial matches across all cameras and bays
    matches = []
    
    try:
        for camera in cameras_collection.find():
            for bay in camera.get("bays", []):
                # Skip bays with no plate information
                if not bay.get("plate"):
                    continue
                    
                # Check if the query is a substring of the plate (case insensitive)
                if query.lower() in bay.get("plate", "").lower():
                    matches.append({
                        "bay_id": bay["bay_id"],
                        "plate": bay["plate"],
                        "status": bay["status"],
                        "tenant": bay.get("tenant", "Unknown"),
                        "camera_serial": camera["serial"],
                        "tenant_id": bay.get("tenantID", ""),
                        "cardholder_name": bay.get("cardholderName", ""),
                        "time_parked": bay.get("time_parked")
                    })
        
        # Return the first 10 matches for better performance
        # You can adjust this limit based on your needs
        return {"matches": matches[:10], "total_count": len(matches)}
        
    except Exception as e:
        # Log and wrap unexpected exceptions as 500 errors
        print(f"Unexpected error in partial plate search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/api/tenants/partial_search")
def partial_search_tenant(query: str):
    # Check if query is likely a tenant ID (using the format with underscores)
    is_likely_tenant_id = "_" in query
    
    # Verify minimum length requirement
    min_length = 5 if is_likely_tenant_id else 3
    if len(query) < min_length:
        raise HTTPException(
            status_code=400, 
            detail=f"Query must be at least {min_length} characters long"
        )
    
    # Search for partial matches across all cameras and bays
    matches = []
    
    try:
        for camera in cameras_collection.find():
            for bay in camera.get("bays", []):
                # Skip bays with no tenant information
                if not bay.get("tenantID") and not bay.get("cardholderName"):
                    continue
                    
                match_found = False
                # Check if the query is a substring of the tenant ID (if tenant ID exists)
                if bay.get("tenantID") and query.lower() in bay.get("tenantID", "").lower():
                    match_found = True
                    
                # Check if the query is a substring of the cardholder name (if cardholder name exists)
                elif bay.get("cardholderName") and query.lower() in bay.get("cardholderName", "").lower():
                    match_found = True
                    
                if match_found:
                    matches.append({
                        "bay_id": bay.get("bay_id"),
                        "tenant_id": bay.get("tenantID", ""),
                        "name": bay.get("cardholderName", ""),
                        "plate": bay.get("plate", ""),
                        "status": bay.get("status", ""),
                        "last_seen": bay.get("time_parked", "")
                    })
        
        # Return the first 10 matches for better performance
        return {"matches": matches[:10], "total_count": len(matches)}
        
    except Exception as e:
        # Log and wrap unexpected exceptions as 500 errors
        print(f"Unexpected error in partial tenant search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/api/bays/search")
def search_bay_by_id(bay_id: str):
    try:
        # Search for the bay across all cameras
        found_camera = None
        found_bay = None
        print(bay_id)
        
        for camera in cameras_collection.find():
            for bay in camera.get("bays", []):
                if bay.get("bay_id") == bay_id:
                    found_camera = camera
                    found_bay = bay
                    break
            if found_camera:
                break
                
        if not found_camera or not found_bay:
            raise HTTPException(status_code=404, detail="Bay not found")
            
        camera_ip = found_camera["camera_ip"]
        preset_position = found_bay.get("ptz_id")
        if not preset_position:
            raise HTTPException(status_code=400, detail="Preset position not defined for this bay")
            
        encoded_image, error = get_camera_image_for_bay(camera_ip, preset_position)
        
        response = {
            "bay_id": bay_id,
            "plate": found_bay.get("plate"),
            "status": found_bay.get("status"),
            "tenant": found_bay.get("tenant"),
            "parked_tenant": found_bay.get("parked_tenant"),
            "time_parked": found_bay.get("time_parked"),
            "type": found_bay.get("type", "car"),  # Added type field with default
            "tenant_id": found_bay.get("tenantID"),
            "cardholder_name": found_bay.get("cardholderName"),
            "image": encoded_image
        }
        
        if error:
            response["error"] = error
            
        return response
        
    except Exception as e:
        # Log the exception for debugging
        print(f"Unexpected error in bay search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/api/tenants/search")
def search_tenant(query: str):
    try:
        # Check if the query is for a tenant ID or name
        # This will search across all cameras and bays to find matching tenant info
        found_camera = None
        found_bay = None
        
        for camera in cameras_collection.find():
            for bay in camera.get("bays", []):
                # Match either tenant ID or cardholder name
                if ((bay.get("tenantID") and bay.get("tenantID") == query) or 
                    (bay.get("cardholderName") and bay.get("cardholderName").lower() == query.lower())):
                    found_camera = camera
                    found_bay = bay
                    break
            if found_camera:
                break
                
        if not found_camera or not found_bay:
            raise HTTPException(status_code=404, detail="Tenant not found")
            
        camera_ip = found_camera["camera_ip"]
        preset_position = found_bay.get("ptz_id")
        if not preset_position:
            raise HTTPException(status_code=400, detail="Preset position not defined for this bay")
            
        encoded_image, error = get_camera_image_for_bay(camera_ip, preset_position)
        
        response = {
            "tenant_id": found_bay.get("tenantID"),
            "name": found_bay.get("cardholderName"),
            "bay_id": found_bay.get("bay_id"),
            "plate": found_bay.get("plate"),
            "status": found_bay.get("status"),
            "time_parked": found_bay.get("time_parked"),
            "image": encoded_image
        }
        
        if error:
            response["error"] = error
            
        return response
        
    except HTTPException as http_ex:
        # Re-raise HTTP exceptions directly without wrapping them
        raise http_ex
    except Exception as e:
        # Log and wrap other unexpected exceptions as 500 errors
        print(f"Unexpected error in tenant search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)