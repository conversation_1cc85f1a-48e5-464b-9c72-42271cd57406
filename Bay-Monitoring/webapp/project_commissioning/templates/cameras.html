<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking Bay Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --background: #0c0a09;
            --foreground: #fafafa;
            --card: #1c1917;
            --card-foreground: #fafafa;
            --primary: #a855f7;
            --primary-foreground: #fafafa;
            --muted: #57534e;
            --muted-foreground: #a8a29e;
            --accent: #292524;
            --accent-foreground: #fafafa;
            --destructive: #ef4444;
            --destructive-foreground: #fafafa;
            --border: #292524;
            --input: #292524;
            --radius: 0.5rem;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            padding: 0;
        }

        .navbar {
            background-color: var(--card);
            border-bottom: 1px solid var(--border);
            padding: 1rem 1.5rem;
        }

        .sidebar {
            background-color: var(--card);
            border-right: 1px solid var(--border);
            height: 100vh;
            position: fixed;
            width: 280px;
            padding: 1.5rem 1rem;
        }

        .content {
            margin-left: 280px;
            padding: 2rem;
        }

        .card {
            background-color: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .card-header {
            background-color: var(--card);
            border-bottom: 1px solid var(--border);
            padding: 1rem 1.5rem;
        }

        .input-group {
            border-radius: var(--radius);
            overflow: hidden;
        }

        .form-control {
            background-color: var(--input);
            border: 1px solid var(--border);
            color: var(--foreground);
            padding: 0.75rem 1rem;
        }

        .form-control:focus {
            background-color: var(--input);
            border-color: var(--primary);
            box-shadow: none;
            color: var(--foreground);
        }

        .btn {
            border-radius: var(--radius);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
        }

        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background-color: #9333ea;
            border-color: #9333ea;
        }

        .btn-outline-secondary {
            border-color: var(--border);
            color: var(--foreground);
        }

        .btn-outline-secondary:hover {
            background-color: var(--accent);
            border-color: var(--accent);
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .nav-pills .nav-link {
            border-radius: var(--radius);
            color: var(--foreground);
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
        }

        .nav-pills .nav-link:hover {
            background-color: var(--accent);
        }

        .nav-pills .nav-link.active {
            background-color: var(--primary);
            color: var(--primary-foreground);
        }

        .table {
            color: var(--foreground);
        }

        .table th {
            border-bottom-color: var(--border);
            color: var(--muted-foreground);
            font-weight: 500;
            padding: 1rem;
        }

        .table td {
            border-bottom-color: var(--border);
            padding: 1rem;
        }

        .plate-image {
            max-width: 100%;
            height: auto;
            border-radius: var(--radius);
        }

        .search-modal .modal-content {
            background-color: var(--card);
            border-color: var(--border);
            color: var(--foreground);
        }

        .search-modal .modal-header,
        .search-modal .modal-footer {
            border-color: var(--border);
        }

        .search-modal .modal-header .btn-close {
            filter: invert(1);
        }

        .alert {
            border-radius: var(--radius);
        }

        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.2);
            color: #4ade80;
        }

        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.2);
            color: #f87171;
        }

        .alert-warning {
            background-color: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.2);
            color: #fbbf24;
        }

        .search-container {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .search-card {
            flex: 1;
            background-color: var(--card);
            border-radius: var(--radius);
            border: 1px solid var(--border);
            padding: 1.5rem;
        }
        
        .search-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 999px;
            font-weight: 500;
        }
        
        .badge-purple {
            background-color: rgba(168, 85, 247, 0.1);
            color: #c084fc;
        }
        
        .badge-blue {
            background-color: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
        }
        
        .badge-green {
            background-color: rgba(34, 197, 94, 0.1);
            color: #4ade80;
        }
        
        .badge-red {
            background-color: rgba(239, 68, 68, 0.1);
            color: #f87171;
        }
        
        .camera-card {
            transition: all 0.2s ease;
        }
        
        .camera-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .spinner-border {
            color: var(--primary);
        }

        @media (max-width: 992px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block p-0">
                <div class="sidebar">
                    <div class="d-flex align-items-center mb-4">
                        <i class="fas fa-parking text-primary me-2" style="font-size: 1.5rem;"></i>
                        <h5 class="mb-0">Parking Management</h5>
                    </div>
                    
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('index') }}">
                                <i class="fas fa-home me-2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('list_cameras') }}">
                                <i class="fas fa-video me-2"></i> Cameras
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-car me-2"></i> Bays
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-building me-2"></i> Tenants
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-chart-bar me-2"></i> Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-cog me-2"></i> Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 p-0">
                <!-- Navbar -->
                <nav class="navbar navbar-dark">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary me-2 d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarOffcanvas">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div class="d-flex d-lg-none align-items-center">
                            <i class="fas fa-parking text-primary me-2"></i>
                            <span>Parking Management</span>
                        </div>
                    </div>
                </nav>
                
                <!-- Content -->
                <div class="content">
                    <h1 class="page-title">Parking Dashboard</h1>
                    
                    <!-- Search Section -->
                    <div class="search-container">
                        <div class="search-card">
                            <div class="search-title">
                                <i class="fas fa-search me-2"></i> Search by License Plate
                            </div>
                            <div class="input-group">
                                <input type="text" id="plateSearch" class="form-control" placeholder="Enter license plate number">
                                <button class="btn btn-primary" type="button" onclick="searchPlate()">
                                    Search
                                </button>
                            </div>
                        </div>
                        
                        <div class="search-card">
                            <div class="search-title">
                                <i class="fas fa-car me-2"></i> Search by Bay ID
                            </div>
                            <div class="input-group">
                                <input type="text" id="baySearch" class="form-control" placeholder="Enter bay ID (e.g. 58A)">
                                <button class="btn btn-primary" type="button" onclick="searchBay()">
                                    Search
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions Section -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="mb-3">Quick Actions</h5>
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('add_camera') }}" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-plus me-2"></i> Add Camera
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('bulk_add_cameras') }}" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-upload me-2"></i> Bulk Add Cameras
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                                        <i class="fas fa-users me-2"></i> Update Tenants
                                    </button>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button type="button" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-file-export me-2"></i> Export Data
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Camera List -->
                    <h4 class="mb-3">Camera List</h4>
                    
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <div class="row">
                        {% for camera in cameras %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card camera-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">{{ camera.serial }}</h5>
                                    <span class="badge badge-purple">{{ camera.bays|length }} Bays</span>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted">IP Address</small>
                                        <p class="mb-0">{{ camera.camera_ip }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted">Events</small>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <p class="mb-0">{{ camera.events|default(0) }}</p>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="resetEvents('{{ camera.serial }}')">
                                                Reset
                                            </button>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <small class="text-muted">Bay Status</small>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleBays('{{ camera.serial }}')">
                                            <span id="toggle-text-{{ camera.serial }}">Show</span>
                                        </button>
                                    </div>
                                    <div id="bays-{{ camera.serial }}" class="table-responsive" style="display: none;">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Bay ID</th>
                                                    <th>Status</th>
                                                    <th>Plate</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for bay in camera.bays %}
                                                <tr>
                                                    <td>{{ bay.bay_id }}</td>
                                                    <td>
                                                        {% if bay.status == 'vacant' %}
                                                        <span class="badge badge-green">Vacant</span>
                                                        {% else %}
                                                        <span class="badge badge-red">Occupied</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ bay.plate or '-' }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('view_camera', serial=camera.serial) }}" class="btn btn-sm btn-primary">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mobile Sidebar Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="sidebarOffcanvas" aria-labelledby="sidebarOffcanvasLabel" style="background-color: var(--card); color: var(--foreground);">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="sidebarOffcanvasLabel">
                <i class="fas fa-parking text-primary me-2"></i>
                Parking Management
            </h5>
            <button type="button" class="btn-close text-reset btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="nav nav-pills flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="{{ url_for('index') }}">
                        <i class="fas fa-home me-2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('list_cameras') }}">
                        <i class="fas fa-video me-2"></i> Cameras
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-car me-2"></i> Bays
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-building me-2"></i> Tenants
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-chart-bar me-2"></i> Reports
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Bulk Update Tenants Modal -->
    <div class="modal fade search-modal" id="bulkUpdateModal" tabindex="-1" aria-labelledby="bulkUpdateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkUpdateModalLabel">Bulk Update Tenants</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('bulk_update_tenants') }}" method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <p>Upload a CSV file to update tenant information for multiple bays. The CSV should have two columns: 'bay' and 'tenant'.</p>
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">CSV File</label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            <div class="form-text" style="color: var(--muted-foreground);">
                                Example CSV format:<br>
                                bay,tenant<br>
                                10A,PWC<br>
                                10B,PWC<br>
                                10C,PWC
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Upload and Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Plate Search Modal -->
    <div class="modal fade search-modal" id="plateSearchModal" tabindex="-1" aria-labelledby="plateSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="plateSearchModalLabel">License Plate Search Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="plateSearchLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Searching for vehicle...</p>
                    </div>
                    <div id="plateSearchResults" style="display: none;">
                        <div id="plateSearchSuccess" style="display: none;">
                            <div class="alert alert-success">
                                Vehicle found in bay <span id="plateBayNumber"></span>
                            </div>
                            <div class="search-result">
                                <img id="plateImage" class="plate-image img-fluid" src="" alt="Vehicle image">
                            </div>
                        </div>
                        <div id="plateSearchError" style="display: none;">
                            <div class="alert alert-danger">
                                <span id="plateErrorMessage">Vehicle not found</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bay Search Modal -->
    <div class="modal fade search-modal" id="baySearchModal" tabindex="-1" aria-labelledby="baySearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="baySearchModalLabel">Bay Search Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="baySearchLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Searching for bay...</p>
                    </div>
                    <div id="baySearchResults" style="display: none;">
                        <div id="baySearchSuccess" style="display: none;">
                            <div class="alert alert-success">
                                Bay <span id="bayNumber"></span> found
                            </div>
                            <div class="search-details">
                                <table class="table table-bordered table-sm">
                                    <tr>
                                        <th>Status</th>
                                        <td id="bayStatus"></td>
                                    </tr>
                                    <tr>
                                        <th>Tenant</th>
                                        <td id="bayTenant"></td>
                                    </tr>
                                    <tr>
                                        <th>Plate</th>
                                        <td id="bayPlate"></td>
                                    </tr>
                                    <tr>
                                        <th>Parked Tenant</th>
                                        <td id="bayParkedTenant"></td>
                                    </tr>
                                    <tr>
                                        <th>Time Parked</th>
                                        <td id="bayTimeParked"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="search-result">
                                <img id="bayImage" class="plate-image img-fluid" src="" alt="Bay image">
                            </div>
                        </div>
                        <div id="baySearchError" style="display: none;">
                            <div class="alert alert-danger">
                                <span id="bayErrorMessage">Bay not found</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleBays(serial) {
            const baysDiv = document.getElementById('bays-' + serial);
            const toggleText = document.getElementById('toggle-text-' + serial);
            if (baysDiv.style.display === 'none') {
                baysDiv.style.display = 'block';
                toggleText.textContent = 'Hide';
            } else {
                baysDiv.style.display = 'none';
                toggleText.textContent = 'Show';
            }
        }

        function resetEvents(serial) {
            if (confirm('Are you sure you want to reset the events counter for this camera?')) {
                fetch(`/camera/${serial}/events/reset`, {
                    method: 'POST',
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to reset events counter');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to reset events counter');
                });
            }
        }

        function searchPlate() {
            const plateNumber = document.getElementById('plateSearch').value.trim();
            if (!plateNumber) {
                alert('Please enter a license plate number');
                return;
            }

            // Reset modal state
            document.getElementById('plateSearchLoading').style.display = 'block';
            document.getElementById('plateSearchResults').style.display = 'none';
            document.getElementById('plateSearchSuccess').style.display = 'none';
            document.getElementById('plateSearchError').style.display = 'none';

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('plateSearchModal'));
            modal.show();

            // Make API request
            fetch(`/api/plates/search?plate=${encodeURIComponent(plateNumber)}`)
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Plate not found');
                        });
                    }
                })
                .then(data => {
                    // Hide loading state, show results
                    document.getElementById('plateSearchLoading').style.display = 'none';
                    document.getElementById('plateSearchResults').style.display = 'block';
                    document.getElementById('plateSearchSuccess').style.display = 'block';
                    
                    // Update results
                    document.getElementById('plateBayNumber').textContent = data.bay_id;
                    
                    // Convert base64 to image source
                    const imageData = `data:image/jpeg;base64,${data.image}`;
                    document.getElementById('plateImage').src = imageData;
                    
                    // Show error message if provided by API but still show the image
                    if (data.error) {
                        const errorAlert = document.createElement('div');
                        errorAlert.className = 'alert alert-warning mt-3';
                        errorAlert.textContent = data.error;
                        document.getElementById('plateSearchSuccess').appendChild(errorAlert);
                    }
                })
                .catch(error => {
                    // Hide loading state, show error
                    document.getElementById('plateSearchLoading').style.display = 'none';
                    document.getElementById('plateSearchResults').style.display = 'block';
                    document.getElementById('plateSearchError').style.display = 'block';
                    document.getElementById('plateErrorMessage').textContent = error.message;
                });
        }

        function searchBay() {
            const bayId = document.getElementById('baySearch').value.trim();
            if (!bayId) {
                alert('Please enter a bay ID');
                return;
            }

            // Reset modal state
            document.getElementById('baySearchLoading').style.display = 'block';
            document.getElementById('baySearchResults').style.display = 'none';
            document.getElementById('baySearchSuccess').style.display = 'none';
            document.getElementById('baySearchError').style.display = 'none';

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('baySearchModal'));
            modal.show();

            // Make API request
            fetch(`/api/bays/search?bay_id=${encodeURIComponent(bayId)}`)
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Bay not found');
                        });
                    }
                })
                .then(data => {
                    // Hide loading state, show results
                    document.getElementById('baySearchLoading').style.display = 'none';
                    document.getElementById('baySearchResults').style.display = 'block';
                    document.getElementById('baySearchSuccess').style.display = 'block';
                    
                    // Update results
                    document.getElementById('bayNumber').textContent = data.bay_id;
                    document.getElementById('bayStatus').textContent = data.status || 'Unknown';
                    document.getElementById('bayTenant').textContent = data.tenant || 'None';
                    document.getElementById('bayPlate').textContent = data.plate || 'None';
                    document.getElementById('bayParkedTenant').textContent = data.parked_tenant || 'None';
                    
                    // Format timestamp if exists
                    if (data.time_parked) {
                        const date = new Date(data.time_parked);
                        document.getElementById('bayTimeParked').textContent = date.toLocaleString();
                    } else {
                        document.getElementById('bayTimeParked').textContent = 'Not parked';
                    }
                    
                    // Convert base64 to image source
                    const imageData = `data:image/jpeg;base64,${data.image}`;
                    document.getElementById('bayImage').src = imageData;
                    
                    // Show error message if provided by API but still show the image
                    if (data.error) {
                        const errorAlert = document.createElement('div');
                        errorAlert.className = 'alert alert-warning mt-3';
                        errorAlert.textContent = data.error;
                        document.getElementById('baySearchSuccess').appendChild(errorAlert);
                    }
                })
                .catch(error => {
                    // Hide loading state, show error
                    document.getElementById('baySearchLoading').style.display = 'none';
                    document.getElementById('baySearchResults').style.display = 'block';
                    document.getElementById('baySearchError').style.display = 'block';
                    document.getElementById('bayErrorMessage').textContent = error.message;
                });
        }

        // Allow searching by pressing Enter key
        document.getElementById('plateSearch').addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchPlate();
            }
        });

        document.getElementById('baySearch').addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchBay();
            }
        });
    </script>
</body>
</html>