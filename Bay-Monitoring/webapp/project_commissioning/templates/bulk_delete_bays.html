<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Delete Bays - Parking Bay Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('list_cameras') }}">Cameras</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('view_camera', serial=camera.serial) }}">Camera {{ camera.serial }}</a></li>
                <li class="breadcrumb-item active">Bulk Delete Bays</li>
            </ol>
        </nav>

        <h1 class="mb-4">Bulk Delete Bays - Camera {{ camera.serial }}</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Current Bays</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Bay ID</th>
                                <th>PTZ ID</th>
                                <th>Status</th>
                                <th>Tenant</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for bay in camera.bays %}
                            <tr>
                                <td>{{ bay.bay_id }}</td>
                                <td>{{ bay.ptz_id }}</td>
                                <td>{{ bay.status }}</td>
                                <td>{{ bay.tenant or 'Not assigned' }}</td>
                                <td>
                                    <form action="{{ url_for('delete_bay', serial=camera.serial, ptz_id=bay.ptz_id) }}" method="POST" 
                                          onsubmit="return confirm('Are you sure you want to delete bay {{ bay.bay_id }}?');" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Delete</button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Bulk Delete Using CSV</h5>
                <p class="card-text">Upload a CSV file containing the bay IDs to delete.</p>
                
                <div class="card mb-3">
                    <div class="card-body bg-light">
                        <h6 class="card-subtitle mb-2">CSV Format Example:</h6>
                        <pre class="mb-0">bay_id
1A
1B
2A</pre>
                        <small class="text-muted">Note: First line must be the header 'bay_id'</small>
                    </div>
                </div>

                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                    </div>
                    
                    <button type="submit" class="btn btn-danger" 
                            onclick="return confirm('Are you sure you want to delete the bays listed in the CSV file?');">
                        Delete Bays
                    </button>
                    <a href="{{ url_for('view_camera', serial=camera.serial) }}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>