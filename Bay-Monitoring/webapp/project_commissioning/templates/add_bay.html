<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Bay - Parking Bay Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('list_cameras') }}">Cameras</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('view_camera', serial=serial) }}">Camera {{ serial }}</a></li>
                <li class="breadcrumb-item active">Add Bay</li>
            </ol>
        </nav>

        <h1 class="mb-4">Add New Bay</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-md-8">
                <form method="POST">
                    <div class="mb-3">
                        <label for="bay_id" class="form-label">Bay ID</label>
                        <input type="text" class="form-control" id="bay_id" name="bay_id" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="ptz_id" class="form-label">PTZ ID</label>
                        <input type="text" class="form-control" id="ptz_id" name="ptz_id" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="type" class="form-label">Bay Type</label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="car">Car</option>
                            <option value="car(small)">Car (Small)</option>
                            <option value="car(EV)">Car (EV)</option>
                            <option value="bike">Bike</option>
                        </select>
                        <div class="form-text">Select the type of vehicle for this bay</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="tenant" class="form-label">Tenant (Optional)</label>
                        <input type="text" class="form-control" id="tenant" name="tenant">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Add Bay</button>
                    <a href="{{ url_for('view_camera', serial=serial) }}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
