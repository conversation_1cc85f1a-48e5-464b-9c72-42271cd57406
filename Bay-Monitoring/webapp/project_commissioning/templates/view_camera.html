<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Details - Parking Bay Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('list_cameras') }}">Cameras</a></li>
                <li class="breadcrumb-item active">Camera {{ camera.serial }}</li>
            </ol>
        </nav>

        <h1 class="mb-4">Camera Details</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Camera Information</h5>
                <div class="mb-3">
                    <div class="d-flex align-items-center gap-2">
                        <p class="card-text mb-0"><strong>Serial:</strong> {{ camera.serial }}</p>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editCameraField('serial', '{{ camera.serial }}')">
                            Edit Serial
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex align-items-center gap-2">
                        <p class="card-text mb-0"><strong>IP Address:</strong> {{ camera.camera_ip }}</p>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editCameraField('ip', '{{ camera.camera_ip }}')">
                            Edit IP
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex align-items-center gap-2">
                        <p class="card-text mb-0"><strong>Events:</strong> {{ camera.events|default(0) }}</p>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="resetEvents('{{ camera.serial }}')">
                            Reset Events
                        </button>
                    </div>
                </div>
                <div class="btn-group">
                    <a href="{{ url_for('add_bay', serial=camera.serial) }}" class="btn btn-primary">Add New Bay</a>
                    <a href="{{ url_for('bulk_delete_bays', serial=camera.serial) }}" class="btn btn-warning">Bulk Delete Bays</a>
                    <button type="button" class="btn btn-danger" onclick="confirmDeleteCamera('{{ camera.serial }}')">Delete Camera</button>
                </div>
            </div>
        </div>

        <h2 class="mb-3">Parking Bays</h2>
        <div class="row">
            {% for bay in camera.bays %}
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Bay {{ bay.bay_id }}</h5>
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <span class="text-nowrap"><strong>PTZ ID:</strong> {{ bay.ptz_id }}</span>
                            <button type="button" class="btn btn-sm btn-outline-secondary" 
                                    onclick="editPTZID('{{ bay.bay_id }}', '{{ bay.ptz_id }}')">
                                Edit PTZ ID
                            </button>
                        </div>
                        <p class="card-text"><strong>Status:</strong> {{ bay.status }}</p>
                        <p class="card-text"><strong>Plate:</strong> {{ bay.plate or 'No plate detected' }}</p>
                        <p class="card-text"><strong>Tenant:</strong> {{ bay.tenant or 'Not assigned' }}</p>
                        
                        <div class="d-flex gap-2 mt-3">
                            <form action="{{ url_for('update_bay', serial=camera.serial, ptz_id=bay.ptz_id) }}" method="POST" class="flex-grow-1">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="tenant" placeholder="Assign tenant" value="{{ bay.tenant or '' }}">
                                    <button type="submit" class="btn btn-outline-primary">Update</button>
                                </div>
                            </form>
                            <form action="{{ url_for('delete_bay', serial=camera.serial, ptz_id=bay.ptz_id) }}" method="POST" 
                                  onsubmit="return confirm('Are you sure you want to delete bay {{ bay.bay_id }}?');">
                                <button type="submit" class="btn btn-outline-danger">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Modal for editing PTZ ID -->
    <div class="modal fade" id="editPTZIDModal" tabindex="-1" aria-labelledby="editPTZIDModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPTZIDModalLabel">Edit PTZ ID</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('update_ptz_id', serial=camera.serial) }}" method="POST">
                    <div class="modal-body">
                        <input type="hidden" id="bayId" name="bay_id">
                        <input type="hidden" id="oldPtzId" name="old_ptz_id">
                        <div class="mb-3">
                            <label for="newPtzId" class="form-label">New PTZ ID for bay <span id="modalBayId"></span></label>
                            <input type="text" class="form-control" id="newPtzId" name="new_ptz_id" required>
                            <div class="form-text">Current PTZ ID: <span id="currentPtzId"></span></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for deleting camera -->
    <div class="modal fade" id="deleteCameraModal" tabindex="-1" aria-labelledby="deleteCameraModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteCameraModalLabel">Delete Camera</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete camera <strong><span id="confirmDeleteSerial"></span></strong>?</p>
                    <p class="text-danger">This will also delete all associated bays and their data. This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">Delete Camera</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for editing camera fields -->
    <div class="modal fade" id="editCameraModal" tabindex="-1" aria-labelledby="editCameraModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCameraModalLabel">Edit Camera</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editCameraForm" onsubmit="return updateCamera(event)">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label id="editFieldLabel" for="editFieldInput" class="form-label"></label>
                            <input type="text" class="form-control" id="editFieldInput" required>
                            <div class="form-text" id="editFieldHelp"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function resetEvents(serial) {
            if (confirm('Are you sure you want to reset the events counter for this camera?')) {
                fetch(`/camera/${serial}/events/reset`, {
                    method: 'POST',
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to reset events counter');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to reset events counter');
                });
            }
        }
        function editPTZID(bayId, currentPtzId) {
            document.getElementById('bayId').value = bayId;
            document.getElementById('oldPtzId').value = currentPtzId;
            document.getElementById('modalBayId').textContent = bayId;
            document.getElementById('currentPtzId').textContent = currentPtzId;
            document.getElementById('newPtzId').value = currentPtzId;
            
            const modal = new bootstrap.Modal(document.getElementById('editPTZIDModal'));
            modal.show();
        }

        function confirmDeleteCamera(serial) {
            const modal = new bootstrap.Modal(document.getElementById('deleteCameraModal'));
            document.getElementById('confirmDeleteSerial').textContent = serial;
            document.getElementById('deleteForm').action = `/camera/${serial}/delete`;
            modal.show();
        }

        let currentField = '';
        function editCameraField(field, currentValue) {
            currentField = field;
            const modal = new bootstrap.Modal(document.getElementById('editCameraModal'));
            const input = document.getElementById('editFieldInput');
            const label = document.getElementById('editFieldLabel');
            const help = document.getElementById('editFieldHelp');
            
            input.value = currentValue;
            
            if (field === 'serial') {
                label.textContent = 'New Serial Number';
                help.textContent = 'Enter the new serial number for the camera';
            } else {
                label.textContent = 'New IP Address';
                help.textContent = 'Enter the new IP address in format ********';
            }
            
            modal.show();
        }

        async function updateCamera(event) {
            event.preventDefault();
            const newValue = document.getElementById('editFieldInput').value;
            const currentSerial = '{{ camera.serial }}';
            
            try {
                const response = await fetch(`${window.location.protocol}//${window.location.host}/api/cameras/${currentSerial}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(
                        currentField === 'serial' 
                            ? { "new_serial": newValue }
                            : { "camera_ip": newValue }
                    ),
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || 'Failed to update camera');
                }
                
                // If we updated the serial, we need to redirect to the new URL
                if (currentField === 'serial') {
                    window.location.href = "{{ url_for('view_camera', serial='') }}" + newValue;
                } else {
                    window.location.reload();
                }
            } catch (error) {
                alert(error.message);
            }
            
            return false;
        }
    </script>
</body>
</html>