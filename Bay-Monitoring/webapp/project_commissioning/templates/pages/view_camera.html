{% extends 'base.html' %}

{% set active_page = 'cameras' %}

{% block title %}Camera {{ camera.serial }} - Parking Bay Management{% endblock %}

{% block content %}
    <div class="d-flex align-items-center mb-4">
        <a href="{{ url_for('list_cameras') }}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="page-title mb-0">Camera Details</h1>
    </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Camera Information</h5>
            <div class="mb-3">
                <div class="d-flex align-items-center gap-2">
                    <p class="card-text mb-0"><strong>Serial:</strong> {{ camera.serial }}</p>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editCameraField('serial', '{{ camera.serial }}')">
                        Edit Serial
                    </button>
                </div>
            </div>
            <div class="mb-3">
                <div class="d-flex align-items-center gap-2">
                    <p class="card-text mb-0"><strong>IP Address:</strong> {{ camera.camera_ip }}</p>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editCameraField('ip', '{{ camera.camera_ip }}')">
                        Edit IP
                    </button>
                </div>
            </div>
            <div class="mb-3">
                <div class="d-flex align-items-center gap-2">
                    <p class="card-text mb-0"><strong>Events:</strong> {{ camera.events|default(0) }}</p>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="resetEvents('{{ camera.serial }}')">
                        Reset Events
                    </button>
                </div>
            </div>
            <div class="btn-group">
                <a href="{{ url_for('add_bay', serial=camera.serial) }}" class="btn btn-primary">Add New Bay</a>
                <a href="{{ url_for('bulk_delete_bays', serial=camera.serial) }}" class="btn btn-warning">Bulk Delete Bays</a>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteCamera('{{ camera.serial }}')">Delete Camera</button>
            </div>
        </div>
    </div>

    <h2 class="mb-3">Parking Bays</h2>
    <div class="row">
        {% for bay in camera.bays %}
        <div class="col-md-6 mb-4">
            {% include 'components/bay_card.html' %}
        </div>
        {% endfor %}
    </div>

    <!-- Modals -->
    {% include 'components/modals/edit_ptz_modal.html' %}
    {% include 'components/modals/delete_camera_modal.html' %}
    {% include 'components/modals/edit_camera_modal.html' %}
    {% include 'components/modals/image_enlarge_modal.html' %}
    {% include 'components/modals/edit_bay_type_modal.html' %}
{% endblock %}

{% block extra_scripts %}
<script>
    // Get bay data from the server-side
    const baysData = {{ camera.bays|tojson|safe }};

    function resetEvents(serial) {
        if (confirm('Are you sure you want to reset the events counter for this camera?')) {
            fetch(`/camera/${serial}/events/reset`, {
                method: 'POST',
            })
            .then(response => {
                if (response.ok) {
                    window.location.reload();
                } else {
                    alert('Failed to reset events counter');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to reset events counter');
            });
        }
    }
    
    function editPTZID(bayId, currentPtzId) {
        document.getElementById('bayId').value = bayId;
        document.getElementById('oldPtzId').value = currentPtzId;
        document.getElementById('modalBayId').textContent = bayId;
        document.getElementById('currentPtzId').textContent = currentPtzId;
        document.getElementById('newPtzId').value = currentPtzId;
        
        const modal = new bootstrap.Modal(document.getElementById('editPTZIDModal'));
        modal.show();
    }

    function confirmDeleteCamera(serial) {
        const modal = new bootstrap.Modal(document.getElementById('deleteCameraModal'));
        document.getElementById('confirmDeleteSerial').textContent = serial;
        document.getElementById('deleteForm').action = `/camera/${serial}/delete`;
        modal.show();
    }

    let currentField = '';
    function editCameraField(field, currentValue) {
        currentField = field;
        const modal = new bootstrap.Modal(document.getElementById('editCameraModal'));
        const input = document.getElementById('editFieldInput');
        const label = document.getElementById('editFieldLabel');
        const help = document.getElementById('editFieldHelp');
        
        input.value = currentValue;
        
        if (field === 'serial') {
            label.textContent = 'New Serial Number';
            help.textContent = 'Enter the new serial number for the camera';
        } else {
            label.textContent = 'New IP Address';
            help.textContent = 'Enter the new IP address in format ********';
        }
        
        modal.show();
    }

    async function updateCamera(event) {
        event.preventDefault();
        const newValue = document.getElementById('editFieldInput').value;
        const currentSerial = '{{ camera.serial }}';
        
        try {
            const response = await fetch(`${window.location.protocol}//${window.location.host}/api/cameras/${currentSerial}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(
                    currentField === 'serial' 
                        ? { "new_serial": newValue }
                        : { "camera_ip": newValue }
                ),
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || 'Failed to update camera');
            }
            
            // If we updated the serial, we need to redirect to the new URL
            if (currentField === 'serial') {
                window.location.href = "{{ url_for('view_camera', serial='') }}" + newValue;
            } else {
                window.location.reload();
            }
        } catch (error) {
            alert(error.message);
        }
        
        return false;
    }
    
    function fetchBayImage(bayId) {
        // Show loading indicator
        const loadingElement = document.getElementById(`bay-image-loading-${bayId}`);
        const imageElement = document.getElementById(`bay-image-${bayId}`);
        const errorElement = document.getElementById(`bay-image-error-${bayId}`);
        
        loadingElement.style.display = 'block';
        imageElement.style.display = 'none';
        errorElement.style.display = 'none';
        
        // Make API request to get bay image
        fetch(`/api/bays/search?bay_id=${encodeURIComponent(bayId)}`)
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Failed to fetch bay image');
                    });
                }
            })
            .then(data => {
                // Hide loading, show image
                loadingElement.style.display = 'none';
                imageElement.style.display = 'block';
                
                // Convert base64 to image source
                const imageData = `data:image/jpeg;base64,${data.image}`;
                imageElement.src = imageData;
                
                // Show error message if provided by API but still show the image
                if (data.error) {
                    errorElement.textContent = data.error;
                    errorElement.style.display = 'block';
                }
            })
            .catch(error => {
                // Hide loading, show error
                loadingElement.style.display = 'none';
                errorElement.textContent = error.message;
                errorElement.style.display = 'block';
            });
    }
    
    function openImageModal(bayId) {
        const imageElement = document.getElementById(`bay-image-${bayId}`);
        const enlargedImage = document.getElementById('enlarged-image');
        const plateText = document.getElementById('plate-text');
        const plateDisplay = document.getElementById('plate-display');
        
        // Only proceed if the image is actually loaded
        if (imageElement.style.display !== 'none' && imageElement.src) {
            // Get the bay data to access plate number
            const bayData = getBayData(bayId);
            
            // Set the image source
            enlargedImage.src = imageElement.src;
            
            // Set the title with bay ID
            document.getElementById('imageEnlargeModalLabel').textContent = `Bay ${bayId} Image`;
            
            // Set plate number if available
            if (bayData && bayData.plate) {
                plateText.textContent = `Plate: ${bayData.plate}`;
                plateDisplay.style.display = 'block';
            } else {
                plateDisplay.style.display = 'none';
            }
            
            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('imageEnlargeModal'));
            modal.show();
        }
    }

    // Helper function to get bay data by bay ID
    function getBayData(bayId) {
        return baysData.find(bay => bay.bay_id === bayId);
    }

    function editBayType(bayId, ptzId, currentType) {
    document.getElementById('editTypeBayId').value = bayId;
    document.getElementById('editTypePtzId').value = ptzId;
    
    // Set current type in dropdown
    const typeSelect = document.getElementById('bayType');
    for (let i = 0; i < typeSelect.options.length; i++) {
        if (typeSelect.options[i].value === currentType) {
            typeSelect.selectedIndex = i;
            break;
        }
    }
    
    const modal = new bootstrap.Modal(document.getElementById('editBayTypeModal'));
    modal.show();
}

async function updateBayType(event) {
    event.preventDefault();
    const form = event.target;
    const bayId = document.getElementById('editTypeBayId').value;
    const ptzId = document.getElementById('editTypePtzId').value;
    const type = document.getElementById('bayType').value;
    
    try {
        const response = await fetch(`${window.location.protocol}//${window.location.host}/api/bay/type`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                serial: '{{ camera.serial }}',
                ptz_id: ptzId,
                type: type
            }),
        });
        
        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.detail || 'Failed to update bay type');
        }
        
        window.location.reload();
    } catch (error) {
        alert(error.message);
    }
}
</script>
{% endblock %}