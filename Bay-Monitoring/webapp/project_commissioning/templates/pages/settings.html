{% extends 'base.html' %}

{% set active_page = 'settings' %}

{% block title %}Settings - Parking Bay Management{% endblock %}

{% block content %}
    <h1 class="page-title">Settings</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <!-- Settings Sections -->
    <div class="row">
        <!-- General Settings -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">General Settings</h5>
                    <i class="fas fa-cog"></i>
                </div>
                <div class="card-body">
                    <form id="generalSettingsForm">
                        <div class="mb-3">
                            <label for="siteName" class="form-label">Site Name</label>
                            <input type="text" class="form-control" id="siteName" value="International Towers - Bay Monitoring">
                        </div>
                        <div class="mb-3">
                            <label for="apiUrl" class="form-label">API URL</label>
                            <input type="text" class="form-control" id="apiUrl" value="http://localhost:8000">
                        </div>
                        <div class="mb-3">
                            <label for="refreshInterval" class="form-label">Data Refresh Interval (seconds)</label>
                            <input type="number" class="form-control" id="refreshInterval" value="30" min="5">
                        </div>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Notification Settings -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Notification Settings</h5>
                    <i class="fas fa-bell"></i>
                </div>
                <div class="card-body">
                    <form id="notificationSettingsForm">
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="enableNotifications" checked>
                            <label class="form-check-label" for="enableNotifications">Enable Notifications</label>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="emailNotifications" checked>
                            <label class="form-check-label" for="emailNotifications">Email Notifications</label>
                        </div>
                        <div class="mb-3">
                            <label for="emailRecipients" class="form-label">Email Recipients</label>
                            <input type="text" class="form-control" id="emailRecipients" placeholder="<EMAIL>, <EMAIL>">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="occupancyAlerts">
                            <label class="form-check-label" for="occupancyAlerts">High Occupancy Alerts</label>
                        </div>
                        <div class="mb-3">
                            <label for="occupancyThreshold" class="form-label">Occupancy Threshold (%)</label>
                            <input type="number" class="form-control" id="occupancyThreshold" value="80" min="50" max="100">
                        </div>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- System Settings -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">System Settings</h5>
                    <i class="fas fa-server"></i>
                </div>
                <div class="card-body">
                    <form id="systemSettingsForm">
                        <div class="mb-3">
                            <label for="logLevel" class="form-label">Log Level</label>
                            <select class="form-control" id="logLevel">
                                <option value="debug">Debug</option>
                                <option value="info" selected>Info</option>
                                <option value="warning">Warning</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="dataRetention" class="form-label">Data Retention (days)</label>
                            <input type="number" class="form-control" id="dataRetention" value="30" min="1">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="enableBackups" checked>
                            <label class="form-check-label" for="enableBackups">Enable Automated Backups</label>
                        </div>
                        <div class="mb-3">
                            <label for="backupFrequency" class="form-label">Backup Frequency</label>
                            <select class="form-control" id="backupFrequency">
                                <option value="daily">Daily</option>
                                <option value="weekly" selected>Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- User Management -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">User Management</h5>
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-body">
                    <div class="mb-3 d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Users</h6>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-plus me-1"></i> Add User
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Role</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>admin</td>
                                    <td>Administrator</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>operator</td>
                                    <td>Operator</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>viewer</td>
                                    <td>Viewer</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // General Settings Form
        document.getElementById('generalSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // Simulate saving settings
            setTimeout(function() {
                alert('General settings saved successfully!');
            }, 500);
        });
        
        // Notification Settings Form
        document.getElementById('notificationSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // Simulate saving settings
            setTimeout(function() {
                alert('Notification settings saved successfully!');
            }, 500);
        });
        
        // System Settings Form
        document.getElementById('systemSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // Simulate saving settings
            setTimeout(function() {
                alert('System settings saved successfully!');
            }, 500);
        });
    });
</script>
{% endblock %}