{% extends 'base.html' %}

{% set active_page = 'cameras' %}

{% block title %}Bulk Add Cameras - Parking Bay Management{% endblock %}

{% block content %}
    <div class="d-flex align-items-center mb-4">
        <a href="{{ url_for('list_cameras') }}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="page-title mb-0">Bulk Add Cameras</h1>
    </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">CSV File Format</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">The CSV file should have the following columns:</p>
                    <pre class="p-3" style="background-color: var(--accent); border-radius: var(--radius); color: var(--foreground);">serial        camera_ip       bays
B8A44F27FE68    ************    1A,1B,1C,2A,2B,2C,2D,2E,2F
B8A44F45EF32    ************    3D,3E,3F,3G,3H,3I,4E,4F,4G
B8A44F45D668    ************    5A,5B,5C,5D,5E,5F,6A,6B,6C,6D,6E,6F</pre>
                    <p class="text-muted">Notes:</p>
                    <ul class="text-muted">
                        <li>The first line must be the header row as shown above</li>
                        <li>Fields are tab-separated</li>
                        <li>Bays are comma-separated</li>
                        <li>Bay IDs can contain letters, numbers, and dots (e.g., "1A", "C1.13")</li>
                        <li>PTZ IDs will be automatically assigned starting from 1</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Upload File</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        </div>
                        
                        <div class="d-flex mt-4">
                            <button type="submit" class="btn btn-primary me-2">Upload and Add Cameras</button>
                            <a href="{{ url_for('list_cameras') }}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}