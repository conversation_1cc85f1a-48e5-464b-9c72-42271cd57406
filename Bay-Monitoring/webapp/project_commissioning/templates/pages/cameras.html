{% extends 'base.html' %}

{% set active_page = 'cameras' %}

{% block title %}Cameras - Parking Bay Management{% endblock %}

{% block content %}
    <h1 class="page-title">Parking Dashboard</h1>
    
<!-- Occupancy Progress Bar -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h5 class="mb-0">Bay Occupancy</h5>
            <span class="badge badge-purple" id="occupancy-stats">
                <!-- Will be filled by JavaScript -->
            </span>
        </div>
        <div class="progress" style="height: 24px;">
            <div class="progress-bar" id="occupancy-bar"
                 role="progressbar" 
                 style="width: 0%;" 
                 aria-valuenow="0" 
                 aria-valuemin="0" 
                 aria-valuemax="100">
                <span id="occupancy-text" style="font-weight: 600; text-shadow: 0px 0px 3px rgba(0,0,0,0.7);">0%</span>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate occupancy stats
    let totalBays = 0;
    let occupiedBays = 0;
    
    // Count bays and occupied bays
    {% for camera in cameras %}
        totalBays += {{ camera.bays|length }};
        {% for bay in camera.bays %}
            {% if bay.status == 'occupied' %}
                occupiedBays++;
            {% endif %}
        {% endfor %}
    {% endfor %}
    
    // Calculate occupancy percentage
    const occupancyPercentage = totalBays > 0 ? Math.round((occupiedBays / totalBays) * 100) : 0;
    
    // Update the progress bar
    const occupancyBar = document.getElementById('occupancy-bar');
    const occupancyText = document.getElementById('occupancy-text');
    
    occupancyBar.style.width = occupancyPercentage + '%';
    occupancyText.textContent = occupancyPercentage + '%';
    occupancyBar.setAttribute('aria-valuenow', occupancyPercentage);
    
    // Update color based on occupancy
    if (occupancyPercentage > 80) {
        occupancyBar.classList.add('bg-danger');
        occupancyText.style.color = '#ffffff';
    } else if (occupancyPercentage > 50) {
        occupancyBar.classList.add('bg-warning');
        occupancyText.style.color = '#000000';
    } else {
        occupancyBar.classList.add('bg-success');
        occupancyText.style.color = '#ffffff';
    }
    
    // Update stats text
    document.getElementById('occupancy-stats').textContent = 
        `${occupiedBays} of ${totalBays} bays occupied (${occupancyPercentage}%)`;
});
</script>
    
<!-- Search Section -->
<div class="search-container">
    <div class="search-card">
        <div class="search-title">
            <i class="fas fa-search me-2"></i> Search by License Plate
        </div>
        <div class="input-group">
            <input type="text" id="plateSearch" class="form-control" placeholder="Enter license plate number" autocomplete="off">
            <button class="btn btn-primary" type="button" onclick="searchPlate()">
                Search
            </button>
        </div>
        <!-- Dropdown for plate search results -->
        <div id="plateSearchDropdown" class="search-dropdown" style="display: none;">
            <div id="plateDropdownLoading" class="p-2" style="display: none;">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Searching...</span>
            </div>
            <div id="plateDropdownContent"></div>
        </div>
    </div>
    
    <div class="search-card">
        <div class="search-title">
            <i class="fas fa-car me-2"></i> Search by Bay ID
        </div>
        <div class="input-group">
            <input type="text" id="baySearch" class="form-control" placeholder="Enter bay ID (e.g. 58A)">
            <button class="btn btn-primary" type="button" onclick="searchBay()">
                Search
            </button>
        </div>
    </div>
    
    <div class="search-card">
        <div class="search-title">
            <i class="fas fa-user me-2"></i> Search by Name/Tenant ID
        </div>
        <div class="input-group">
            <input type="text" id="tenantSearch" class="form-control" placeholder="Enter name or tenant ID" autocomplete="off">
            <button class="btn btn-primary" type="button" onclick="searchTenant()">
                Search
            </button>
        </div>
        <!-- Dropdown for tenant search results -->
        <div id="tenantSearchDropdown" class="search-dropdown" style="display: none;">
            <div id="tenantDropdownLoading" class="p-2" style="display: none;">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Searching...</span>
            </div>
            <div id="tenantDropdownContent"></div>
        </div>
    </div>
</div>
    
    <!-- Actions Section -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="mb-3">Quick Actions</h5>
            <div class="row">
                <div class="col-md-3 mb-2">
                    <a href="{{ url_for('add_camera') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-plus me-2"></i> Add Camera
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="{{ url_for('bulk_add_cameras') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-upload me-2"></i> Bulk Add Cameras
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <button type="button" class="btn btn-outline-secondary w-100" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                        <i class="fas fa-users me-2"></i> Update Tenants
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <div class="d-flex">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary flex-grow-1 me-2">
                            <i class="fas fa-cog me-2"></i> Settings
                        </a>
                        <button type="button" class="btn btn-outline-secondary flex-grow-1">
                            <i class="fas fa-file-export me-2"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Camera List -->
    <h4 class="mb-3">Camera List</h4>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <div class="row">
        {% for camera in cameras %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card camera-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ camera.serial }}</h5>
                    <span class="badge badge-purple">{{ camera.bays|length }} Bays</span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">IP Address</small>
                        <p class="mb-0">{{ camera.camera_ip }}</p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Events</small>
                        <div class="d-flex justify-content-between align-items-center">
                            <p class="mb-0">{{ camera.events|default(0) }}</p>
                            <button class="btn btn-sm btn-outline-secondary" onclick="resetEvents('{{ camera.serial }}')">
                                Reset
                            </button>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <small class="text-muted">Bay Status</small>
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleBays('{{ camera.serial }}')">
                            <span id="toggle-text-{{ camera.serial }}">Show</span>
                        </button>
                    </div>
                    <div id="bays-{{ camera.serial }}" class="table-responsive" style="display: none;">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Bay ID</th>
                                    <th>Status</th>
                                    <th>Type</th>
                                    <th>Plate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bay in camera.bays %}
                                <tr>
                                    <td>{{ bay.bay_id }}</td>
                                    <td>
                                        {% if bay.status == 'vacant' %}
                                        <span class="badge badge-green">Vacant</span>
                                        {% else %}
                                        <span class="badge badge-red">Occupied</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ bay.type or 'car' }}</td>
                                    <td>{{ bay.plate or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('view_camera', serial=camera.serial) }}" class="btn btn-sm btn-primary">
                        View Details
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% endblock %}