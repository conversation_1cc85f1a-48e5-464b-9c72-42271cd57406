{% extends 'base.html' %}

{% set active_page = 'cameras' %}

{% block title %}Add Camera - Parking Bay Management{% endblock %}

{% block content %}
    <div class="d-flex align-items-center mb-4">
        <a href="{{ url_for('list_cameras') }}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="page-title mb-0">Add New Camera</h1>
    </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="serial" class="form-label">Camera Serial Number</label>
                            <input type="text" class="form-control" id="serial" name="serial" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="camera_ip" class="form-label">Camera IP Address</label>
                            <input type="text" class="form-control" id="camera_ip" name="camera_ip" 
                                   pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$" 
                                   placeholder="e.g., *************" required>
                        </div>
                        
                        <div class="d-flex mt-4">
                            <button type="submit" class="btn btn-primary me-2">Add Camera</button>
                            <a href="{{ url_for('list_cameras') }}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}