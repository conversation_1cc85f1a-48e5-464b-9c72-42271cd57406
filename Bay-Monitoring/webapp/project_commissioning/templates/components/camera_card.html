<div class="card camera-card h-100">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{ camera.serial }}</h5>
        <span class="badge badge-purple">{{ camera.bays|length }} Bays</span>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <small class="text-muted">IP Address</small>
            <p class="mb-0">{{ camera.camera_ip }}</p>
        </div>
        <div class="mb-3">
            <small class="text-muted">Events</small>
            <div class="d-flex justify-content-between align-items-center">
                <p class="mb-0">{{ camera.events|default(0) }}</p>
                <button class="btn btn-sm btn-outline-secondary" onclick="resetEvents('{{ camera.serial }}')">
                    Reset
                </button>
            </div>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <small class="text-muted">Bay Status</small>
            <button class="btn btn-sm btn-outline-secondary" onclick="toggleBays('{{ camera.serial }}')">
                <span id="toggle-text-{{ camera.serial }}">Show</span>
            </button>
        </div>
        <div id="bays-{{ camera.serial }}" class="table-responsive" style="display: none;">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Bay ID</th>
                        <th>Status</th>
                        <th>Plate</th>
                    </tr>
                </thead>
                <tbody>
                    {% for bay in camera.bays %}
                    <tr>
                        <td>{{ bay.bay_id }}</td>
                        <td>
                            {% if bay.status == 'vacant' %}
                            <span class="badge badge-green">Vacant</span>
                            {% else %}
                            <span class="badge badge-red">Occupied</span>
                            {% endif %}
                        </td>
                        <td>{{ bay.plate or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <a href="{{ url_for('view_camera', serial=camera.serial) }}" class="btn btn-sm btn-primary">
            View Details
        </a>
    </div>
</div>