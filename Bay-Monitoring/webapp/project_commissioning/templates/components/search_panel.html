<div class="search-container">
    <div class="search-card">
        <div class="search-title">
            <i class="fas fa-search me-2"></i> Search by License Plate
        </div>
        <div class="input-group">
            <input type="text" id="plateSearch" class="form-control" placeholder="Enter license plate number" autocomplete="off">
            <button class="btn btn-primary" type="button" onclick="searchPlate()">
                Search
            </button>
        </div>
        <!-- New dropdown for search results -->
        <div id="plateSearchDropdown" class="search-dropdown" style="display: none;">
            <div id="plateDropdownLoading" class="p-2" style="display: none;">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Searching...</span>
            </div>
            <div id="plateDropdownContent"></div>
        </div>
    </div>
    
    <div class="search-card">
        <div class="search-title">
            <i class="fas fa-car me-2"></i> Search by Bay ID
        </div>
        <div class="input-group">
            <input type="text" id="baySearch" class="form-control" placeholder="Enter bay ID (e.g. 58A)">
            <button class="btn btn-primary" type="button" onclick="searchBay()">
                Search
            </button>
        </div>
    </div>
    <div class="search-container position-relative me-3">
        <label for="tenantSearch" class="form-label">Search by Name/Tenant ID</label>
        <input type="text" class="form-control" id="tenantSearch" placeholder="Enter name or tenant ID...">
        
        <!-- Dropdown for search results -->
        <div id="tenantSearchDropdown" class="search-dropdown" style="display: none;">
            <div id="tenantDropdownLoading" class="p-3 text-center">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Searching...</span>
            </div>
            <div id="tenantDropdownContent" class="dropdown-content" style="display: none;">
                <!-- Results will be populated here dynamically -->
            </div>
        </div>
    </div>
</div>