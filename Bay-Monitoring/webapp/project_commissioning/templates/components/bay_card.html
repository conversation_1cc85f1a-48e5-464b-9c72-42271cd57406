<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Bay {{ bay.bay_id }}</h5>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary" type="button" id="dropdownMenuButton-{{ bay.bay_id }}" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-ellipsis-v"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton-{{ bay.bay_id }}" style="min-width: 250px;">
                <!-- Update Tenant -->
                <li class="px-3 py-2">
                    <form action="{{ url_for('update_bay', serial=camera.serial, ptz_id=bay.ptz_id) }}" method="POST">
                        <div class="mb-2"><small class="text-muted">Update Tenant</small></div>
                        <div class="input-group input-group-sm mb-2">
                            <input type="text" class="form-control" name="tenant" placeholder="Tenant name" value="{{ bay.tenant or '' }}">
                            <button type="submit" class="btn btn-outline-primary">Update</button>
                        </div>
                    </form>
                </li>

                <!-- Update Type -->
                <li class="px-3 py-2">
                    <form action="{{ url_for('update_bay', serial=camera.serial, ptz_id=bay.ptz_id) }}" method="POST">
                        <!-- Hidden tenant field to avoid KeyError in update_bay -->
                        <input type="hidden" name="tenant" value="{{ bay.tenant or '' }}">
                        
                        <div class="mb-2"><small class="text-muted">Update Type</small></div>
                        <div class="input-group input-group-sm mb-2">
                            <select class="form-select" name="type" required>
                                <option value="car" {% if bay.type == 'car' %}selected{% endif %}>Car</option>
                                <option value="car(small)" {% if bay.type == 'car(small)' %}selected{% endif %}>Car (Small)</option>
                                <option value="car(EV)" {% if bay.type == 'car(EV)' %}selected{% endif %}>Car (EV)</option>
                                <option value="bike" {% if bay.type == 'bike' %}selected{% endif %}>Bike</option>
                            </select>
                            <button type="submit" class="btn btn-outline-primary">Update</button>
                        </div>
                    </form>
                </li>

                <li><hr class="dropdown-divider"></li>

                <!-- Edit PTZ ID -->
                <li>
                    <button class="dropdown-item" type="button" onclick="editPTZID('{{ bay.bay_id }}', '{{ bay.ptz_id }}')">
                        <i class="fas fa-edit me-2"></i> Edit PTZ ID
                    </button>
                </li>

                <!-- Delete Bay -->
                <li>
                    <form action="{{ url_for('delete_bay', serial=camera.serial, ptz_id=bay.ptz_id) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete bay {{ bay.bay_id }}?');">
                        <button type="submit" class="dropdown-item text-danger">
                            <i class="fas fa-trash me-2"></i> Delete Bay
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p class="card-text">
                    <strong>Status:</strong> 
                    {% if bay.status == 'vacant' %}
                        <span class="badge badge-green">Vacant</span>
                    {% else %}
                        <span class="badge badge-red">Occupied</span>
                    {% endif %}
                </p>
                {% if bay.time_parked %}
                <p class="card-text"><strong>Time Parked:</strong> 
                    {% if bay.time_parked is string %}
                        {% set dt = bay.time_parked|to_sydney_time %}
                        {{ dt.strftime('%I:%M%p')|lower }}, {{ dt.day }}{{ dt.day|ordinal_suffix }} {{ dt.strftime('%b %y') }}
                    {% else %}
                        {{ bay.time_parked.strftime('%I:%M%p')|lower }}, {{ bay.time_parked.day }}{{ bay.time_parked.day|ordinal_suffix }} {{ bay.time_parked.strftime('%b %y') }}
                    {% endif %}
                </p>
                {% endif %}
                <p class="card-text"><strong>Plate:</strong> {{ bay.plate or 'No plate detected' }}</p>
                <p class="card-text"><strong>Cardholder:</strong> {{ bay.cardholderName or 'Not assigned' }}</p>
                <p class="card-text"><strong>Unique Cardholder ID:</strong> {{ bay.tenantID or 'Not assigned' }}</p>
                <p class="card-text"><strong>Parked Tenant:</strong> {{ bay.parked_tenant or 'Not assigned' }}</p>
                <p class="card-text"><strong>Assigned Tenant:</strong> {{ bay.tenant or 'Not assigned' }}</p>
                <p class="card-text"><strong>Type:</strong> {{ bay.type or 'car' }}</p>
                <p class="card-text"><strong>PTZ ID:</strong> {{ bay.ptz_id }}</p>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary w-100" 
                            onclick="fetchBayImage('{{ bay.bay_id }}')">
                        <i class="fas fa-camera me-2"></i> Get Latest Image
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <div id="bay-image-container-{{ bay.bay_id }}" class="text-center">
                    <div id="bay-image-loading-{{ bay.bay_id }}" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Fetching image...</p>
                    </div>
                    <img id="bay-image-{{ bay.bay_id }}" class="img-fluid rounded cursor-pointer" 
                         style="max-height: 200px; display: none; cursor: pointer;" 
                         src="" alt="Bay image"
                         onclick="openImageModal('{{ bay.bay_id }}')">
                    <div id="bay-image-error-{{ bay.bay_id }}" class="alert alert-danger mt-2" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>