<div class="modal fade search-modal" id="bulkUpdateModal" tabindex="-1" aria-labelledby="bulkUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkUpdateModalLabel">Bulk Update Tenants</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('bulk_update_tenants') }}" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <p>Upload a CSV file to update tenant information for multiple bays. The CSV should have two columns: 'bay' and 'tenant'.</p>
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text" style="color: var(--muted-foreground);">
                            Example CSV format:<br>
                            bay,tenant<br>
                            10A,PWC<br>
                            10B,PWC<br>
                            10C,PWC
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload and Update</button>
                </div>
            </form>
        </div>
    </div>
</div>