from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from pymongo import MongoClient
import os
import requests
import csv
import io
from datetime import datetime
import pytz

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# MongoDB configuration
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
client = MongoClient(MONGO_URI)
db = client['parking_db']
cameras_collection = db['cameras']

# FastAPI backend URL
API_URL = "http://localhost:8000"

# Add these filters to your Flask app
@app.template_filter('to_sydney_time')
def to_sydney_time(value):
    """Convert a timestamp string to a Sydney timezone datetime object."""
    if not value:
        return None
        
    sydney_tz = pytz.timezone('Australia/Sydney')
    
    if isinstance(value, str):
        try:
            # Try to parse ISO format with timezone info
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            
            # If the datetime has timezone info, convert to Sydney time
            if dt.tzinfo is not None:
                return dt.astimezone(sydney_tz)
            
            # If no timezone info, assume it's already Sydney time
            return sydney_tz.localize(dt)
            
        except ValueError:
            # If parsing fails, try other common formats
            for fmt in ['%Y-%m-%dT%H:%M:%S', '%Y-%m-%d %H:%M:%S']:
                try:
                    dt = datetime.strptime(value, fmt)
                    return sydney_tz.localize(dt)
                except ValueError:
                    continue
            
            # If all parsing attempts fail, return the original string
            return value
    
    # If it's already a datetime object
    if value.tzinfo is not None:
        return value.astimezone(sydney_tz)
    return sydney_tz.localize(value)

@app.template_filter('ordinal_suffix')
def ordinal_suffix(day):
    """Return the ordinal suffix for a day number (st, nd, rd, th)."""
    if 10 <= day % 100 <= 20:
        return 'th'
    else:
        return {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')

# CORS settings
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@app.route('/')
def index():
    try:
        response = requests.get(f"{API_URL}/cameras/")
        cameras = response.json()
        return render_template('pages/cameras.html', cameras=cameras, active_page='dashboard')
    except requests.RequestException as e:
        flash(f'Error fetching cameras: {str(e)}', 'danger')
        return render_template('pages/cameras.html', cameras=[], active_page='dashboard')

# API proxy route for bay search  
@app.route('/api/bays/search')
def search_bay_api():
    bay_id = request.args.get('bay_id')
    if not bay_id:
        return jsonify({"detail": "Bay ID is required"}), 400
    
    try:
        # Call the FastAPI endpoint
        response = requests.get(f"{API_URL}/api/bays/search", params={"bay_id": bay_id})
        
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            # Pass through the error from the API
            return jsonify({"detail": response.json().get("detail", "Bay not found")}), response.status_code
    except requests.RequestException as e:
        return jsonify({"detail": f"Error searching for bay: {str(e)}"}), 500

@app.route('/plates/search')
def search_plate_api():
    plate = request.args.get('plate')
    if not plate:
        return jsonify({"detail": "License plate is required"}), 400
    
    try:
        # Call the FastAPI endpoint
        response = requests.get(f"{API_URL}/plates/search", params={"plate": plate})
        
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            # Pass through the error from the API
            return jsonify({"detail": response.json().get("detail", "Plate not found")}), response.status_code
    except requests.RequestException as e:
        return jsonify({"detail": f"Error searching for plate: {str(e)}"}), 500

@app.route('/cameras')
def list_cameras():
    try:
        response = requests.get(f"{API_URL}/cameras/")
        cameras = response.json()
        return render_template('pages/cameras.html', cameras=cameras, active_page='cameras')
    except requests.RequestException as e:
        flash(f'Error fetching cameras: {str(e)}', 'danger')
        return render_template('pages/cameras.html', cameras=[], active_page='cameras')

@app.route('/add_camera', methods=['GET', 'POST'])
def add_camera():
    if request.method == 'POST':
        camera_data = {
            "serial": request.form['serial'],
            "camera_ip": request.form['camera_ip'],
            "bays": []
        }
        try:
            response = requests.post(f"{API_URL}/cameras/", json=camera_data)
            if response.status_code == 200:
                flash('Camera added successfully!', 'success')
                return redirect(url_for('list_cameras'))
            else:
                flash(f'Error adding camera: {response.json().get("detail", "Unknown error")}', 'danger')
        except requests.RequestException as e:
            flash(f'Error adding camera: {str(e)}', 'danger')
    return render_template('pages/add_camera.html', active_page='cameras')

@app.route('/camera/<serial>')
def view_camera(serial):
    try:
        response = requests.get(f"{API_URL}/cameras/{serial}")
        camera = response.json()
        return render_template('pages/view_camera.html', camera=camera, active_page='cameras')
    except requests.RequestException as e:
        flash(f'Error fetching camera details: {str(e)}', 'danger')
        return redirect(url_for('list_cameras'))

@app.route('/camera/<serial>/add_bay', methods=['GET', 'POST'])
def add_bay(serial):
    if request.method == 'POST':
        bay_data = [{
            "bay_id": request.form['bay_id'],
            "ptz_id": request.form['ptz_id'],
            "tenant": request.form['tenant'],
            "type": request.form['type'],
            "status": "vacant"
        }]
        try:
            response = requests.post(f"{API_URL}/cameras/{serial}/bays", json=bay_data)
            if response.status_code == 200:
                flash('Bay added successfully!', 'success')
                return redirect(url_for('view_camera', serial=serial))
            else:
                flash(f'Error adding bay: {response.json().get("detail", "Unknown error")}', 'danger')
        except requests.RequestException as e:
            flash(f'Error adding bay: {str(e)}', 'danger')
    return render_template('pages/add_bay.html', serial=serial, active_page='cameras')

@app.route('/camera/<serial>/update_bay/<ptz_id>', methods=['POST'])
def update_bay(serial, ptz_id):
    try:
        # Create data object with both tenant and type
        data = {
            "ptz_id": ptz_id,
            "tenant": request.form['tenant']
        }
        
        # Update tenant
        tenant_response = requests.patch(f"{API_URL}/cameras/{serial}/bays/tenant", params=data)
        if tenant_response.status_code != 200:
            flash(f'Error updating bay tenant: {tenant_response.json().get("detail", "Unknown error")}', 'danger')
            return redirect(url_for('view_camera', serial=serial))
            
        # If type field is in the form data, update it as well
        if 'type' in request.form:
            type_data = {
                "ptz_id": ptz_id,
                "type": request.form['type']
            }
            type_response = requests.patch(f"{API_URL}/cameras/{serial}/bays", params=type_data)
            if type_response.status_code != 200:
                flash(f'Error updating bay type: {type_response.json().get("detail", "Unknown error")}', 'danger')
                return redirect(url_for('view_camera', serial=serial))
                
        flash('Bay updated successfully!', 'success')
    except requests.RequestException as e:
        flash(f'Error updating bay: {str(e)}', 'danger')
    return redirect(url_for('view_camera', serial=serial))

@app.route('/camera/<serial>/update_ptz_id', methods=['POST'])
def update_ptz_id(serial):
    try:
        bay_id = request.form['bay_id']
        old_ptz_id = request.form['old_ptz_id']
        new_ptz_id = request.form['new_ptz_id']

        # First, check if the new PTZ ID is already in use
        camera_response = requests.get(f"{API_URL}/cameras/{serial}")
        if camera_response.status_code != 200:
            flash('Error fetching camera details', 'danger')
            return redirect(url_for('view_camera', serial=serial))
        
        camera = camera_response.json()
        
        # Check if new PTZ ID is already in use by another bay
        for bay in camera['bays']:
            if bay['ptz_id'] == new_ptz_id and bay['bay_id'] != bay_id:
                flash(f'PTZ ID {new_ptz_id} is already in use by bay {bay["bay_id"]}', 'danger')
                return redirect(url_for('view_camera', serial=serial))
        
        # Update the PTZ ID
        response = requests.patch(
            f"{API_URL}/cameras/{serial}/bays/ptz",
            json={
                "old_ptz_id": old_ptz_id,
                "new_ptz_id": new_ptz_id,
                "bay_id": bay_id
            }
        )
        
        if response.status_code == 200:
            flash('PTZ ID updated successfully!', 'success')
        else:
            flash(f'Error updating PTZ ID: {response.json().get("detail", "Unknown error")}', 'danger')
    except requests.RequestException as e:
        flash(f'Error updating PTZ ID: {str(e)}', 'danger')
    return redirect(url_for('view_camera', serial=serial))

@app.route('/camera/<serial>/delete_bay/<ptz_id>', methods=['POST'])
def delete_bay(serial, ptz_id):
    try:
        # First get the camera details to verify the bay exists
        camera_response = requests.get(f"{API_URL}/cameras/{serial}")
        if camera_response.status_code != 200:
            flash('Error fetching camera details', 'danger')
            return redirect(url_for('list_cameras'))
        
        camera = camera_response.json()
        bay_exists = any(bay['ptz_id'] == ptz_id for bay in camera.get('bays', []))
        
        if not bay_exists:
            flash('Bay not found', 'danger')
            return redirect(request.referrer or url_for('view_camera', serial=serial))
        
        # Delete the bay using the correct endpoint
        response = requests.delete(f"{API_URL}/cameras/{serial}/bay", params={"ptz_id": ptz_id})
        if response.status_code == 200:
            flash('Bay deleted successfully!', 'success')
        else:
            flash(f'Error deleting bay: {response.json().get("detail", "Unknown error")}', 'danger')
    except requests.RequestException as e:
        flash(f'Error deleting bay: {str(e)}', 'danger')
    return redirect(request.referrer or url_for('view_camera', serial=serial))

@app.route('/cameras/<serial>', methods=['PATCH'])
def update_camera_details(serial):
    try:
        # Get the update data from the request
        data = request.get_json()
        if not data:
            return jsonify({"detail": "No data provided"}), 400
        
        # Forward the request to the API
        response = requests.patch(
            f"{API_URL}/cameras/{serial}",
            json=data
        )
        
        if response.status_code == 200:
            return jsonify({"message": "Camera updated successfully", "new_serial": data.get("new_serial", serial)})
        else:
            return jsonify(response.json()), response.status_code
    except requests.RequestException as e:
        return jsonify({"detail": str(e)}), 500

@app.route('/camera/<serial>/delete', methods=['POST'])
def delete_camera(serial):
    try:
        response = requests.delete(f"{API_URL}/cameras/{serial}")
        if response.status_code == 200:
            flash('Camera deleted successfully!', 'success')
            return redirect(url_for('list_cameras'))
        else:
            flash(f'Error deleting camera: {response.json().get("detail", "Unknown error")}', 'danger')
            return redirect(url_for('view_camera', serial=serial))
    except requests.RequestException as e:
        flash(f'Error deleting camera: {str(e)}', 'danger')
        return redirect(url_for('view_camera', serial=serial))

@app.route('/camera/<serial>/events/reset', methods=['POST'])
def reset_events(serial):
    try:
        response = requests.patch(f"{API_URL}/cameras/{serial}/events/reset", json={})
        if response.status_code == 200:
            flash('Events counter reset successfully!', 'success')
            return jsonify({"message": "Events counter reset successfully"})
        else:
            flash(f'Error resetting events counter: {response.json().get("detail", "Unknown error")}', 'danger')
            return jsonify({"error": "Failed to reset events counter"}), 500
    except requests.RequestException as e:
        flash(f'Error resetting events counter: {str(e)}', 'danger')
        return jsonify({"error": str(e)}), 500

@app.route('/camera/<serial>/bulk_delete_bays', methods=['GET', 'POST'])
def bulk_delete_bays(serial):
    try:
        # Get camera details for the template
        camera_response = requests.get(f"{API_URL}/cameras/{serial}")
        if camera_response.status_code != 200:
            flash('Error fetching camera details', 'danger')
            return redirect(url_for('list_cameras'))
        
        camera = camera_response.json()
        
        if request.method == 'POST':
            if 'csv_file' not in request.files:
                flash('No file uploaded', 'danger')
                return redirect(request.url)
            
            file = request.files['csv_file']
            if file.filename == '':
                flash('No file selected', 'danger')
                return redirect(request.url)
            
            if not file.filename.endswith('.csv'):
                flash('File must be a CSV', 'danger')
                return redirect(request.url)
            
            try:
                # Read CSV file
                content = file.stream.read().decode("UTF8")
                stream = io.StringIO(content, newline=None)
                csv_reader = csv.DictReader(stream)
                
                if 'bay_id' not in csv_reader.fieldnames:
                    flash('CSV file must contain "bay_id" column', 'danger')
                    return redirect(request.url)
                
                success_count = 0
                error_count = 0
                errors = []
                not_found = []
                
                # Create a map of bay_id to ptz_id
                bay_map = {bay['bay_id']: bay['ptz_id'] for bay in camera['bays']}
                
                for row in csv_reader:
                    bay_id = row['bay_id'].strip()
                    if bay_id in bay_map:
                        try:
                            response = requests.delete(
                                f"{API_URL}/cameras/{serial}/bay",
                                params={"ptz_id": bay_map[bay_id]}
                            )
                            if response.status_code == 200:
                                success_count += 1
                            else:
                                error_count += 1
                                errors.append(f"Bay {bay_id}: {response.json().get('detail', 'Unknown error')}")
                        except requests.RequestException as e:
                            error_count += 1
                            errors.append(f"Bay {bay_id}: {str(e)}")
                    else:
                        not_found.append(bay_id)
                
                if success_count > 0:
                    flash(f'Successfully deleted {success_count} bay(s)!', 'success')
                if error_count > 0:
                    for error in errors:
                        flash(error, 'danger')
                if not_found:
                    flash(f'Bays not found: {", ".join(not_found)}', 'warning')
                
                return redirect(url_for('view_camera', serial=serial))
                
            except Exception as e:
                flash(f'Error processing CSV file: {str(e)}', 'danger')
                return redirect(request.url)
        
        return render_template('pages/bulk_delete_bays.html', camera=camera, active_page='cameras')
        
    except requests.RequestException as e:
        flash(f'Error: {str(e)}', 'danger')
        return redirect(url_for('list_cameras'))

@app.route('/settings')
def settings():
    try:
        return render_template('pages/settings.html', active_page='settings')
    except Exception as e:
        flash(f'Error loading settings page: {str(e)}', 'danger')
        return redirect(url_for('index'))

@app.route('/bulk_add_cameras', methods=['GET', 'POST'])
def bulk_add_cameras():
    if request.method == 'POST':
        if 'csv_file' not in request.files:
            flash('No file uploaded', 'danger')
            return redirect(request.url)
        
        file = request.files['csv_file']
        if file.filename == '':
            flash('No file selected', 'danger')
            return redirect(request.url)
        
        if not file.filename.endswith('.csv'):
            flash('File must be a CSV', 'danger')
            return redirect(request.url)
        
        try:
            # Read CSV file
            content = file.stream.read().decode("UTF8")
            # Replace tabs with commas for CSV reader
            content = content.replace('\t', ',')
            stream = io.StringIO(content, newline=None)
            csv_reader = csv.DictReader(stream)
            
            success_count = 0
            error_count = 0
            errors = []
            
            for row in csv_reader:
                if 'serial' not in row or 'camera_ip' not in row or 'bays' not in row:
                    flash('CSV file must contain "serial", "camera_ip", and "bays" columns', 'danger')
                    return redirect(request.url)
                
                # Split bays string into list and clean up each bay ID
                bays_str = row['bays'].strip()
                bay_ids = [bay.strip() for bay in bays_str.split(',') if bay.strip()]
                
                # Create bays list with auto-incrementing PTZ IDs starting from 2
                bays = []
                next_ptz_id = 2  # Start from 2 as 1 is home position
                
                for bay_id in bay_ids:
                    bays.append({
                        "bay_id": bay_id,
                        "ptz_id": str(next_ptz_id),  # Always start from 2 for first bay
                        "tenant": "",
                        "status": "vacant"
                    })
                    next_ptz_id += 1
                
                camera_data = {
                    "serial": row['serial'].strip(),
                    "camera_ip": row['camera_ip'].strip(),
                    "bays": bays
                }
                
                try:
                    # First try to add the camera
                    response = requests.post(f"{API_URL}/cameras/", json=camera_data)
                    
                    if response.status_code == 200:
                        success_count += 1
                    else:
                        # If camera already exists, check existing bays and add only new ones
                        try:
                            # Get existing camera details
                            camera_response = requests.get(f"{API_URL}/cameras/{camera_data['serial']}")
                            if camera_response.status_code != 200:
                                error_count += 1
                                errors.append(f"Camera {camera_data['serial']}: Error fetching camera details")
                                continue

                            existing_camera = camera_response.json()
                            existing_bay_ids = {bay['bay_id'] for bay in existing_camera.get('bays', [])}
                            
                            # Filter out bays that already exist
                            new_bays = []
                            skipped_bays = []
                            
                            # Find the highest existing PTZ ID
                            highest_ptz = 1  # Start at 1 since it's the home position
                            for bay in existing_camera.get('bays', []):
                                try:
                                    ptz_num = int(bay.get('ptz_id', '1'))
                                    highest_ptz = max(highest_ptz, ptz_num)
                                except ValueError:
                                    continue
                            
                            next_ptz_id = max(2, highest_ptz + 1)  # Ensure we start at least from 2
                            
                            for bay in bays:
                                if bay['bay_id'] in existing_bay_ids:
                                    skipped_bays.append(bay['bay_id'])
                                else:
                                    bay['ptz_id'] = str(next_ptz_id)
                                    new_bays.append(bay)
                                    next_ptz_id += 1
                            
                            if skipped_bays:
                                flash(f"Camera {camera_data['serial']}: Skipped existing bays: {', '.join(skipped_bays)}", 'warning')
                            
                            if new_bays:
                                bay_response = requests.post(
                                    f"{API_URL}/cameras/{camera_data['serial']}/bays",
                                    json=new_bays
                                )
                                if bay_response.status_code == 200:
                                    success_count += 1
                                    flash(f"Camera {camera_data['serial']}: Added {len(new_bays)} new bays", 'success')
                                else:
                                    error_count += 1
                                    errors.append(f"Camera {camera_data['serial']}: {bay_response.json().get('detail', 'Error adding bays')}")
                            else:
                                flash(f"Camera {camera_data['serial']}: No new bays to add", 'info')
                                
                        except requests.RequestException as e:
                            error_count += 1
                            errors.append(f"Camera {camera_data['serial']}: Error adding bays - {str(e)}")
                except requests.RequestException as e:
                    error_count += 1
                    errors.append(f"Camera {camera_data['serial']}: {str(e)}")
            
            if success_count > 0:
                flash(f'Successfully processed {success_count} camera(s)!', 'success')
            if error_count > 0:
                for error in errors:
                    flash(error, 'danger')
            
            return redirect(url_for('list_cameras'))
            
        except Exception as e:
            flash(f'Error processing CSV file: {str(e)}', 'danger')
            return redirect(request.url)
    
    return render_template('pages/bulk_add_cameras.html', active_page='cameras')

@app.route('/bulk_update_tenants', methods=['POST'])
def bulk_update_tenants():
    if 'csv_file' not in request.files:
        flash('No file uploaded', 'danger')
        return redirect(url_for('list_cameras'))
    
    file = request.files['csv_file']
    if file.filename == '':
        flash('No file selected', 'danger')
        return redirect(url_for('list_cameras'))
    
    if not file.filename.endswith('.csv'):
        flash('File must be a CSV', 'danger')
        return redirect(url_for('list_cameras'))
    
    try:
        # Read CSV file
        content = file.stream.read().decode("UTF8")
        # Replace tabs with commas for CSV reader
        content = content.replace('\t', ',')
        stream = io.StringIO(content, newline=None)
        
        # First try with comma delimiter
        csv_reader = csv.DictReader(stream)
        fieldnames = [name.strip().lower() for name in csv_reader.fieldnames] if csv_reader.fieldnames else []
        
        if 'bay' not in fieldnames or 'tenant' not in fieldnames:
            # Reset stream and try with tab delimiter
            stream.seek(0)
            csv_reader = csv.DictReader(stream, delimiter='\t')
            fieldnames = [name.strip().lower() for name in csv_reader.fieldnames] if csv_reader.fieldnames else []
            
            if 'bay' not in fieldnames or 'tenant' not in fieldnames:
                flash('CSV file must contain "bay" and "tenant" columns', 'danger')
                return redirect(url_for('list_cameras'))
        
        # Get all cameras first
        cameras_response = requests.get(f"{API_URL}/cameras/")
        if cameras_response.status_code != 200:
            flash('Error fetching cameras', 'danger')
            return redirect(url_for('list_cameras'))
        
        cameras = cameras_response.json()
        
        success_count = 0
        not_found = []
        errors = []
        
        # Create a map of bay_id to camera serial and ptz_id
        bay_map = {}
        for camera in cameras:
            for bay in camera.get('bays', []):
                bay_map[bay['bay_id']] = {
                    'serial': camera['serial'],
                    'ptz_id': bay['ptz_id']
                }
        
        # Process each row in the CSV
        for row in csv_reader:
            # Get the bay and tenant values using case-insensitive column names
            bay_id = next((row[col] for col in row.keys() if col.strip().lower() == 'bay'), None)
            tenant = next((row[col] for col in row.keys() if col.strip().lower() == 'tenant'), None)
            
            if not bay_id or not tenant:
                continue
                
            bay_id = bay_id.strip()
            tenant = tenant.strip()
            
            if bay_id in bay_map:
                try:
                    data = {
                        "ptz_id": bay_map[bay_id]['ptz_id'],
                        "tenant": tenant
                    }
                    response = requests.patch(
                        f"{API_URL}/cameras/{bay_map[bay_id]['serial']}/bays/tenant",
                        params=data
                    )
                    if response.status_code == 200:
                        success_count += 1
                    else:
                        errors.append(f"Bay {bay_id}: {response.json().get('detail', 'Unknown error')}")
                except requests.RequestException as e:
                    errors.append(f"Bay {bay_id}: {str(e)}")
            else:
                not_found.append(bay_id)
        
        if success_count > 0:
            flash(f'Successfully updated {success_count} bay(s)!', 'success')
        if errors:
            for error in errors:
                flash(error, 'danger')
        if not_found:
            flash(f'Bays not found: {", ".join(not_found)}', 'warning')
        
        return redirect(url_for('list_cameras'))
        
    except Exception as e:
        flash(f'Error processing CSV file: {str(e)}', 'danger')
        return redirect(url_for('list_cameras'))

@app.route('/bay/type', methods=['PATCH'])
def update_bay_type():
    try:
        data = request.get_json()
        serial = data.get('serial')
        ptz_id = data.get('ptz_id')
        type_value = data.get('type')
        
        if not all([serial, ptz_id, type_value]):
            return jsonify({"detail": "Missing required fields"}), 400
        
        # Make sure the type is one of the allowed values
        allowed_types = ["car", "car(small)", "car(EV)", "bike"]
        if type_value not in allowed_types:
            return jsonify({"detail": "Invalid bay type"}), 400
        
        # Forward the request to the API
        response = requests.patch(
            f"{API_URL}/cameras/{serial}/bays",
            params={"ptz_id": ptz_id, "type": type_value}
        )
        
        if response.status_code == 200:
            return jsonify({"message": "Bay type updated successfully"})
        else:
            return jsonify(response.json()), response.status_code
    except Exception as e:
        return jsonify({"detail": str(e)}), 500

@app.route('/api/tenants/search')
def search_tenant_api():
    query = request.args.get('query')
    if not query:
        return jsonify({"detail": "Tenant name or ID is required"}), 400
    
    try:
        # Call the FastAPI endpoint
        response = requests.get(f"{API_URL}/api/tenants/search", params={"query": query})
        
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            # Pass through the error from the API
            return jsonify({"detail": response.json().get("detail", "Tenant not found")}), response.status_code
    except requests.RequestException as e:
        return jsonify({"detail": f"Error searching for tenant: {str(e)}"}), 500

@app.route('/api/tenants/partial_search')
def partial_search_tenant_api():
    query = request.args.get('query')
    if not query:
        return jsonify({"detail": "Search query is required"}), 400
    
    try:
        # Call the FastAPI endpoint
        response = requests.get(f"{API_URL}/api/tenants/partial_search", params={"query": query})
        
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            # Pass through the error from the API
            return jsonify({"detail": response.json().get("detail", "Search failed")}), response.status_code
    except requests.RequestException as e:
        return jsonify({"detail": f"Error during tenant search: {str(e)}"}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)