import asyncio
from aiohttp import web
import json
import logging
import requests
from datetime import datetime, timedelta
import paho.mqtt.client as mqtt
import pytz
import os
import errno
import base64 # Needed if you want to send image via JSON (e.g., MQTT)
from PIL import Image, UnidentifiedImageError
import io
from typing import Union, Tuple
import time

# --- Import your new Firebase handler ---
import firebase_handler

# Track the first PTZ ID and timestamp for each camera
first_ptz_seen = {}  # Stores serial -> {'ptz_id': str, 'timestamp': datetime, 'camera_ip': str}

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

sydney_tz = pytz.timezone("Australia/Sydney")

HOST_NAME = '0.0.0.0'  # Listen on all available interfaces
PORT_NUMBER = 8082  # Port to listen on
API_SERVER_IP = '***********'
MQTT_BROKER = '***********'  # MQTT broker address
MQTT_PORT = 1883  # Default MQTT port

# Initialize MQTT client
mqtt_client = mqtt.Client()
mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)

def crop_detected_objects(image_content: bytes, results_json: dict) -> Tuple[Union[bytes, None], Union[bytes, None]]:
    """
    Crops an image to the bounding boxes of the detected license plate and vehicle.

    Args:
        image_content: The raw bytes of the full image.
        results_json: The JSON dictionary containing detection results.

    Returns:
        Tuple[Union[bytes, None], Union[bytes, None]]: A tuple containing:
            - Element 0: Bytes of the cropped license plate image (JPEG), or None if failed.
            - Element 1: Bytes of the cropped vehicle image (JPEG), or None if failed.
    """
    cropped_plate_bytes = None
    cropped_vehicle_bytes = None

    if not image_content:
        logger.error("Cannot crop objects: No image content provided.")
        return None, None

    if not results_json:
        logger.error("Cannot crop objects: No results JSON provided.")
        return None, None

    try:
        # --- Load the original image once ---
        try:
            original_image = Image.open(io.BytesIO(image_content))
            img_width, img_height = original_image.size # Get image dimensions for validation
        except UnidentifiedImageError:
            logger.error("Failed to identify/open image from bytes. Corrupted or unsupported format.")
            return None, None
        except Exception as img_err:
            logger.error(f"Error opening image: {img_err}", exc_info=True)
            return None, None

        # --- Navigate to results (assuming first result is primary) ---
        results = results_json.get('results')
        if not results or not isinstance(results, list) or len(results) == 0:
            logger.warning("No 'results' list found or list is empty in JSON.")
            # Return None for both as we can't get either box
            return None, None

        first_result = results[0]
        if not isinstance(first_result, dict):
             logger.error("First item in 'results' is not a dictionary.")
             return None, None # Cannot proceed

        # --- Try to Crop License Plate ---
        try:
            plate_box = first_result.get('box')
            if plate_box and isinstance(plate_box, dict):
                coords = {k: plate_box.get(k) for k in ('xmin', 'ymin', 'xmax', 'ymax')}
                if None not in coords.values():
                     # Convert to int, handle potential errors
                     try:
                         xmin, ymin, xmax, ymax = map(int, coords.values())
                         # Basic coordinate validation
                         if 0 <= xmin < xmax <= img_width and 0 <= ymin < ymax <= img_height:
                             crop_area = (xmin, ymin, xmax, ymax)
                             cropped_plate_image = original_image.crop(crop_area)
                             byte_buffer = io.BytesIO()
                             cropped_plate_image.save(byte_buffer, format='JPEG', quality=90)
                             cropped_plate_bytes = byte_buffer.getvalue()
                             logger.info(f"Successfully cropped plate image to box: {crop_area}")
                         else:
                             logger.warning(f"Invalid or out-of-bounds plate coordinates: {coords}. Image size: ({img_width}x{img_height})")
                     except (ValueError, TypeError) as e:
                         logger.error(f"Error converting plate coordinates to int: {coords}, Error: {e}")
                else:
                    logger.warning("Plate 'box' dictionary missing coordinate(s).")
            else:
                logger.warning("No valid 'box' found for plate in the first result.")
        except Exception as e:
            logger.error(f"Error during plate cropping: {e}", exc_info=True)
            # Continue to attempt vehicle cropping even if plate fails

        # --- Try to Crop Vehicle ---
        try:
            vehicle_info = first_result.get('vehicle')
            if vehicle_info and isinstance(vehicle_info, dict):
                vehicle_box = vehicle_info.get('box')
                if vehicle_box and isinstance(vehicle_box, dict):
                    coords = {k: vehicle_box.get(k) for k in ('xmin', 'ymin', 'xmax', 'ymax')}
                    if None not in coords.values():
                        try:
                            xmin, ymin, xmax, ymax = map(int, coords.values())
                             # Basic coordinate validation
                            if 0 <= xmin < xmax <= img_width and 0 <= ymin < ymax <= img_height:
                                crop_area = (xmin, ymin, xmax, ymax)
                                cropped_vehicle_image = original_image.crop(crop_area)
                                byte_buffer = io.BytesIO()
                                cropped_vehicle_image.save(byte_buffer, format='JPEG', quality=85) # Slightly lower quality ok?
                                cropped_vehicle_bytes = byte_buffer.getvalue()
                                logger.info(f"Successfully cropped vehicle image to box: {crop_area}")
                            else:
                                logger.warning(f"Invalid or out-of-bounds vehicle coordinates: {coords}. Image size: ({img_width}x{img_height})")
                        except (ValueError, TypeError) as e:
                           logger.error(f"Error converting vehicle coordinates to int: {coords}, Error: {e}")
                    else:
                        logger.warning("Vehicle 'box' dictionary missing coordinate(s).")
                else:
                    logger.warning("No 'box' found within 'vehicle' info.")
            else:
                 logger.warning("No 'vehicle' info found in the first result.")
        except Exception as e:
            logger.error(f"Error during vehicle cropping: {e}", exc_info=True)
            # Error here doesn't affect the already processed plate crop

        # --- Return the results ---
        return cropped_plate_bytes, cropped_vehicle_bytes

    except Exception as e:
        # Catch-all for unexpected errors before returning
        logger.error(f"An unexpected error occurred during object cropping: {e}", exc_info=True)
        return None, None # Return None for both in case of major failure

# --- Updated update_bay function signature ---
async def update_bay(bay_to_update, plate_number, vehicle_score, ptz_id, serial, API_SERVER_IP, camera_data, mqtt_client, sydney_tz, utc_time, image_url, cropped_url):
    """
    Updates the bay status via API and MQTT.

    Args:
        bay_to_update (dict): The dictionary representing the bay to update.
        plate_number (str | None): The detected license plate number, or None.
        vehicle_score (float): The vehicle detection score.
        ptz_id (str): The preset token (PTZ ID) associated with the bay.
        serial (str): The camera serial number.
        API_SERVER_IP (str): The IP address of the API server.
        camera_data (dict): Data about the camera retrieved from the API.
        mqtt_client (mqtt.Client): The MQTT client instance.
        sydney_tz (pytz.timezone): The Sydney timezone object.
        image_content (bytes | None): The raw bytes of the image, if available.
    """
    try:
        current_plate = bay_to_update.get('plate')

        # Ensure valid plate number and score threshold (You might want to use vehicle_score here)
        if plate_number is not None:
            status = 'occupied'
            # Correctly get Sydney time
            sydney_time = datetime.now(sydney_tz)
            # Format it as ISO-8601 but with the Sydney timezone offset instead of Z
            time_parked = sydney_time.isoformat()
            # Retrieve existing tenant info only if a plate is detected
            parked_tenant = bay_to_update.get('parked_tenant')
            tenantID = bay_to_update.get('tenantID')
            cardholderName = bay_to_update.get('cardholderName')
        else:
            plate_number = None
            status = 'vacant'
            time_parked = None
            parked_tenant = None
            tenantID = None  # Clear tenantID when plate is None
            cardholderName = None  # Clear cardholderName when plate is None

        # Prepare patch data
        patch_data = {
            "plate": plate_number,
            "ptz_id": str(ptz_id),
            "status": status,
            "time_parked": time_parked,
            "tenant": bay_to_update.get('tenant'), # Keep assigned tenant
            "parked_tenant": parked_tenant,
            "tenantID": tenantID,
            "cardholderName": cardholderName
        }

        # Send request to update bay status
        patch_response = requests.patch(
            f'http://{API_SERVER_IP}:8000/cameras/{serial}/bays',
            params=patch_data # Note: Sending image via params is not standard. API needs adjustment if image needed here.
            # If the API endpoint /cameras/{serial}/bays expects the image,
            # you would likely need to send it as multipart/form-data instead of params:
            # files = {'image': ('snapshot.jpg', image_content, 'image/jpeg')} if image_content else None
            # patch_response = requests.patch(f'http://{API_SERVER_IP}:8000/cameras/{serial}/bays', data=patch_data, files=files)
        )

        if patch_response.status_code == 200:
            logger.info(f"Successfully updated bay with PTZ ID {ptz_id} for camera {serial}")

            # Prepare MQTT message
            mqtt_message = {
                'serial': serial,
                'ptz_id': ptz_id,
                'bay_id': bay_to_update.get('bay_id', bay_to_update.get('id')),  # Try both possible field names
                'plate': plate_number,
                'status': status,
                'time_parked': time_parked,
                'tenant': bay_to_update.get('tenant'),
                'parked_tenant': parked_tenant,
                'tenantID': tenantID,
                'cardholderName': cardholderName,
                'timestamp': utc_time,
                'image_url' : image_url,
                'cropped_url' : cropped_url
                # --- Option: Include image in MQTT (e.g., base64 encoded) ---
                # 'image_base64': base64.b64encode(image_content).decode('utf-8') if image_content else None
            }

            logger.info(f"Bay data for MQTT message (excluding image): {json.dumps({k: v for k, v in mqtt_message.items() if k != 'image_base64'})}") # Avoid logging large image data
            mqtt_client.publish('update', json.dumps(mqtt_message))
            logger.info(f"Published update to MQTT topic")

            # Stop the guard tour only if a vehicle is detected (status becomes 'occupied')
            if status == 'occupied':
                camera_ip = camera_data.get('camera_ip')
                if not camera_ip:
                    logger.error(f"Camera IP not found for serial {serial}")
                    # Continue without stopping guard tour if IP is missing
                else:
                    for guard_tour_index in range(5): # Assuming max 5 guard tours (G0 to G4)
                        try:
                            guard_tour_response = requests.get(
                                f'http://{camera_ip}/axis-cgi/param.cgi?action=update&GuardTour.G{guard_tour_index}.Running=no',
                                auth=requests.auth.HTTPDigestAuth('root', 'M1ecrdry1!'), # Consider making user/pass configurable
                                timeout=5 # Add a timeout
                            )

                            if guard_tour_response.status_code == 404:
                                # This is expected if the tour doesn't exist or isn't running
                                logger.debug(f"Guard tour G{guard_tour_index} not found or not running on {camera_ip}.")
                                break # Stop trying higher indexes if one doesn't exist
                            elif guard_tour_response.status_code == 200:
                                logger.info(f"Successfully stopped guard tour G{guard_tour_index} on {camera_ip}")
                                # Decide if you want to stop checking others once one is stopped.
                                # break
                            else:
                                logger.warning(f"Failed to stop guard tour G{guard_tour_index} on {camera_ip}, Status Code: {guard_tour_response.status_code}, Response: {guard_tour_response.text[:100]}") # Log truncated response
                        except requests.exceptions.RequestException as req_err:
                            logger.error(f"Error connecting to camera {camera_ip} to stop guard tour G{guard_tour_index}: {req_err}")
                            break # Stop trying if connection fails

        else:
            logger.error(f"Failed to update bay with PTZ ID {ptz_id} for camera {serial}, Status Code: {patch_response.status_code}, Response: {patch_response.text}")

    except Exception as e:
        logger.error(f"Unexpected error while processing bay update for PTZ ID {ptz_id}, Serial {serial}: {e}", exc_info=True)


async def handle_webhook(request):
    upload_to = "uploads"
    image_content = None # Initialize image content to None
    json_data = None

    try:
        if request.method == "GET":
            return web.Response(text="Send a POST request instead.")

        logger.info(f"Content-Type: {request.headers.get('Content-Type', 'not specified')}")

        # Create upload directory if it doesn't exist
        if not os.path.exists(upload_to):
            try:
                os.makedirs(upload_to)
            except OSError as exc:
                if exc.errno != errno.EEXIST:
                    logger.error(f"Error creating upload directory '{upload_to}': {exc}")
                    # Decide if you want to raise or just log and continue without saving
                    raise # Re-raise if directory creation is critical

        # Check if the request is multipart
        if request.content_type.startswith('multipart/'):
            try:
                form_data = await request.post()
                logger.info(f"Form keys received: {list(form_data.keys())}")

                # Process parts: find JSON and potential image file
                for field_name, field in form_data.items():
                    if field_name == 'json':
                        try:
                            # If the 'json' field is a file upload:
                            if hasattr(field, 'file'):
                                json_string = field.file.read().decode('utf-8')
                            # If the 'json' field is plain text:
                            else:
                                json_string = field # Assume it's the string directly
                            json_data = json.loads(json_string)
                            logger.info("Processed JSON data from 'json' form field.")
                        except json.JSONDecodeError as json_err:
                            logger.error(f"Error decoding JSON from 'json' field: {json_err}")
                            return web.json_response({'status': 'error', 'message': 'Invalid JSON in form data'}, status=400)
                        except Exception as field_err:
                             logger.error(f"Error reading 'json' field: {field_err}")
                             return web.json_response({'status': 'error', 'message': 'Could not process json field'}, status=400)

                    # Check if other fields are files (potential images)
                    elif hasattr(field, 'filename') and field.filename:
                        filename = field.filename
                        filepath = os.path.join(upload_to, filename)
                        try:
                            # Read content into memory AND save to disk
                            # In aiohttp, field has file-like properties
                            current_image_content = field.file.read() # Read the content
                            if current_image_content: # Make sure content is not empty
                                image_content = current_image_content # Store the first valid image content found
                                with open(filepath, 'wb') as f:
                                    f.write(image_content)
                                logger.info(f"Found and saved image file '{filename}' ({len(image_content)} bytes)")
                                # Optional: Break if you only expect one image file
                                # break
                            else:
                                logger.warning(f"Found file field '{filename}' but it was empty.")
                        except Exception as file_err:
                            logger.error(f"Error processing file '{filename}': {file_err}")
                            # Continue processing other fields even if one file fails

                if not json_data:
                     logger.error("Multipart request received, but no 'json' field found or processed.")
                     return web.json_response({'status': 'error', 'message': "Multipart request needs a 'json' field"}, status=400)

            except Exception as parsing_error:
                logger.error(f"Error parsing multipart form data: {parsing_error}", exc_info=True)
                return web.json_response({'status': 'error', 'message': f'Error parsing multipart data: {parsing_error}'}, status=400)

        # Else, assume the entire body is JSON
        elif request.content_type == 'application/json':
            try:
                json_data = await request.json()
                logger.info("Processed request body as JSON.")
                # No image content expected in this case, image_content remains None
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON format in request body: {e}")
                raw_body = await request.text()
                logger.error(f"Raw body received: {raw_body[:500]}") # Log first 500 chars
                return web.json_response({'status': 'error', 'message': 'Invalid JSON format'}, status=400)
        else:
            # Handle other content types or lack thereof
             logger.error(f"Unsupported Content-Type: {request.content_type}. Expected 'multipart/form-data' or 'application/json'.")
             return web.json_response({'status': 'error', 'message': 'Unsupported Content-Type'}, status=415)


        # --- Process the extracted data ---
        if json_data:
            logger.info(f"Passing JSON to process_webhook_data. Image present: {image_content is not None}")
            # Pass both json_data and potentially image_content
            await process_webhook_data(json_data, image_content)
            return web.json_response({'status': 'success'})
        else:
            # This case should ideally be caught earlier
            logger.error("Reached end of handler without valid JSON data.")
            return web.json_response({'status': 'error', 'message': 'No processable JSON data found in request'}, status=400)

    except Exception as e:
        logger.error(f"Unexpected error in handle_webhook: {e}", exc_info=True)
        import traceback
        traceback.print_exc()
        return web.json_response({'status': 'error', 'message': f'Internal Server Error: {str(e)}'}, status=500)

async def process_webhook_data(data, image_content=None):
    """
    Processes the webhook JSON data, checks if an update is needed based on status
    or plate change, and triggers bay updates if necessary.

    Args:
        data (dict): The parsed JSON data from the webhook.
        image_content (bytes | None): The raw bytes of the image, if it was included in the request.
    """
    plate_image_url = None
    vehicle_image_url = None
    cropped_plate_bytes = None
    cropped_vehicle_bytes = None
    
    try:
        # Extract camera_id and ptz_id
        data_info = data.get('data', {})
        if not data_info:
             logger.warning(f"Webhook JSON missing 'data' field. Full data: {data}")
             return

        camera_id = data_info.get('camera_id')
        if not camera_id:
            logger.error("Camera ID (camera_id) not found in webhook data['data']")
            return

        if '_' not in camera_id:
            logger.error(f"Invalid camera_id format '{camera_id}', expected format <serial>_<preset_token>")
            return
        serial, ptz_id = camera_id.split('_', 1)
        logger.info(f"Processing event for Camera Serial: {serial}, PTZ ID/Preset: {ptz_id}")

        # Get current camera details
        try:
            response = requests.get(f'http://{API_SERVER_IP}:8000/cameras/{serial}', timeout=10)
            response.raise_for_status()
        except requests.exceptions.RequestException as req_err:
             logger.error(f"Failed to get camera details for serial {serial} from API: {req_err}")
             return
        camera_data = response.json()
        camera_ip = camera_data.get('camera_ip')

        # --- Guard Tour Cycle Detection Logic (remains the same) ---
        current_time = datetime.utcnow()
        # Convert to ISO 8601 string
        iso_time_string = current_time.isoformat()
        iso_time_string_utc = iso_time_string + "Z"
        reset_interval = timedelta(seconds=60)
        if serial not in first_ptz_seen or (current_time - first_ptz_seen[serial]['timestamp']) > reset_interval:
            first_ptz_seen[serial] = {'ptz_id': ptz_id, 'timestamp': current_time, 'camera_ip': camera_ip}
            logger.info(f"Tracking first PTZ ID {ptz_id} for potential cycle detection on camera {serial}.")
        elif first_ptz_seen[serial]['ptz_id'] == ptz_id:
            logger.info(f"PTZ ID {ptz_id} has cycled back within {reset_interval.total_seconds()}s for camera {serial}. Stopping active guard tours.")
            if camera_ip:
                for guard_tour_index in range(5):
                    try:
                        # ... (rest of guard tour stopping code) ...
                        guard_tour_response = requests.get(
                            f'http://{camera_ip}/axis-cgi/param.cgi?action=update&GuardTour.G{guard_tour_index}.Running=no',
                            auth=requests.auth.HTTPDigestAuth('root', 'M1ecrdry1!'),
                            timeout=5
                        )
                        if guard_tour_response.status_code == 200:
                            logger.info(f"Successfully stopped guard tour G{guard_tour_index} on {camera_ip} due to cycle detection.")
                        elif guard_tour_response.status_code == 404:
                            logger.debug(f"Guard tour G{guard_tour_index} not found/running on {camera_ip} (cycle detection stop).")
                            break
                        else:
                             logger.warning(f"Failed to stop guard tour G{guard_tour_index} on {camera_ip} (cycle detection stop), Status: {guard_tour_response.status_code}")
                    except requests.exceptions.RequestException as tour_err:
                        logger.error(f"Error connecting to camera {camera_ip} to stop guard tour G{guard_tour_index} (cycle detection): {tour_err}")
                        break
            else:
                 logger.error(f"Cannot stop guard tour for cycle detection: Camera IP not found for serial {serial}")
            # Important: Reset first_ptz_seen only after attempting to stop
            if serial in first_ptz_seen:
                 del first_ptz_seen[serial]
                 logger.info(f"Reset cycle detection state for camera {serial}")

        # Extract plate number and vehicle score from the webhook data
        results = data_info.get('results', [])
        detected_plate = None
        highest_plate_score = -1.0
        vehicle_score = 0.0
        region_code = "XX"
        best_result_item = None

        if isinstance(results, list) and results:
            logger.info(f"Processing {len(results)} results to find the best plate.")
            for item in results:
                if not isinstance(item, dict):
                    logger.warning(f"Skipping non-dictionary item in results: {item}")
                    continue

                current_plate = item.get('plate')
                current_score_raw = item.get('score')

                try:
                    current_score = float(current_score_raw)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid or missing score '{current_score_raw}' for plate '{current_plate}'. Skipping.")
                    continue

                if current_plate is None:
                    logger.warning(f"Skipping item with null plate and score {current_score}.")
                    continue
                
                logger.debug(f"Evaluating plate: {current_plate}, score: {current_score}")
                if current_score > highest_plate_score:
                    highest_plate_score = current_score
                    detected_plate = current_plate.upper() if current_plate else None
                    best_result_item = item
                    logger.debug(f"New best plate: {detected_plate}, score: {highest_plate_score}")

            if best_result_item:
                logger.info(f"Highest scoring plate: {detected_plate} with score: {highest_plate_score}")
                
                # Extract region_code from the best_result_item
                region_info = best_result_item.get('region')
                if region_info and isinstance(region_info, dict):
                    code = region_info.get('code')
                    if code and isinstance(code, str) and code.strip():
                        region_code = code.strip().upper()
                        logger.info(f"Region code for best plate: {region_code}")
                    else:
                        logger.info(f"Region code missing or invalid in best result. Using default: {region_code}")
                else:
                    logger.info(f"No region info for best plate. Using default: {region_code}")

                # Extract vehicle_score from the best_result_item's vehicle data
                vehicle_info = best_result_item.get('vehicle')
                if vehicle_info and isinstance(vehicle_info, dict) and 'score' in vehicle_info:
                    try:
                        vehicle_score = float(vehicle_info['score'])
                        logger.info(f"Vehicle detection score for best plate: {vehicle_score}")
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid vehicle score '{vehicle_info['score']}' in best_result_item. Using default {vehicle_score}.")
                        vehicle_score = 0.0 # Ensure it's reset if invalid
                else:
                    logger.info(f"No vehicle information or score in best result. Using default vehicle_score: {vehicle_score}")
            else:
                logger.info("No valid plate with a positive score found in results.")
        elif not results:
             logger.info("No 'results' array found or array is empty in webhook data for this event.")
        else:
            logger.warning(f"'results' field is not a list. Found: {type(results)}. Skipping plate processing.")

        # Determine the potential new status based on detection
        new_status = 'occupied' if detected_plate is not None else 'vacant'

        # Find the corresponding bay in the camera's bay list
        bay_to_update = None
        if 'bays' in camera_data and isinstance(camera_data['bays'], list):
            for bay in camera_data['bays']:
                bay_ptz_id = bay.get('ptz_id')
                if bay_ptz_id is not None and str(bay_ptz_id) == str(ptz_id):
                    bay_to_update = bay
                    break
        else:
            logger.warning(f"Camera data for {serial} is missing 'bays' list or it's not a list.")
            return # Cannot proceed without bays list

        if not bay_to_update:
            logger.error(f"Bay with PTZ ID {ptz_id} not found in configuration for camera {serial}")
            return # Cannot update if bay is not found

        # --- Get Current State from the fetched bay data ---
        current_status = bay_to_update.get('status')
        current_plate = bay_to_update.get('plate') # This might be None
        bay_id_log = bay_to_update.get('id', 'N/A') # For logging

        logger.info(f"Checking Bay ID {bay_id_log} (PTZ {ptz_id}): Current Status='{current_status}', Current Plate='{current_plate}'. Detected Plate='{detected_plate}', Implied New Status='{new_status}'.")

        # --- Conditional Logic: Update only if status changes OR plate changes while occupied ---
        should_update = False
        reason = "No change detected"

        if new_status != current_status:
            should_update = True
            reason = f"Status change ({current_status} -> {new_status})"
        elif new_status == 'occupied' and current_status == 'occupied' and detected_plate != current_plate:
            # This covers case where bay was occupied by Plate A, now detected Plate B
            # Also covers case where bay was occupied by Plate A, now detected None (handled by status change)
            # Also covers case where bay was occupied by None (error state?), now detected Plate B (status change)
            should_update = True
            reason = f"Plate change while occupied ({current_plate} -> {detected_plate})"

        # --- Call update_bay only if needed ---
        if should_update:
            #we detected a new vehicle, lets do image stuff
            if detected_plate:
                cropped_plate_bytes, cropped_vehicle_bytes = crop_detected_objects(image_content, data_info)
                # Handle the plate image (e.g., save for testing)
                if cropped_plate_bytes:
                    state_str = region_code
                    state = "UNKNOWN"
                    if state_str and "-" in region_code:
                        state = region_code.split("-")[1].upper()
                    plate_str_for_filename = detected_plate if detected_plate else "NOPLATE"
                    epoch_time = int(time.time())
                    logger.info(f"Obtained cropped plate image ({len(cropped_plate_bytes)} bytes).")
                    plate_blob_name = f"plate-images/{plate_str_for_filename}_{state}_{epoch_time}.jpeg"
                    logger.info(f"Attempting to upload plate image to Firebase: {plate_blob_name}")
                    plate_image_url = firebase_handler.upload_to_firebase_storage(cropped_plate_bytes, plate_blob_name)
                    if plate_image_url: logger.info(f"Plate image uploaded: {plate_image_url}")
                    else: logger.error("Plate image upload failed.")
                    #save local to debug
                    try:
                        fixed_plate_filename = "test_crop_plate.jpg"
                        with open(fixed_plate_filename, 'wb') as f:
                            f.write(cropped_plate_bytes)
                        full_path = os.path.abspath(fixed_plate_filename)
                        logger.info(f"Cropped plate image SAVED/OVERWRITTEN to: {full_path}")
                    except Exception as e:
                        logger.error(f"Failed to save cropped plate image to file '{fixed_plate_filename}': {e}")
                else:
                    logger.warning("Failed to obtain cropped plate image.")

                # Handle the vehicle image (e.g) for bays we actually use teh overview image
                if cropped_vehicle_bytes:
                    logger.info(f"Obtained cropped vehicle image ({len(image_content)} bytes).")
                    vehicle_blob_name = f"vehicle-images/{plate_str_for_filename}_{state}_{epoch_time}.jpeg"
                    logger.info(f"Attempting to upload vehicle image to Firebase: {vehicle_blob_name}")
                    #the reason we use th ewhole image is so they can see teh bay number on teh ground
                    vehicle_image_url = firebase_handler.upload_to_firebase_storage(image_content, vehicle_blob_name)
                    if vehicle_image_url: logger.info(f"Vehicle image uploaded: {vehicle_image_url}")
                    else: logger.error("Vehicle image upload failed.")
                    #save local to see
                    try:
                        fixed_vehicle_filename = "test_crop_vehicle.jpg"
                        with open(fixed_vehicle_filename, 'wb') as f:
                            f.write(cropped_vehicle_bytes)
                        full_path = os.path.abspath(fixed_vehicle_filename)
                        logger.info(f"Cropped vehicle image SAVED/OVERWRITTEN to: {full_path}")
                    except Exception as e:
                        logger.error(f"Failed to save cropped vehicle image to file '{fixed_vehicle_filename}': {e}")
                else:
                    logger.warning("Failed to obtain cropped vehicle image.")
            else:
                logger.warning("Clearing bay, no images needed.")

            logger.info(f"Update required for Bay ID {bay_id_log} (PTZ {ptz_id}): {reason}. Calling update_bay.")
            await update_bay(
                bay_to_update,
                detected_plate, # Pass the newly detected plate
                vehicle_score,
                ptz_id,
                serial,
                API_SERVER_IP,
                camera_data,
                mqtt_client,
                sydney_tz,
                iso_time_string_utc,
                vehicle_image_url,
                plate_image_url
            )
        else:
            logger.info(f"No update required for Bay ID {bay_id_log} (PTZ {ptz_id}): {reason}.")

    except KeyError as ke:
         logger.error(f"Missing expected key in webhook data: {ke}. Data: {data}", exc_info=True)
    except Exception as e:
        logger.error(f"Unexpected error while processing webhook data for PTZ {ptz_id}, Serial {serial}: {e}", exc_info=True)


if __name__ == '__main__':
        # --- Initialize Firebase ---
    logger.info("Initializing Firebase...")
    if not firebase_handler.initialize_firebase():
        logger.error("Firebase initialization failed. Uploads will not work. Exiting.")
        # Decide if the app should exit if Firebase is critical
        exit(1)
    logger.info("Firebase initialization check complete.")

    app = web.Application()
    # Increase max request size if large images are expected (e.g., 10MB)
    app.router.add_post('/webhook', handle_webhook) # client_max_size=1024**2*10)

    # Start MQTT client loop in the background
    try:
        mqtt_client.loop_start()
        logger.info(f"Started MQTT client, connected to {MQTT_BROKER}:{MQTT_PORT}")
    except Exception as mqtt_err:
        logger.error(f"Failed to start MQTT client: {mqtt_err}", exc_info=True)
        # Decide if the application should exit if MQTT fails to start
        # exit(1)


    logger.info(f"Starting server at http://{HOST_NAME}:{PORT_NUMBER}")
    web.run_app(app, host=HOST_NAME, port=PORT_NUMBER)

    # Stop MQTT client loop on shutdown
    logger.info("Stopping MQTT client loop.")
    mqtt_client.loop_stop()