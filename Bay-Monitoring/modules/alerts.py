import paho.mqtt.client as mqtt
import json
import logging
from msal import PublicClientApplication
import requests
import urllib3
import random
from json import dumps

# Disable SSL verification warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MQTT Configuration
MQTT_BROKER = '***********'
MQTT_PORT = 1883
MQTT_TOPIC = 'update'

# API Configuration
DASHBOARD_IP = '***********'

# Azure AD Configuration
CLIENT_ID = '6b0d2046-f9bf-4583-aff3-243d392a657e'
TENANT_ID = '4feaf35a-169e-417c-a7df-d06f696e0f28'
USERNAME = 'ddi_modules'
PASSWORD = 'Poba9555'

def addAlert(data, bay, headers):
    """Add an alert to the system"""
    url = f'https://{DASHBOARD_IP}:8000/alerts/addAlert/'
    
    try:
        payload = {
            "id": str(random.randint(10, 9999)),
            "cardholderName": data['cardholderName'],
            "cardholderID": data['cardholderID'],
            "tenantName": data['tenantName'],
            "tenantID": data['tenantID'],
            "alertType": "Bay violation",
            "licensePlate": data['plate'],
            "description": f"Parked in {bay['tenant']} bay-{bay['bay_id']}",
            "timestamp": bay['time_parked'],
            "acknowledged": False,
            "repeatOffender": False,
            "gallagherID": "N/A",
            "cameraID": "9",
            "cameraName": "Bay Monitoring",
            "imageURL": "dummy_image_url"
        }
        
        response = requests.request("POST", url, headers=headers, data=dumps(payload), verify=False)
        if response.ok:
            alert = response.json()
            logger.info("Successfully added alert to the system")
            return alert['data']
        else:
            logger.error(f"Error adding alert: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Exception adding alert: {e}")
        return False

def check_access_control(ip_address, endpoint, plate, headers):
    """Check if a license plate has access control"""
    url = f'{ip_address}/{endpoint}/{plate}'
    try:
        response = requests.request("GET", url, headers=headers, verify=False)
        return response
    except Exception as e:
        logger.error(f"Error checking access control: {e}")
        return None

def get_access_token():
    """Get access token from Azure AD"""
    try:
        # Define scope using client_id
        scope = [f"{CLIENT_ID}/.default"]

        # Initialize the MSAL client
        app = PublicClientApplication(
            client_id=CLIENT_ID,
            authority=f"https://login.microsoftonline.com/{TENANT_ID}"
        )

        # Acquire token using username and password
        result = app.acquire_token_by_username_password(
            username=USERNAME,
            password=PASSWORD,
            scopes=scope
        )

        if 'error' in result:
            logger.error(f"Error getting token: {result['error']}")
            logger.error(f"Description: {result['error_description']}")
            return None
        else:
            logger.info("Successfully retrieved access token")
            return result['access_token']
    except Exception as e:
        logger.error(f"Unexpected error getting token: {e}")
        return None

def on_connect(client, userdata, flags, rc):
    """Callback when client connects to the broker"""
    if rc == 0:
        logger.info(f"Connected to MQTT Broker at {MQTT_BROKER}:{MQTT_PORT}")
        # Subscribe to the update topic
        client.subscribe(MQTT_TOPIC)
        logger.info(f"Subscribed to topic: {MQTT_TOPIC}")
    else:
        logger.error(f"Failed to connect to MQTT broker with code: {rc}")

def on_message(client, userdata, msg):
    """Callback when a message is received"""
    try:
        # Decode the JSON message
        payload = json.loads(msg.payload.decode())
        
        # Log the update information
        logger.info("Bay Update Received:")
        logger.info(f"Camera Serial: {payload['serial']}")
        logger.info(f"Bay ID: {payload['bay_id']}")
        logger.info(f"PTZ ID: {payload['ptz_id']}")
        logger.info(f"License Plate: {payload['plate']}")
        logger.info(f"Status: {payload['status']}")
        logger.info(f"Time Parked: {payload['time_parked']}")
        logger.info("-" * 50)
        
        # Check if the status is occupied
        if payload['status'] == 'occupied':
            logger.info(f"Bay is occupied with plate {payload['plate']} - proceeding to check plate against API")
            
            # Set up headers with the access token
            headers = {
                'Authorization': f'Bearer {userdata["access_token"]}',
                'Content-Type': 'application/json'
            }
            
            # Check the plate against the API
            result = check_access_control(
                'https://***********:8000',
                'carpark/searchCarpark',
                payload['plate'].upper(),  # Ensure plate is uppercase
                headers
            )
            
            if result:
                try:
                    result_data = result.json()
                    if result_data['data']:
                        api_tenant = result_data['data']['tenantName']
                        bay_tenant = payload.get('tenant')
                        logger.info(f"Plate {payload['plate']} is assigned to tenant: {api_tenant}")
                        
                        # Compare the bay tenant with the API response tenant
                        if bay_tenant and api_tenant and bay_tenant.lower() != api_tenant.lower():
                            logger.warning(f"ALERT: Wrong tenant parked! Bay assigned to '{bay_tenant}' but vehicle belongs to '{api_tenant}'")
                            
                            # Create alert using the API
                            alert_data = result_data['data']  # Contains cardholderName, cardholderID, tenantName, tenantID, etc.
                            bay_data = {
                                'tenant': bay_tenant,
                                'bay_id': payload['bay_id'],
                                'time_parked': payload['time_parked']
                            }
                            
                            alert_result = addAlert(alert_data, bay_data, headers)
                            if alert_result:
                                logger.info("Successfully created bay violation alert")
                            else:
                                logger.error("Failed to create bay violation alert")
                    else:
                        logger.info(f"Plate {payload['plate']} is not assigned to any tenant")
                except Exception as e:
                    logger.error(f"Error parsing API response: {e}")
            else:
                logger.error("Failed to check plate against API")
        else:
            logger.info("Bay is vacant - no need to check plate against API")
        
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode message: {e}")
    except KeyError as e:
        logger.error(f"Missing expected field in message: {e}")
    except Exception as e:
        logger.error(f"Error processing message: {e}")

def main():
    # Get access token first
    access_token = get_access_token()
    if not access_token:
        logger.error("Failed to get access token. Exiting...")
        return

    # Store token in userdata to be accessible in callbacks
    userdata = {'access_token': access_token}
    
    # Create MQTT client with userdata
    client = mqtt.Client(userdata=userdata)
    
    # Set callbacks
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # Connect to broker
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        
        # Start the loop
        logger.info("Starting MQTT client...")
        client.loop_forever()
        
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        client.disconnect()
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        client.disconnect()

if __name__ == "__main__":
    main()