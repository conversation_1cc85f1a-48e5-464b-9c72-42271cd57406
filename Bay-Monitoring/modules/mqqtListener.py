import asyncio
import signal
import json
import time
import logging
import sys
import threading
import queue
from datetime import datetime
import os
from collections import defaultdict

import httpx
import paho.mqtt.client as mqtt

#from bikeyapa import bike_or_nobike  # assuming this is a synchronous function
from caryapa import car_or_nocar

# Add this at the top of your file with other configuration variables
SAVE_NOCAR_IMAGES = False  # Set to False to disable saving no-car images

# Global camera processing tracking dictionary
camera_processing = {}  # Tracks which cameras are currently being processed

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global stop event for graceful shutdown
stop_event = threading.Event()

# MQTT configuration
MQTT_BROKER = '***********'
MQTT_PORT = 1883
MQTT_TOPIC = 'motion/#'  # Subscribe to all motion events

# Debounce configuration
DEBOUNCE_INTERVAL = 180  # seconds

# Camera credentials (for digest auth)
CAMERA_USERNAME = 'root'
CAMERA_PASSWORD = 'M1ecrdry1!'

# API server and Plate Recognizer API URL
API_SERVER_IP = '***********'
PLATE_RECOGNIZER_API_URL = f'http://{API_SERVER_IP}:8080/v1/plate-reader/'

# Global mapping from camera serial to IP address
camera_ip_map = {}

# Thread-safe queue for transferring messages from MQTT thread to asyncio
mqtt_message_queue = queue.Queue()

camera_preset_last_processed = {}  # {serial_preset: timestamp}
camera_preset_lock = threading.Lock()
PRESET_LOCK_DURATION = 10  # seconds - minimum time between processing the same preset
# Add these at the top of your file with other global variables
# Tracks which cameras are currently being processed to prevent concurrent processing
active_cameras = {}
active_cameras_lock = threading.Lock()

# Add these variables at the top of your file
# Track when each camera+preset combination was last processed
camera_preset_last_processed = {}  # {serial_preset: timestamp}
camera_preset_lock = threading.Lock()

# Define a smaller per-preset lock duration
PRESET_LOCK_DURATION = 10  # seconds - minimum time between processing the same preset

########################################
# ASYNC HTTP FUNCTIONS
########################################

async def initialize_camera_ip_map(client: httpx.AsyncClient):
    """
    Asynchronously fetch all cameras and initialize the camera_ip_map.
    """
    url = f'http://{API_SERVER_IP}:8000/cameras/'
    try:
        response = await client.get(url, timeout=10)
        if response.status_code == 200:
            cameras = response.json()
            for camera in cameras:
                camera_ip_map[camera['serial']] = camera['camera_ip']
            logger.info("Camera IP mapping initialized successfully")
        else:
            logger.error(f"Failed to fetch cameras, Status Code: {response.status_code}")
    except Exception as e:
        logger.error(f"Error initializing camera IP map: {e}")

async def send_no_car_webhook(client: httpx.AsyncClient, serial_preset_token: str):
    """
    Send a JSON message to the webhook server when no car is detected.
    """
    webhook_url = "http://***********:8082/webhook"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    payload = {
        "hook": {
            "target": None,
            "id": None,
            "event": None
        },
        "data": {
            "filename": None,
            "timestamp": timestamp,
            "camera_id": serial_preset_token,
            "results": [],
            "usage": None,
            "processing_time": None
        }
    }
    try:
        response = await client.post(webhook_url, json=payload, timeout=10)
        if response.status_code == 200:
            logger.info(f"Successfully sent webhook message for camera {serial_preset_token}")
        else:
            logger.error(f"Failed to send webhook message for camera {serial_preset_token}, Status Code: {response.status_code}")
    except Exception as e:
        logger.error(f"Exception sending webhook message for camera {serial_preset_token}: {e}")

async def send_snapshot_to_plate_recognizer(client: httpx.AsyncClient, image_content: bytes, camera_id_with_preset: str):
    """
    Send the snapshot to the Plate Recognizer API asynchronously.
    """
    try:
        files = {"upload": ("snapshot.jpg", image_content, "image/jpeg")}
        data = {"camera_id": camera_id_with_preset}
        response = await client.post(PLATE_RECOGNIZER_API_URL, files=files, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("Successfully sent snapshot to Plate Recognizer API")
        else:
            logger.error(f"Failed to send snapshot to Plate Recognizer API, Status Code: {response.status_code}")
            logger.debug(f"Response: {response.text}")
    except Exception as e:
        logger.error(f"Exception sending snapshot to Plate Recognizer API: {e}")

async def capture_and_send_snapshot(camera_client: httpx.AsyncClient,
                                   plate_client: httpx.AsyncClient,
                                   camera_ip: str, serial: str, preset_token: str):
    """
    Capture a snapshot from the camera, run car detection on it,
    and if the car probability is above 0.50, send it to the Plate Recognizer API.
    """
    try:
        url = f"http://{camera_ip}/axis-cgi/jpg/image.cgi?resolution=1280x720&compression=35"
        logger.debug(f"Requesting snapshot from camera {serial} at URL: {url}")
        response = await camera_client.get(url, timeout=10)
        if response.status_code == 200:
            image_content = response.content
            logger.info(f"Successfully captured snapshot from camera {serial}")
            
            # Run the detection function in a separate thread to avoid blocking the event loop
            loop = asyncio.get_running_loop()
            car_prob = await loop.run_in_executor(None, car_or_nocar, image_content)
            #car_prob = 0.51
            
            serial_preset = f"{serial}_{preset_token}"
            # Check the returned probability and send snapshot if above threshold
            if car_prob is not None and car_prob > 0.50:
                logger.info(f"Car probability {car_prob:.2f} is above threshold, sending snapshot for camera {serial}")
                await send_snapshot_to_plate_recognizer(plate_client, image_content, serial_preset)
            else:
                logger.info(f"Car probability {car_prob} is below threshold; snapshot not sent for camera {serial}")
                await send_no_car_webhook(plate_client, serial_preset)
                
                # Save the image with timestamp for training purposes when no car is detected
                # Only if SAVE_NOCAR_IMAGES is True
                if SAVE_NOCAR_IMAGES:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    training_dir = os.path.join("training_data", "no_car")
                    os.makedirs(training_dir, exist_ok=True)
                    file_path = os.path.join(training_dir, f"nocar_{serial}_{timestamp}.jpg")
                    
                    # Save image to disk
                    with open(file_path, "wb") as f:
                        f.write(image_content)
                    logger.info(f"Saved no-car image for training: {file_path}")
                else:
                    logger.debug(f"Skipping no-car image save (disabled in config) for camera {serial}")
        else:
            logger.error(f"Failed to capture snapshot from camera {serial}, Status Code: {response.status_code}")
    except Exception as e:
        logger.error(f"Exception capturing snapshot from camera {serial}: {e}")

async def increment_camera_events(client: httpx.AsyncClient, serial: str):
    """
    Increment the event counter for the given camera.
    """
    url = f'http://{API_SERVER_IP}:8000/cameras/{serial}/events/increment'
    try:
        response = await client.post(url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Incremented events for camera {serial}. New count: {data.get('events')}")
        else:
            logger.error(f"Failed to increment events for camera {serial}. Status code: {response.status_code}")
    except Exception as e:
        logger.error(f"Error incrementing events for camera {serial}: {e}")

########################################
# MQTT MESSAGE PROCESSOR
########################################

async def mqtt_message_processor(sessions: dict):
    """
    Process messages from the thread-safe queue with strict preset-specific locking.
    """
    while not stop_event.is_set():
        try:
            # Non-blocking check for new messages
            try:
                payload = mqtt_message_queue.get_nowait()
            except queue.Empty:
                # No messages, sleep briefly then check again
                await asyncio.sleep(0.1)
                continue
                
            serial = payload.get("serial")
            preset_token = payload.get("message", {}).get("source", {}).get("PresetToken")
            on_preset = payload.get("message", {}).get("data", {}).get("on_preset")
            
            if not serial:
                logger.warning("Serial not found in message.")
                continue
                
            if preset_token is None:
                logger.warning("PresetToken not found in message.")
                continue
            
            # Handle moving camera differently - we want to discard these messages completely
            is_moving = preset_token == "-1" or on_preset == "0"
            if is_moving:
                logger.info(f"Camera {serial} is moving (preset={preset_token}, on_preset={on_preset}); discarding message")
                continue  # Skip moving camera messages entirely
                
            # Create keys for camera and preset combinations
            camera_preset_key = f"{serial}_{preset_token}"
            processing_key = f"{serial}"
            
            # First check: Ensure camera isn't currently being processed
            if processing_key in camera_processing and camera_processing[processing_key]:
                logger.info(f"Camera {serial} is actively being processed, skipping this message")
                continue
            
            # Second check: Apply a strict minimum time between processing the same preset
            current_time = time.time()
            with camera_preset_lock:
                if camera_preset_key in camera_preset_last_processed:
                    elapsed = current_time - camera_preset_last_processed[camera_preset_key]
                    if elapsed < PRESET_LOCK_DURATION:
                        remaining = PRESET_LOCK_DURATION - elapsed
                        logger.info(f"Preset lock active: {camera_preset_key} processed {elapsed:.1f}s ago; enforcing {remaining:.1f}s more lock.")
                        continue  # Skip this message entirely
            
            # Mark this camera as being processed
            camera_processing[processing_key] = True
            
            try:
                logger.info(f"Processing message for camera {serial} with preset {preset_token}")
                
                # Update the preset's last processed time RIGHT BEFORE processing
                with camera_preset_lock:
                    camera_preset_last_processed[camera_preset_key] = time.time()
                
                # Get the camera IP from the mapping
                camera_ip = camera_ip_map.get(serial)
                if not camera_ip:
                    logger.error(f"Camera IP not found for serial {serial}; skipping snapshot capture.")
                    continue
                
                # Increment the camera events counter
                await increment_camera_events(sessions["events_client"], serial)
                
                # Capture and send the snapshot
                await capture_and_send_snapshot(
                    sessions["camera_client"],
                    sessions["plate_client"],
                    camera_ip,
                    serial,
                    preset_token
                )
                
                # Update the last processed time AGAIN after successful processing
                with camera_preset_lock:
                    camera_preset_last_processed[camera_preset_key] = time.time()
                
            finally:
                # Mark this camera as no longer being processed
                camera_processing[processing_key] = False
                
        except Exception as e:
            logger.error(f"Error in mqtt_message_processor: {e}", exc_info=True)

########################################
# MQTT CALLBACKS (RUNNING IN MQTT THREAD)
########################################

def on_connect(client, userdata, flags, rc):
    logger.debug(f"Connected to MQTT Broker with result code {rc}")
    client.subscribe(MQTT_TOPIC)
    logger.debug(f"Subscribed to topic {MQTT_TOPIC}")

def on_message(client, userdata, msg):
    """
    This callback runs in the MQTT thread. It simply places the message
    in a thread-safe queue for the asyncio event loop to process.
    """
    # Ignore retained messages so that you only process messages published after connection
    if msg.retain:
        logger.debug("Ignoring retained message on startup.")
        return

    try:
        payload = json.loads(msg.payload.decode())
        # Simply add the message to the thread-safe queue and return immediately
        mqtt_message_queue.put(payload)
        logger.debug(f"Added message to thread-safe queue: {payload.get('serial')}")
    except Exception as e:
        logger.error(f"Error in on_message: {e}", exc_info=True)

########################################
# MQTT CLIENT AND ASYNCIO LOOP INTEGRATION
########################################

async def run_mqtt_client():
    """
    Starts the MQTT client in a separate thread.
    """
    logger.debug("Starting MQTT client")
    client = mqtt.Client(clean_session=True)
    client.on_connect = on_connect
    client.on_message = on_message

    try:
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        mqtt_thread = threading.Thread(target=client.loop_forever)
        mqtt_thread.daemon = True
        mqtt_thread.start()
        logger.debug("MQTT client thread started")
        
        # Wait until stop event is set
        while not stop_event.is_set():
            await asyncio.sleep(1)
            
        # Disconnect MQTT client
        client.disconnect()
        logger.info("MQTT client disconnected")
    except Exception as e:
        logger.error(f"Error in run_mqtt_client: {e}", exc_info=True)

########################################
# MAIN ASYNC FUNCTION
########################################

async def main():
    if not sys.version_info >= (3, 7):
        raise Exception("Python 3.7+ is required. Current version: %s" % sys.version)
    logger.info("Starting Thread-Safe MQTT Client System")

    # Get the current event loop
    loop = asyncio.get_running_loop()

    # Set up graceful termination handlers
    def termination_handler():
        logger.info("Termination signal received. Stopping all tasks.")
        stop_event.set()

    for sig in (signal.SIGTERM, signal.SIGINT):
        try:
            loop.add_signal_handler(sig, termination_handler)
        except NotImplementedError:
            signal.signal(sig, lambda s, f: termination_handler())

    # Create httpx AsyncClient instances
    camera_client = httpx.AsyncClient(auth=httpx.DigestAuth(CAMERA_USERNAME, CAMERA_PASSWORD), timeout=10)
    plate_client = httpx.AsyncClient(timeout=10)
    events_client = httpx.AsyncClient(timeout=10)

    # Initialize the camera IP mapping
    await initialize_camera_ip_map(events_client)

    # Sessions dictionary for the message processor
    sessions = {
        "camera_client": camera_client,
        "plate_client": plate_client,
        "events_client": events_client,
    }

    try:
        # Start the message processor and MQTT client as two separate tasks
        message_processor_task = asyncio.create_task(mqtt_message_processor(sessions))
        mqtt_client_task = asyncio.create_task(run_mqtt_client())
        
        # Wait for both tasks to complete (which happens when stop_event is set)
        await asyncio.gather(message_processor_task, mqtt_client_task)
    finally:
        # Close the httpx clients gracefully
        await camera_client.aclose()
        await plate_client.aclose()
        await events_client.aclose()
        logger.info("All HTTP clients closed")

if __name__ == "__main__":
    logger.debug("Starting main function")
    asyncio.run(main())