#!/usr/bin/env python3
import requests
import time
import logging
import schedule
import datetime
import pytz
import os
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("bay_checker.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('bay_checker')

# API Configuration
BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:8000')
API_TIMEOUT = 30  # seconds

def check_bays(status_filter="vacant"):
    """
    Function that checks all cameras for bays with the specified status and triggers the bay search API.
    This optimized version cycles through cameras to minimize waiting times while ensuring at least
    10 seconds between requests to the same camera.
    
    Args:
        status_filter (str): Status to filter bays by, either "vacant" or "occupied"
    """
    logger.info(f"Starting daily {status_filter} bay check")
    
    try:
        # Step 1: Get a list of all cameras
        logger.info("Retrieving camera list")
        cameras_response = requests.get(f"{BASE_URL}/cameras/", timeout=API_TIMEOUT)
        cameras_response.raise_for_status()
        cameras = cameras_response.json()
        
        if not cameras:
            logger.warning("No cameras found in the system")
            return
            
        logger.info(f"Found {len(cameras)} cameras")
        
        # Prepare data structures for optimized processing
        camera_last_request = defaultdict(float)  # Track last request time for each camera
        camera_bay_queue = {}  # Dictionary to hold queues of bays to check for each camera
        
        # Build the queue of bays to check for each camera
        for camera in cameras:
            camera_serial = camera.get('serial')
            if not camera_serial:
                logger.warning(f"Camera without serial found: {camera}")
                continue
            
            # Filter bays based on the requested status
            filtered_bays = [bay for bay in camera.get('bays', []) if bay.get('status') == status_filter]
            if filtered_bays:
                logger.info(f"Camera {camera_serial} has {len(filtered_bays)} {status_filter} bays to check")
                camera_bay_queue[camera_serial] = filtered_bays
            else:
                logger.info(f"Camera {camera_serial} has no {status_filter} bays to check")
        
        # Continue processing until all bays have been checked
        total_bays_checked = 0
        while camera_bay_queue:
            current_time = time.time()
            cameras_processed_this_round = False
            
            # Iterate through cameras that still have bays to check
            for camera_serial in list(camera_bay_queue.keys()):
                # Check if this camera is available (10+ seconds since last request)
                elapsed_since_last_request = current_time - camera_last_request[camera_serial]
                if elapsed_since_last_request < 10 and camera_last_request[camera_serial] > 0:
                    # This camera is not ready yet, skip it for now
                    continue
                
                # This camera is ready for a new request
                cameras_processed_this_round = True
                
                # Get the next bay to check and remove it from the queue
                bay = camera_bay_queue[camera_serial].pop(0)
                bay_id = bay.get('bay_id')
                
                if not bay_id:
                    logger.warning(f"Bay without ID found in camera {camera_serial}")
                    # If this was the last bay for this camera, remove the camera from the queue
                    if not camera_bay_queue[camera_serial]:
                        del camera_bay_queue[camera_serial]
                    continue
                
                # Process this bay
                logger.info(f"Checking {status_filter} bay: {bay_id} on camera {camera_serial}")
                try:
                    # Hit the bay search API to trigger a camera check
                    search_response = requests.get(
                        f"{BASE_URL}/api/bays/search",
                        params={"bay_id": bay_id},
                        timeout=API_TIMEOUT
                    )
                    
                    if search_response.status_code == 200:
                        logger.info(f"Successfully checked bay {bay_id}")
                        total_bays_checked += 1
                    else:
                        logger.warning(
                            f"Error checking bay {bay_id}: Status {search_response.status_code}, "
                            f"Response: {search_response.text}"
                        )
                except Exception as e:
                    logger.error(f"Exception while checking bay {bay_id}: {str(e)}")
                
                # Update the last request time for this camera
                camera_last_request[camera_serial] = time.time()
                
                # If this was the last bay for this camera, remove the camera from the queue
                if not camera_bay_queue[camera_serial]:
                    del camera_bay_queue[camera_serial]
                
                # We've processed one bay from this camera, move on to the next camera
                break  # Exit the camera loop after processing one bay
            
            # If no cameras were processed this round, wait a bit before the next round
            if not cameras_processed_this_round and camera_bay_queue:
                logger.info("All cameras are waiting on cooldown. Waiting 1 second...")
                time.sleep(1)
        
        logger.info(f"Daily {status_filter} bay check completed successfully. Total bays checked: {total_bays_checked}")
        
    except Exception as e:
        logger.error(f"Error during daily {status_filter} bay check: {str(e)}")

def check_vacant_bays():
    """Function to check all vacant bays"""
    check_bays(status_filter="vacant")
    
def check_occupied_bays():
    """Function to check all occupied bays"""
    check_bays(status_filter="occupied")

def run_morning_job():
    """Function that will be scheduled to run at 9am Sydney time to check vacant bays"""
    logger.info("Running scheduled morning vacant bay check job")
    check_vacant_bays()

def run_evening_job():
    """Function that will be scheduled to run at 10pm Sydney time to check occupied bays"""
    logger.info("Running scheduled evening occupied bay check job")
    check_occupied_bays()

def main():
    """
    Main entry point that sets up the scheduler to run jobs at 9am and 10pm Sydney time.
    """
    # Get Sydney timezone
    sydney_tz = pytz.timezone('Australia/Sydney')
    
    # Log startup information
    logger.info("Bay checker service starting")
    now = datetime.datetime.now(sydney_tz)
    logger.info(f"Current time in Sydney: {now.strftime('%Y-%m-%d %H:%M:%S %Z%z')}")
    
    # Schedule the morning job to run at 9am Sydney time (check vacant bays)
    schedule.every().day.at("10:18").do(run_morning_job)
    logger.info("Morning job scheduled to run at 09:00 Sydney time (checking vacant bays)")
    
    # Schedule the evening job to run at 10pm Sydney time (check occupied bays)
    schedule.every().day.at("22:00").do(run_evening_job)
    logger.info("Evening job scheduled to run at 22:00 Sydney time (checking occupied bays)")
    
    # If you want to run either check immediately on startup, uncomment one of these lines
    # check_vacant_bays()
    # check_occupied_bays()
    
    # Keep the script running and check the schedule
    logger.info("Starting scheduler loop")
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute

if __name__ == "__main__":
    main()