import paho.mqtt.client as mqtt
import json
import logging
from msal import PublicClientApplication
import requests
import urllib3
import random
from json import dumps
import pytz
from datetime import datetime, timedelta
from collections import defaultdict

# Disable SSL verification warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MQTT Configuration
MQTT_BROKER = '***********'
MQTT_PORT = 1883
MQTT_TOPIC = 'update'

# API Configuration
DASHBOARD_IP = '***********'

# backend api
API_IP = '***********'

# Azure AD Configuration
CLIENT_ID = '6b0d2046-f9bf-4583-aff3-243d392a657e'
TENANT_ID = '4feaf35a-169e-417c-a7df-d06f696e0f28'
USERNAME = 'ddi_modules'
PASSWORD = 'Poba9555' # Consider using environment variables or a secrets manager for credentials

sydney_tz = pytz.timezone("Australia/Sydney")

# Alert tracking system
# Structure: {plate_number: {bay_id: timestamp_of_last_alert}}
alert_registry = defaultdict(dict)

def is_alert_allowed(plate, bay_id):
    """
    Check if an alert is allowed for this plate and bay combination
    Returns True if alert is allowed, False if we've already sent one today
    """
    current_time = datetime.now()

    # If plate is not in registry or bay is not registered for this plate, allow alert
    if bay_id not in alert_registry[plate]:
        return True

    # Get the last alert time for this plate+bay
    last_alert_time = alert_registry[plate][bay_id]

    # Check if last alert was more than 24 hours ago
    time_since_last_alert = current_time - last_alert_time
    if time_since_last_alert > timedelta(days=1):
        return True

    # Otherwise, we've already alerted within 24 hours
    logger.info(f"Alert for plate {plate} in bay {bay_id} suppressed - last alert was {time_since_last_alert} ago")
    return False

def register_alert(plate, bay_id):
    """
    Register that we've sent an alert for this plate+bay combination
    """
    alert_registry[plate][bay_id] = datetime.now()
    logger.info(f"Registered alert for plate {plate} in bay {bay_id}")



""" mqtt_message = {
    'serial': serial,
    'ptz_id': ptz_id,
    'bay_id': bay_to_update.get('bay_id', bay_to_update.get('id')), # Try both possible field names
    'plate': plate_number,
    'status': status,
    'time_parked': time_parked,
    'tenant': bay_to_update.get('tenant'),
    'parked_tenant': parked_tenant,
    'tenantID': tenantID,
    'cardholderName': cardholderName,
    'timestamp': utc_time,
    'image_url' : image_url,
    'cropped_url' : cropped_url
} """

def add_event_cloud(data, bay, headers):
    """Add an event to the system cloud"""
    # Prepare payload for cloud event
    CameraName = "Bay-" + bay.get('bay_id')
    payload = {
        "MessageType" : bay.get('status').capitalize(),
        "Type" : "Bay Event",
        "CameraName": CameraName,
        "Serial": bay.get('serial'), # Use .get for safety
        "PtzId": bay.get('ptz_id'),
        "BayId": bay.get('bay_id'),
        "Plate": bay.get('plate'),
        "Status": bay.get('status'),
        "TimeParked": bay.get('time_parked'),
        "TenantName": bay.get('tenant'), # Bay's assigned tenant
        "ParkedTenant": data.get('tenantName'), # Tenant from API lookup (or None if vacant/not found)
        "TenantID": data.get('tenantID'),
        "CardholderName": data.get('cardholderName'),
        "Timestamp": bay.get('timestamp'),
        "VehicleImage" : bay.get('image_url'),
        "Image" : bay.get('cropped_url')
    }
    print(payload)
    url = f'https://{DASHBOARD_IP}:8000/bayEvents'
    try:
        response = requests.post(url, headers=headers, data=dumps(payload), verify=False, timeout=10) # Added timeout
        response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
        alert = response.json()
        logger.info(f"Successfully added event to the cloud system for bay {bay.get('bay_id')}")
        # Check if 'data' key exists before returning
        return alert.get('data', True) # Return True if 'data' key is missing but request was ok
    except requests.exceptions.RequestException as e:
        logger.error(f"Error adding cloud event for bay {bay.get('bay_id')}: {e}")
        # Log response text if available
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response text: {e.response.text}")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response when adding cloud event for bay {bay.get('bay_id')}: {e}")
        logger.error(f"Response text: {response.text}")
        return False
    except Exception as e:
        logger.error(f"Unexpected exception adding cloud event for bay {bay.get('bay_id')}: {e}")
        return False


def update_bay(data, bay, headers):
    """Update bay with additional information from the API response"""
    # Cloud dashboard first
    cloud_response_data = add_event_cloud(data, bay, headers)
    if cloud_response_data:
        logger.info(f"Successfully processed cloud event for bay {bay['bay_id']}")
        # No need to print the whole response usually, log confirms success
    else:
        # Error already logged in add_event_cloud
        logger.warning(f"Failed to process cloud event for bay {bay['bay_id']}")
        # Decide if you want to proceed with local update even if cloud fails

    # Local dashboard update
    url = f'http://{API_IP}:8000/cameras/{bay["serial"]}/bays'
    params = {
        'ptz_id': bay['ptz_id'],
        'parked_tenant': data.get('tenantName'),
        'tenantID': data.get('tenantID'),
        'cardholderName': data.get('cardholderName')
    }

    try:
        response = requests.patch(url, params=params, verify=False, timeout=10) # Added timeout
        response.raise_for_status() # Raise an exception for bad status codes
        logger.info(f"Successfully updated local bay {bay['bay_id']} with tenant information")
        alert = response.json()
        # Check if 'data' key exists before returning
        return alert.get('data', True) # Return True if 'data' key is missing but request was ok
    except requests.exceptions.RequestException as e:
        logger.error(f"Error updating local bay {bay['bay_id']}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response text: {e.response.text}")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response when updating local bay {bay['bay_id']}: {e}")
        logger.error(f"Response text: {response.text}")
        return False
    except Exception as e:
        logger.error(f"Unexpected exception updating local bay {bay['bay_id']}: {e}")
        return False


def addAlert(data, bay, headers):
    """Add an alert to the system"""
    url = f'https://{DASHBOARD_IP}:8000/alertMessage'

    # Note: Getting image data here might add latency. Consider if needed for every alert.
    # image_base64 = "dummy_image_url"
    # try:
    #     # Use the more robust bays_search endpoint instead
    #     img_url = f"http://{MQTT_BROKER}:8000/api/bays/search?bay_id={bay['bay_id']}"
    #     img_response = requests.get(img_url, verify=False, timeout=5)
    #     img_response.raise_for_status()
    #     response_data = img_response.json()
    #     image_base64 = response_data.get('image', "dummy_image_url") # Or handle missing image
    # except requests.exceptions.RequestException as img_error:
    #     logger.warning(f"Failed to get image from API for alert: {img_error}")
    # except Exception as img_error:
    #     logger.error(f"Error getting camera image from API for alert: {img_error}")

    dateTime = datetime.now(pytz.utc) # Use UTC directly
    time_alert = dateTime.strftime("%Y-%m-%dT%H:%M:%S.%fZ")

    payload = {
        "Id": str(random.randint(10000, 99999)), # Increased range slightly
        "Type": "BayViolation",
        "CardholderName": data.get('cardholderName', 'N/A'), # Use .get for safety
        "CardholderID": data.get('cardholderID', 'N/A'),
        "TenantName": data.get('tenantName', 'N/A'),
        "TenantID": data.get('tenantID', 'N/A'),
        "Alert": "BayViolation",
        "Plate": data.get('plate', 'N/A'),
        "Description": f"Parked in {bay.get('tenant', 'Unknown')} bay-{bay.get('bay_id', 'Unknown')}",
        "Timestamp": time_alert,
        "GallagherID": "N/A", # Consider if this can be populated
        "CameraID": "Bay", # Consider if specific camera ID is available
        "CameraName": "Bay Monitoring", # Consider if specific camera name is available
        "Image": bay.get('cropped_url'), # Use bay data passed in
        "VehicleImage" : bay.get('image_url') # Use bay data passed in
    }

    try:
        response = requests.post(url, headers=headers, data=dumps(payload), verify=False, timeout=10) # Added timeout
        response.raise_for_status()
        alert = response.json()
        logger.info(f"Successfully added alert to the system for plate {data.get('plate')}")
        return alert.get('data', True)
    except requests.exceptions.RequestException as e:
        logger.error(f"Error adding alert for plate {data.get('plate')}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response text: {e.response.text}")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response when adding alert for plate {data.get('plate')}: {e}")
        logger.error(f"Response text: {response.text}")
        return False
    except Exception as e:
        logger.error(f"Exception adding alert for plate {data.get('plate')}: {e}")
        return False

def check_access_control(ip_address, endpoint, plate, headers):
    """Check if a license plate has access control"""
    if not plate: # Handle cases where plate is None or empty
        logger.warning("Attempted to check access control for an empty plate.")
        return None
    url = f'{ip_address}/{endpoint}/{plate}'
    try:
        response = requests.get(url, headers=headers, verify=False, timeout=10) # Added timeout
        # It's often better to return the whole response object
        # and let the caller handle status codes and JSON parsing
        return response
    except requests.exceptions.RequestException as e:
        logger.error(f"Error checking access control for plate {plate}: {e}")
        return None
    except Exception as e:
         logger.error(f"Unexpected error checking access control for plate {plate}: {e}")
         return None


def get_access_token():
    """Get access token from Azure AD"""
    try:
        # Define scope using client_id
        scope = [f"{CLIENT_ID}/.default"]

        # Initialize the MSAL client
        app = PublicClientApplication(
            client_id=CLIENT_ID,
            authority=f"https://login.microsoftonline.com/{TENANT_ID}"
        )

        # Acquire token using username and password
        result = app.acquire_token_by_username_password(
            username=USERNAME,
            password=PASSWORD,
            scopes=scope
        )

        if 'error' in result:
            logger.error(f"Error getting token: {result.get('error', 'Unknown error')}")
            logger.error(f"Description: {result.get('error_description', 'No description')}")
            return None
        elif 'access_token' in result:
            logger.info("Successfully retrieved access token")
            return result['access_token']
        else:
            logger.error(f"Unexpected result when getting token: {result}")
            return None
    except Exception as e:
        logger.error(f"Unexpected error getting token: {e}")
        return None

def on_connect(client, userdata, flags, rc):
    """Callback when client connects to the broker"""
    # Map return codes to human-readable messages
    rc_map = {
        0: "Connection successful",
        1: "Connection refused - incorrect protocol version",
        2: "Connection refused - invalid client identifier",
        3: "Connection refused - server unavailable",
        4: "Connection refused - bad username or password",
        5: "Connection refused - not authorised"
    }
    if rc == 0:
        logger.info(f"Connected to MQTT Broker at {MQTT_BROKER}:{MQTT_PORT} ({rc_map.get(rc, 'Unknown code')})")
        # Subscribe to the update topic
        # Using QoS 1 for guaranteed delivery (at least once)
        client.subscribe(MQTT_TOPIC, qos=1) # Removed options=mqtt.SubscribeOptions(noLocal=True) unless specifically needed
        logger.info(f"Subscribed to topic: {MQTT_TOPIC}")
    else:
        logger.error(f"Failed to connect to MQTT broker: {rc_map.get(rc, f'Unknown code {rc}')}")
        # Consider adding retry logic or exiting if connection fails persistently

def on_message(client, userdata, msg):
    """Callback when a message is received"""
    try:
        raw_payload = msg.payload.decode('utf-8')
        logger.info(f"+++ RECEIVED RAW MQTT MESSAGE on topic '{msg.topic}': {raw_payload}")
        payload = json.loads(raw_payload)
        logger.info(f"+++ SUCCESSFULLY PARSED MQTT PAYLOAD: {json.dumps(payload)}")

        # Validate essential payload keys
        required_keys = ['serial', 'bay_id', 'ptz_id', 'status', 'timestamp']
        if not all(key in payload for key in required_keys):
            logger.error(f"!!! INVALID PAYLOAD: Missing one or more required keys {required_keys}. Payload: {payload}")
            return

        # Log the update information using .get for safety
        logger.info("Bay Update Received:")
        logger.info(f"  Camera Serial: {payload.get('serial')}")
        logger.info(f"  Bay ID: {payload.get('bay_id')}")
        logger.info(f"  PTZ ID: {payload.get('ptz_id')}")
        logger.info(f"  License Plate: {payload.get('plate')}")
        logger.info(f"  Status: {payload.get('status')}")
        logger.info(f"  Time Parked: {payload.get('time_parked')}")
        logger.info(f"  Bay Tenant: {payload.get('tenant')}") # Log the bay's assigned tenant
        logger.info(f"  Timestamp: {payload.get('timestamp')}")
        logger.info("-" * 50)

        # Ensure access token is available
        access_token = userdata.get('access_token')
        if not access_token:
            logger.error("!!! Access token not found in userdata. Cannot proceed.")
            # Optionally try to refresh token here if logic allows
            return

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        bay_status = payload.get('status')
        plate_number = payload.get('plate') # Can be None

        if bay_status == 'occupied':
            if not plate_number:
                logger.warning(f"Bay {payload['bay_id']} is occupied but plate is missing/null. Cannot check access.")
                # Still update bay status as occupied but without tenant info
                update_data = {'tenantName': None, 'tenantID': None, 'cardholderName': None}
                update_bay(update_data, payload, headers)
                return # Stop further processing for this message

            logger.info(f"Bay {payload['bay_id']} occupied by plate {plate_number}. Checking access control...")

            # Check the plate against the API
            result = check_access_control(
                f'https://{DASHBOARD_IP}:8000', # Use https and correct IP
                'carpark/searchCarpark',
                plate_number.upper(), # Ensure plate is uppercase
                headers
            )

            if result is None:
                logger.error(f"API check failed for plate {plate_number}. Cannot determine tenant.")
                # Update bay as occupied, but without specific tenant info from API
                update_data = {'tenantName': 'Unknown', 'tenantID': None, 'cardholderName': 'Unknown'}
                update_bay(update_data, payload, headers)

            elif result.ok: # Check if request was successful (status code 2xx)
                try:
                    result_data = result.json()
                    api_data = result_data.get('data')

                    if api_data: # Check if 'data' exists and is not empty/null
                        api_tenant = api_data.get('tenantName')
                        api_tenant_id = api_data.get('tenantID')
                        api_cardholder_name = api_data.get('cardholderName')
                        bay_tenant = payload.get('tenant') # The tenant assigned to the physical bay

                        logger.info(f"API Result for {plate_number}: Tenant={api_tenant}, ID={api_tenant_id}, Cardholder={api_cardholder_name}")

                        # Update the bay (local and cloud) with info found in API
                        update_bay(api_data, payload, headers)

                        # Handle 'vacant' tenant name explicitly if needed
                        if bay_tenant and bay_tenant.lower() == "vacant":
                            bay_tenant = None # Treat 'vacant' as unassigned for comparison

                        # Compare the bay's assigned tenant with the vehicle's tenant from API
                        # Only alert if the bay has an assigned tenant AND it doesn't match the vehicle's tenant
                        if bay_tenant and api_tenant and bay_tenant.lower() != api_tenant.lower():
                            logger.warning(f"ALERT: Bay Violation! Bay {payload['bay_id']} assigned to '{bay_tenant}' but vehicle belongs to '{api_tenant}' (Plate: {plate_number})")

                            # Check alert rate limiting
                            if is_alert_allowed(plate_number.upper(), payload['bay_id']):
                                # Prepare data for alert - combine API data and payload data
                                alert_data_combined = {**payload, **api_data} # Combine dicts, payload overwrites api_data if keys clash (e.g., plate)
                                alert_data_combined['plate'] = plate_number.upper() # Ensure plate is correct

                                # Pass the original payload as 'bay' context for the alert function
                                alert_result = addAlert(alert_data_combined, payload, headers)
                                if alert_result:
                                    logger.info(f"Successfully created bay violation alert for plate {plate_number}")
                                    register_alert(plate_number.upper(), payload['bay_id'])
                                else:
                                    logger.error(f"Failed to create bay violation alert for plate {plate_number}")
                            else:
                                # Log suppression (already logged in is_alert_allowed)
                                pass
                        else:
                            logger.info(f"No violation: Bay Tenant='{bay_tenant}', Vehicle Tenant='{api_tenant}'. Plate: {plate_number}")

                    else:
                        logger.info(f"Plate {plate_number} found by API but no tenant data returned (API data: {api_data}). Treating as unknown.")
                         # Update bay as occupied, but without specific tenant info from API
                        update_data = {'tenantName': 'Unknown/Not Found', 'tenantID': None, 'cardholderName': 'Unknown/Not Found'}
                        update_bay(update_data, payload, headers)

                except json.JSONDecodeError:
                    logger.error(f"Failed to decode JSON response from API for plate {plate_number}. Response: {result.text}")
                    # Update bay as occupied, but without specific tenant info from API
                    update_data = {'tenantName': 'Error', 'tenantID': None, 'cardholderName': 'Error'}
                    update_bay(update_data, payload, headers)
                except Exception as e:
                    logger.error(f"Error processing successful API response for plate {plate_number}: {e}")
                    update_data = {'tenantName': 'Processing Error', 'tenantID': None, 'cardholderName': 'Processing Error'}
                    update_bay(update_data, payload, headers)

            else: # Handle non-2xx responses (e.g., 404 Not Found)
                logger.warning(f"API check for plate {plate_number} returned status {result.status_code}. Assuming plate not found or unauthorized.")
                logger.warning(f"Response text: {result.text}") # Log server response
                # Update bay as occupied, but mark tenant as not found by API
                update_data = {'tenantName': 'Not Found in System', 'tenantID': None, 'cardholderName': 'Not Found in System'}
                update_bay(update_data, payload, headers)
                # Decide if you want to trigger an alert for unknown plates parking
                # if is_alert_allowed(plate_number.upper(), payload['bay_id']):
                #     alert_data_unknown = {'plate': plate_number.upper(), 'tenantName': 'Unknown', ...} # Add other needed fields
                #     addAlert(alert_data_unknown, payload, headers)
                #     register_alert(plate_number.upper(), payload['bay_id'])


        elif bay_status == 'vacant':
            logger.info(f"Bay {payload['bay_id']} is now vacant. Updating status.")
            # Data to send for a vacant event (clearing parked tenant info)
            vacant_data = {
                "tenantName": "Unknown",
                "tenantID": "Unknown",
                "cardholderName": "Unknown"
            }
            # Call add_event_cloud directly for vacant events, no need for update_bay logic?
            # Or call update_bay which handles both cloud and local updates
            update_bay(vacant_data, payload, headers)
            # --- Correction is here ---
            # Removed the logging block that caused the error as update_bay now handles logging.

        else:
            logger.warning(f"Received unknown bay status '{bay_status}' for bay {payload['bay_id']}. Payload: {payload}")

    except json.JSONDecodeError as e:
        logger.error(f"!!! FAILED TO PARSE MQTT JSON: {e}. Raw payload was: {msg.payload.decode('utf-8', errors='ignore')}") # Added error handling for decode
    except KeyError as e:
        logger.error(f"!!! MISSING EXPECTED KEY in MQTT payload: {e}. Payload: {payload}") # Log payload if available
    except Exception as e:
        # Log the full traceback for unexpected errors
        logger.exception(f"!!! UNEXPECTED ERROR processing MQTT message: {e}")


def main():
    # Get access token first
    access_token = get_access_token()
    if not access_token:
        logger.error("Failed to get access token. Exiting...")
        return

    # Store token in userdata to be accessible in callbacks
    userdata = {'access_token': access_token}

    # Create MQTT client with userdata
    # Using MQTTv311 as default, specify if different version needed
    client = mqtt.Client(userdata=userdata, clean_session=True)

    # Assign callbacks
    client.on_connect = on_connect
    client.on_message = on_message
    # Optional: Add on_disconnect, on_log callbacks for more detailed debugging
    # client.on_log = lambda client, userdata, level, buf: logger.debug(f"MQTT Log: {buf}")

    try:
        # Connect to broker with keepalive
        logger.info(f"Attempting to connect to MQTT broker at {MQTT_BROKER}:{MQTT_PORT}...")
        client.connect(MQTT_BROKER, MQTT_PORT, keepalive=60)

        # Start the network loop (non-blocking) and handle reconnections manually if needed
        # Or use loop_forever() for blocking behavior
        logger.info("Starting MQTT client loop...")
        client.loop_forever()

    except ConnectionRefusedError:
         logger.error(f"Connection refused by MQTT broker at {MQTT_BROKER}:{MQTT_PORT}. Check broker status, credentials, and firewall.")
    except TimeoutError:
         logger.error(f"Connection attempt to MQTT broker at {MQTT_BROKER}:{MQTT_PORT} timed out.")
    except OSError as e:
         logger.error(f"Network error connecting to MQTT broker: {e}")
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user.")
    except Exception as e:
        logger.exception(f"An unexpected error occurred in main loop: {e}") # Log traceback
    finally:
        logger.info("Disconnecting MQTT client...")
        client.disconnect()
        logger.info("MQTT client disconnected.")

if __name__ == "__main__":
    main()