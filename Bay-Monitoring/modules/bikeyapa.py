from ultralytics import <PERSON><PERSON><PERSON>
from PIL import Image
import io

model = YOLO('./bestbike.pt')
def bike_or_nobike(imageBytes):
    try:
        #print("\nStarting bike ride ---------------------------------\n")
        with open("tempyapabike.jpg", "wb") as f:
            f.write(imageBytes)
        image = Image.open(io.BytesIO(imageBytes))
        results = model(image, verbose=False)

        if results and results[0].probs is not None: 
            probs = results[0].probs.data.tolist() # Convert tensor to list
            class_names = model.names
            output_str = ""
            bike_prob = None
            # Build output string and capture probability for 'bike'
            for i, prob in enumerate(probs):
                output_str += f"{class_names[i]} {prob:.2f}, "
                if class_names[i].lower() == "bike":
                    bike_prob = prob
            output_str = output_str[:-2] # Remove the last comma and space
            # print("\n--------------")
            # print(output_str)
            # print("--------------\n")
            #print("Bike ride successful ---------------------------------")
            return bike_prob
        else:
            print("No predictions found.")
            return None
        #print("\nBike ride successful ---------------------------------\n")
    except Exception as e:
        print("\nBike crashed ---------------------------------\n")
        print(f"{e}\n")
        print("---------------------------------\n")
        return None