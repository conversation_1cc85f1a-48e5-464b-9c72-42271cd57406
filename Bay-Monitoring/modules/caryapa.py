from ultralytics import YOLO
from PIL import Image
import io
#from bikeyapa import bike_or_nobike
from datetime import datetime
import os

model = YOLO('./car_nocar_1_2.pt')
fullmodel = YOLO('./car_bike_empty_1_2.pt')
def car_or_nocar(imageBytes):
    try:
        print("\nStarting ride ...\n")
        car_bike_empty(imageBytes)
        with open("tempyapacar.jpg", "wb") as f:
            f.write(imageBytes)
        image = Image.open(io.BytesIO(imageBytes))
        results = model(image, verbose=False)

        if results and results[0].probs is not None:
            # Convert tensor to list
            probs = results[0].probs.data.tolist()
            class_names = model.names
            output_str = ""
            car_prob = None
            bike_prob = None
            # Build output string and capture probability for 'car'
            for i, prob in enumerate(probs):
                output_str += f"{class_names[i]} {prob:.2f}, "
                if class_names[i].lower() == "car":
                    car_prob = prob
                elif class_names[i].lower() == "bike":
                    bike_prob = prob

            output_str = output_str[:-2]  # Remove the last comma and space
            if car_prob is None or car_prob < 0.5:
                print("\n--------------")
                # print("Its NOT a car!")
                #bike_prob = bike_or_nobike(imageBytes)
                if bike_prob is not None and bike_prob > 0.5:
                    # print("Its a bike!")

                    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    # training_dir = os.path.join("training_data", "bike")
                    # os.makedirs(training_dir, exist_ok=True)
                    # file_path = os.path.join(training_dir, f"{timestamp}.jpg")
                    # with open(file_path, "wb") as f:
                    #     f.write(imageBytes)
                    
                    # print("--------------\n")
                    return bike_prob
                else:
                    # print("Its NOT a bike!")

                    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    # training_dir = os.path.join("training_data", "empty")
                    # os.makedirs(training_dir, exist_ok=True)
                    # file_path = os.path.join(training_dir, f"{timestamp}.jpg")
                    # with open(file_path, "wb") as f:
                    #     f.write(imageBytes)

                    # print("--------------\n")
                    return None
            # print("Its a car!")

            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # training_dir = os.path.join("training_data", "car")
            # os.makedirs(training_dir, exist_ok=True)
            # file_path = os.path.join(training_dir, f"{timestamp}.jpg")
            # with open(file_path, "wb") as f:
            #     f.write(imageBytes)

            # print("--------------\n")
            return car_prob
        else:
            print("No predictions found.")
            return None
    except Exception as e:
        print("Car ride crashed ---------------------------------")
        print(e)
        print("---------------------------------")
        return None


def car_bike_empty(imageBytes):
    try:
        # print("\nStarting ride ...\n")
        # with open("tempimage.jpg", "wb") as f:
        #     f.write(imageBytes)

        image = Image.open(io.BytesIO(imageBytes))
        results = fullmodel(image, verbose=False)
        if results and results[0].probs is not None:
            # Convert tensor to list
            probs = results[0].probs.data.tolist()
            class_names = fullmodel.names
            output_str = ""
            car_prob = None
            bike_prob = None
            empty_prob = None
            # Build output string and capture probability for 'car'
            for i, prob in enumerate(probs):
                output_str += f"{class_names[i]} {prob:.2f}, "
                if class_names[i].lower() == "car":
                    car_prob = prob
                elif class_names[i].lower() == "bike":
                    bike_prob = prob
                elif class_names[i].lower() == "empty":
                    empty_prob = prob
            output_str = output_str[:-2]  # Remove the last comma and space
            print(output_str)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            car_dir = os.path.join("training_data", "car")
            bike_dir = os.path.join("training_data", "bike")
            empty_dir = os.path.join("training_data", "empty")

            os.makedirs(car_dir, exist_ok=True)
            os.makedirs(bike_dir, exist_ok=True)
            os.makedirs(empty_dir, exist_ok=True)


            if car_prob is not None and car_prob > 0.5:
                print("\nCar detected.")
                # file_path = os.path.join(car_dir, f"{timestamp}.jpg")
                # with open(file_path, "wb") as f:
                #     f.write(imageBytes)
            if bike_prob is not None and bike_prob > 0.5:
                print("\nBike detected.")
                # file_path = os.path.join(bike_dir, f"{timestamp}.jpg")
                # with open(file_path, "wb") as f:
                #     f.write(imageBytes)
            if empty_prob is not None and empty_prob > 0.5:
                print("\nEmpty detected.")
                # file_path = os.path.join(empty_dir, f"{timestamp}.jpg")
                # with open(file_path, "wb") as f:
                #     f.write(imageBytes)
            

            print("--------------\n")
            return output_str
        else:
            print("No predictions found.")
            return None
    except Exception as e:
        print("Ride crashed ---------------------------------")
        print(e)
        print("---------------------------------")
        return None
