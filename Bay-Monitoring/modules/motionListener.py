import asyncio
import signal
import json
import logging
import sys
import threading
import time


import httpx
import paho.mqtt.client as mqtt

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global stop event for graceful shutdown
stop_event = threading.Event()

# MQTT Configuration
BROKER_IP = "***********"
MQTT_PORT = 1883
CLIENT_ID = "motion_listener_001"
SUB_TOPIC = "motion"
FILTER_PREFIX = "axis:CameraApplicationPlatform/VMD/Camera"
TIME_THRESHOLD_MS = 12000  # 5 seconds in milliseconds

# API Configuration
API_URL = "http://***********:8000/cameras/"

# Global data structures
camera_info = {}  # {serial: {"ip": camera_ip, "username": "root", "password": "M1ecrdry1!"}}
clusters = {}     # {serial: {"last_time": timestamp, "count": n}}

########################################
# ASYNC HTTP FUNCTIONS USING HTTPX
########################################

async def fetch_camera_info():
    """
    Asynchronously fetch camera information from the API.
    """
    global camera_info
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(API_URL, timeout=10)
            response.raise_for_status()
            cameras = response.json()
            info = {}
            for camera in cameras:
                serial = camera.get("serial")
                cam_ip = camera.get("camera_ip")
                if serial and cam_ip:
                    info[serial] = {
                        "ip": cam_ip,
                        "username": "root",
                        "password": "M1ecrdry1!"
                    }
            camera_info = info
            logger.info("Camera info fetched successfully")
            return info
    except httpx.RequestError as e:
        logger.error(f"Error fetching camera information: {e}")
        return {}

async def trigger_guard_tours(serial):
    """
    Sequentially trigger guard tours for a given camera.
    Each tour group is started and then stopped after 4 minutes.
    If a 401 Unauthorized is received, the request is retried.
    """
    if serial not in camera_info:
        logger.error(f"No camera info found for serial {serial}")
        return

    cam = camera_info[serial]
    cam_ip = cam["ip"]
    username = cam["username"]
    password = cam["password"]

    group_number = 0
    auth = httpx.DigestAuth(username, password)
    async with httpx.AsyncClient(auth=auth, timeout=10.0) as client:
        # Optionally prime the auth handshake (this call may return 401,
        # but it sets up the digest auth parameters for subsequent requests)
        try:
            await client.get(f"http://{cam_ip}/axis-cgi/param.cgi?action=info")
        except Exception as e:
            logger.warning(f"Auth priming failed: {e}")

        while True:
            group_param = f"GuardTour.G{group_number}"
            start_url = f"http://{cam_ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=yes"
            try:
                resp = await client.get(start_url)
                if resp.status_code == 401:
                    logger.info(f"[{serial}] Received 401 on start request, retrying...")
                    resp = await client.get(start_url)
                response_text = resp.text.strip()
                logger.info(f"[{serial}] Checking {group_param}: Response: {response_text}")
                if ("Error -1 getting param" in response_text) or ("Error setting" in response_text):
                    logger.info(f"[{serial}] Error in {group_param}: {response_text}")
                    break
            except httpx.RequestError as e:
                logger.error(f"Error starting tour group {group_param} for {serial}: {e}")
                break

            logger.info(f"[{serial}] Tour group {group_param} started (status: {resp.status_code}).")
            # Wait 4 minutes (240 seconds) for the tour to run.
            await asyncio.sleep(120)

            stop_url = f"http://{cam_ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=no"
            try:
                stop_resp = await client.get(stop_url)
                if stop_resp.status_code == 401:
                    logger.info(f"[{serial}] Received 401 on stop request, retrying...")
                    stop_resp = await client.get(stop_url)
                stop_resp.raise_for_status()
                logger.info(f"[{serial}] Tour group {group_param} stopped (status: {stop_resp.status_code}).")
            except httpx.RequestError as e:
                logger.error(f"Error stopping tour group {group_param} for {serial}: {e}")
            # Brief pause between tours
            await asyncio.sleep(1)
            group_number += 1

        if group_number == 0:
            logger.info(f"[{serial}] No guard tours found.")
        else:
            logger.info(f"[{serial}] Completed processing {group_number} guard tour groups.")

async def stop_guard_tour(client, cam_ip, group_param, serial):
    """
    Send the command to stop a specific guard tour group.
    """
    stop_url = f"http://{cam_ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=no"
    try:
        stop_resp = await client.get(stop_url)
        stop_resp.raise_for_status()
        logger.info(f"[{serial}] Tour group {group_param} stopped (status: {stop_resp.status_code}).")
    except httpx.RequestError as e:
        logger.error(f"Error stopping tour group {group_param} for {serial}: {e}")

async def process_message(payload):
    """
    Process an incoming MQTT message payload asynchronously.
    Filters messages by topic prefix, updates the event cluster for the camera,
    and triggers guard tours if the event count threshold is exceeded.
    """
    try:
        # Check if the payload's "topic" field starts with FILTER_PREFIX.
        topic_field = payload.get("topic", "")
        if not topic_field.startswith(FILTER_PREFIX):
            return

        serial = payload.get("serial")
        timestamp = payload.get("timestamp")
        if serial is None or timestamp is None:
            logger.error(f"Missing 'serial' or 'timestamp' in message: {payload}")
            return

        current_time = int(timestamp)  # Assuming timestamp is in milliseconds

        if serial not in clusters:
            clusters[serial] = {"last_time": current_time, "count": 1}
            logger.info(f"[{serial}] New event cluster started, count = 1")
        else:
            last_time = clusters[serial]["last_time"]
            if current_time - last_time > TIME_THRESHOLD_MS:
                clusters[serial]["count"] = 1
                clusters[serial]["last_time"] = current_time
                logger.info(f"[{serial}] Time gap exceeded. Count reset to 1")
            else:
                clusters[serial]["count"] += 1
                clusters[serial]["last_time"] = current_time
                logger.info(f"[{serial}] Updated event count = {clusters[serial]['count']}")
                if clusters[serial]["count"] > 3:  # Threshold condition
                    logger.info(f"[{serial}] Excess event detected (count={clusters[serial]['count']}). Triggering guard tours.")
                    # Schedule the guard tour asynchronously without blocking the MQTT callback.
                    asyncio.create_task(trigger_guard_tours(serial))
                    clusters[serial]["count"] = 0
    except Exception as e:
        logger.error(f"Error processing message: {e}")

########################################
# MQTT CALLBACKS
########################################

def on_connect(client, userdata, flags, rc, properties=None):
    logger.info(f"Connected with result code: {rc}")
    client.subscribe(SUB_TOPIC)
    logger.info(f"Subscribed to topic: {SUB_TOPIC}")

def on_message(client, userdata, msg):
    # Ignore retained messages (optional)
    if msg.retain:
        return
    try:
        payload = json.loads(msg.payload.decode("utf-8"))
        #logger.info(f"Message received on topic {msg.topic}: {payload}")
        loop = userdata.get("loop")
        # Schedule the asynchronous processing of the message.
        asyncio.run_coroutine_threadsafe(process_message(payload), loop)
    except Exception as e:
        logger.error(f"Error in on_message: {e}")

########################################
# MQTT CLIENT LOOP (Async Wrapper)
########################################

async def mqtt_client_loop(loop):
    """
    Start the MQTT client in a separate thread and keep the async loop running.
    """
    client = mqtt.Client(client_id=CLIENT_ID, clean_session=False, callback_api_version=mqtt.CallbackAPIVersion.VERSION2)
    userdata = {"loop": loop}
    client.user_data_set(userdata)
    client.on_connect = on_connect
    client.on_message = on_message

    try:
        client.connect(BROKER_IP, MQTT_PORT, 60)
    except Exception as e:
        logger.error(f"Error connecting to MQTT Broker: {e}")
        return

    thread = threading.Thread(target=client.loop_forever)
    thread.daemon = True
    thread.start()
    logger.info("MQTT client loop thread started")

    # Keep running until stop_event is set.
    while not stop_event.is_set():
        await asyncio.sleep(1)
    client.disconnect()
    logger.info("MQTT client disconnected.")

########################################
# MAIN ASYNC FUNCTION
########################################

async def main():
    if sys.version_info < (3, 7):
        raise Exception("Python 3.7+ is required.")
    logger.info("Starting async MQTT client with HTTPX integration")

    loop = asyncio.get_running_loop()

    # Setup graceful termination
    def termination_handler():
        logger.info("Termination signal received. Stopping...")
        stop_event.set()

    for sig in (signal.SIGTERM, signal.SIGINT):
        try:
            loop.add_signal_handler(sig, termination_handler)
        except NotImplementedError:
            signal.signal(sig, lambda s, f: termination_handler())

    # Fetch camera info asynchronously
    await fetch_camera_info()

    # Start the MQTT client loop (runs until stop_event is set)
    await mqtt_client_loop(loop)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Exception in main: {e}")
