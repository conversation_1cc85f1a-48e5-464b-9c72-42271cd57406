# firebase_handler.py

import firebase_admin
from firebase_admin import credentials, storage
import os
import logging
from typing import Union

# Get a logger instance specific to this module
# It will inherit configuration from the main script's logging setup
logger = logging.getLogger(__name__)

# --- Configuration ---
# Consider loading these from environment variables or a config file for production
# For simplicity now, keeping them as constants:
SERVICE_ACCOUNT_KEY_PATH = '/home/<USER>/Desktop/barangarooadmin-firebase-adminsdk-ukwil-a956f81941.json'  # <-- IMPORTANT: Update this path
FIREBASE_STORAGE_BUCKET = 'barangarooadmin.appspot.com'     # <-- IMPORTANT: Update this bucket name

# --- Module-level Globals ---
# These will hold the initialized app and bucket references for this module
_firebase_app = None
_firebase_bucket = None

def initialize_firebase() -> bool:
    """
    Initializes the Firebase Admin SDK using credentials and bucket info.
    Should be called once when the application starts.

    Returns:
        bool: True if initialization was successful or already done, False otherwise.
    """
    global _firebase_app, _firebase_bucket

    # Prevent re-initialization if already done successfully
    if _firebase_bucket:
        logger.info("Firebase already initialized.")
        return True

    try:
        # Best Practice: Use Environment Variable if available
        cred_path = os.environ.get("GOOGLE_APPLICATION_CREDENTIALS", SERVICE_ACCOUNT_KEY_PATH)

        if not os.path.exists(cred_path):
            logger.error(f"Firebase Service Account Key not found at: {cred_path}")
            logger.error("Please set GOOGLE_APPLICATION_CREDENTIALS or update SERVICE_ACCOUNT_KEY_PATH.")
            return False

        cred = credentials.Certificate(cred_path)

        # Check if the default app already exists before initializing
        try:
             _firebase_app = firebase_admin.get_app()
             logger.info("Firebase app already exists, using existing default app.")
        except ValueError:
             # Initialize if it doesn't exist
             _firebase_app = firebase_admin.initialize_app(cred, {
                 'storageBucket': FIREBASE_STORAGE_BUCKET
             })
             logger.info("Firebase Admin SDK initialized successfully.")

        _firebase_bucket = storage.bucket(app=_firebase_app) # Get bucket instance
        logger.info(f"Obtained Firebase Storage bucket reference: {FIREBASE_STORAGE_BUCKET}")
        return True

    except Exception as e:
        logger.error(f"An unexpected error occurred during Firebase initialization: {e}", exc_info=True)
        _firebase_app = None # Ensure state reflects failure
        _firebase_bucket = None
        return False

def upload_to_firebase_storage(image_bytes: bytes, destination_blob_name: str) -> Union[str, None]:
    """
    Uploads image bytes to Firebase Cloud Storage and returns the public URL.

    Args:
        image_bytes: The raw bytes of the image to upload.
        destination_blob_name: The full path and filename for the image
                               within the Firebase Storage bucket.

    Returns:
        str: The public URL of the uploaded image, or None if upload fails.
    """
    global _firebase_bucket # Access the module-level bucket instance

    if not image_bytes:
        logger.error("Cannot upload to Firebase: No image bytes provided.")
        return None

    if not _firebase_bucket:
        logger.error("Cannot upload to Firebase: Firebase Storage bucket not initialized.")
        # You might attempt re-initialization here, but it's often better
        # to ensure initialize_firebase() succeeded at startup.
        return None

    try:
        blob = _firebase_bucket.blob(destination_blob_name)

        blob.upload_from_string(
            image_bytes,
            content_type='image/jpeg' # Adjust if necessary
        )
        logger.info(f"Successfully uploaded image to Firebase Storage: gs://{FIREBASE_STORAGE_BUCKET}/{destination_blob_name}")

        try:
            # Crucial step: Make the blob publicly readable
            #blob.make_public()
            logger.info(f"Blob made public: {destination_blob_name}")
        except Exception as acl_error:
            logger.error(f"Failed to make blob public (check permissions/IAM roles for service account {SERVICE_ACCOUNT_KEY_PATH}): {acl_error}")
            # Consider if you still want to return a URL here.
            # blob.public_url might work IF bucket has Uniform Bucket-Level Access disabled AND default object ACL is public-read,
            # but explicitly setting it is more reliable. Let's return None on failure here.
            return None

        public_url = blob.public_url
        logger.info(f"Public URL: {public_url}")
        return public_url

    except Exception as e:
        logger.error(f"Firebase upload failed for {destination_blob_name}: {e}", exc_info=True)
        return None

# Optional: Add a check for direct execution
if __name__ == '__main__':
    print("This module provides Firebase handling functions and should be imported.")
    # You could add test code here, e.g., try initializing
    # logging.basicConfig(level=logging.INFO) # Need basic config for testing
    # if initialize_firebase():
    #      print("Firebase initialized successfully (for testing).")
    # else:
    #      print("Firebase initialization failed (for testing).")