{"version": "0.2.0", "configurations": [{"name": "Webhook Remote Debug (Python)", "type": "python", "request": "attach", "port": 5678, "host": "localhost", "logToFile": true, "redirectOutput": true, "pathMappings": [{"localRoot": "${workspaceFolder}/modules/Webhook", "remoteRoot": "/app"}], "windows": {"pathMappings": [{"localRoot": "${workspaceFolder}\\modules\\Webhook", "remoteRoot": "/app"}]}}, {"name": "Webhook Local Debug (Python)", "type": "python", "request": "launch", "program": "${workspaceFolder}/modules/Webhook/main.py", "console": "integratedTerminal", "env": {"EdgeHubConnectionString": "${config:azure-iot-edge.EdgeHubConnectionString}", "EdgeModuleCACertificateFile": "${config:azure-iot-edge.EdgeModuleCACertificateFile}"}, "windows": {"program": "${workspaceFolder}\\modules\\Webhook\\main.py"}}]}