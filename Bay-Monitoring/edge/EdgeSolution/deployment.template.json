{"$schema-template": "4.0.0", "modulesContent": {"$edgeAgent": {"properties.desired": {"schemaVersion": "1.1", "runtime": {"type": "docker", "settings": {"minDockerVersion": "v1.25", "loggingOptions": "", "registryCredentials": {"ddilabs": {"username": "$CONTAINER_REGISTRY_USERNAME_ddilabs", "password": "$CONTAINER_REGISTRY_PASSWORD_ddilabs", "address": "ddilabs.azurecr.io"}}}}, "systemModules": {"edgeAgent": {"type": "docker", "settings": {"image": "mcr.microsoft.com/azureiotedge-agent:1.5", "createOptions": {}}}, "edgeHub": {"type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "mcr.microsoft.com/azureiotedge-hub:1.5", "createOptions": {"HostConfig": {"PortBindings": {"5671/tcp": [{"HostPort": "5671"}], "8883/tcp": [{"HostPort": "8883"}], "443/tcp": [{"HostPort": "443"}]}}}}}}, "modules": {"MQTTServer": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "eclipse-m<PERSON><PERSON><PERSON>", "createOptions": {"HostConfig": {"NetworkMode": "host", "Binds": ["/srv/ddi:/mosquitto/config"], "LogConfig": {"Type": "json-file", "Config": {"max-size": "50m", "max-file": "2"}}}, "NetworkingConfig": {"EndpointsConfig": {"host": {}}}}}}, "MongoDB": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "amd64/mongo", "createOptions": {"HostConfig": {"NetworkMode": "host", "Binds": ["/home/<USER>/mongodb/database:/data/db"], "LogConfig": {"Type": "json-file", "Config": {"max-size": "50m", "max-file": "2"}}}, "NetworkingConfig": {"EndpointsConfig": {"host": {}}}}}}, "BayAlerts": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.BayAlerts}", "createOptions": {"HostConfig": {"Binds": ["/run/systemd:/run/systemd", "/var/run/docker.sock:/var/run/docker.sock", "/srv/ddi/Raw/:/app/Config"], "NetworkMode": "host", "LogConfig": {"Type": "json-file", "Config": {"max-size": "50m", "max-file": "2"}}}, "NetworkingConfig": {"EndpointsConfig": {"host": {}}}}}, "env": {"AZURE_AD_CLIENT_ID": {"value": "$AZURE_AD_CLIENT_ID"}, "AZURE_AD_TENANT_ID": {"value": "$AZURE_AD_TENANT_ID"}, "AZURE_AD_USERNAME": {"value": "$AZURE_AD_USERNAME"}, "AZURE_AD_PASSWORD": {"value": "$AZURE_AD_PASSWORD"}}}, "Motion": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.Motion}", "createOptions": {"HostConfig": {"Binds": ["/run/systemd:/run/systemd", "/var/run/docker.sock:/var/run/docker.sock", "/srv/ddi/Raw/:/app/Config"], "NetworkMode": "host", "LogConfig": {"Type": "json-file", "Config": {"max-size": "50m", "max-file": "2"}}}, "NetworkingConfig": {"EndpointsConfig": {"host": {}}}}}, "env": {"CAMERA_USERNAME": {"value": "$CAMERA_USERNAME"}, "CAMERA_PASSWORD": {"value": "$CAMERA_PASSWORD"}}}, "Schedule": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.Schedule}", "createOptions": {"HostConfig": {"Binds": ["/run/systemd:/run/systemd", "/var/run/docker.sock:/var/run/docker.sock", "/srv/ddi/Raw/:/app/Config"], "NetworkMode": "host", "LogConfig": {"Type": "json-file", "Config": {"max-size": "50m", "max-file": "2"}}}, "NetworkingConfig": {"EndpointsConfig": {"host": {}}}}}}, "VehicleDetect": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.VehicleDetect}", "createOptions": {"HostConfig": {"Binds": ["/run/systemd:/run/systemd", "/var/run/docker.sock:/var/run/docker.sock", "/srv/ddi/Raw/:/app/Config"], "NetworkMode": "host", "LogConfig": {"Type": "json-file", "Config": {"max-size": "50m", "max-file": "2"}}}, "NetworkingConfig": {"EndpointsConfig": {"host": {}}}}}}, "Webhook": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.Webhook}", "createOptions": {}}}}}}, "$edgeHub": {"properties.desired": {"schemaVersion": "1.2", "routes": {"SampleModuleToIoTHub": "FROM /messages/modules/SampleModule/outputs/* INTO $upstream", "sensorToSampleModule": "FROM /messages/modules/SimulatedTemperatureSensor/outputs/temperatureOutput INTO BrokeredEndpoint(\"/modules/SampleModule/inputs/input1\")", "MotionToIoTHub": "FROM /messages/modules/Motion/outputs/* INTO $upstream", "scheduleToIoTHub": "FROM /messages/modules/schedule/outputs/* INTO $upstream", "VehicleDetectToIoTHub": "FROM /messages/modules/VehicleDetect/outputs/* INTO $upstream", "WebhookToIoTHub": "FROM /messages/modules/Webhook/outputs/* INTO $upstream"}, "storeAndForwardConfiguration": {"timeToLiveSecs": 7200}}}, "VehicleDetect": {"properties.desired": {"mqttBrokerIp": "***********", "mqttTopic": "motion/#", "apiServerIp": "***********", "plateRecognizerIp": "***********", "webhookIp": "***********"}}, "BayAlerts": {"properties.desired": {"mqttBrokerIp": "***********", "mqttTopic": "update", "dashboardIp": "***********"}}, "Schedule": {"properties.desired": {"apiBaseUrl": "http://***********:8000", "vacantTimes": "09:00", "occupiedTimes": "22:00", "timezone": "Australia/Sydney"}}, "Motion": {"properties.desired": {"mqttBrokerIp": "***********", "mqttTopic": "motion", "cameraApiIp": "***********"}}}}