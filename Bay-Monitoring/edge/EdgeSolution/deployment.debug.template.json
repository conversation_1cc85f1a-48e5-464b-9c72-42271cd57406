{"$schema-template": "4.0.0", "modulesContent": {"$edgeAgent": {"properties.desired": {"schemaVersion": "1.4", "runtime": {"type": "docker", "settings": {"minDockerVersion": "v1.25", "loggingOptions": "", "registryCredentials": {"ddilabs": {"username": "$CONTAINER_REGISTRY_USERNAME_ddilabs", "password": "$CONTAINER_REGISTRY_PASSWORD_ddilabs", "address": "ddilabs.azurecr.io"}}}}, "systemModules": {"edgeAgent": {"type": "docker", "settings": {"image": "mcr.microsoft.com/azureiotedge-agent:1.4", "createOptions": {}}}, "edgeHub": {"type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "mcr.microsoft.com/azureiotedge-hub:1.4", "createOptions": {"HostConfig": {"PortBindings": {"5671/tcp": [{"HostPort": "5671"}], "8883/tcp": [{"HostPort": "8883"}], "443/tcp": [{"HostPort": "443"}]}}}}}}, "modules": {"SampleModule": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.SampleModule.debug}", "createOptions": {"ExposedPorts": {"5678/tcp": {}}, "HostConfig": {"PortBindings": {"5678/tcp": [{"HostPort": "5678"}]}}}}}, "SimulatedTemperatureSensor": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "mcr.microsoft.com/azureiotedge-simulated-temperature-sensor:1.4", "createOptions": {}}}, "Motion": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.Motion.debug}", "createOptions": {"ExposedPorts": {"5678/tcp": {}}, "HostConfig": {"PortBindings": {"5678/tcp": [{"HostPort": "5678"}]}}}}}, "schedule": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.schedule.debug}", "createOptions": {"ExposedPorts": {"5678/tcp": {}}, "HostConfig": {"PortBindings": {"5678/tcp": [{"HostPort": "5678"}]}}}}}, "VehicleDetect": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.VehicleDetect.debug}", "createOptions": {"ExposedPorts": {"5678/tcp": {}}, "HostConfig": {"PortBindings": {"5678/tcp": [{"HostPort": "5678"}]}}}}}, "Webhook": {"version": "1.0", "type": "docker", "status": "running", "restartPolicy": "always", "settings": {"image": "${MODULES.Webhook.debug}", "createOptions": {"ExposedPorts": {"5678/tcp": {}}, "HostConfig": {"PortBindings": {"5678/tcp": [{"HostPort": "5678"}]}}}}}}}}, "$edgeHub": {"properties.desired": {"schemaVersion": "1.4", "routes": {"SampleModuleToIoTHub": "FROM /messages/modules/SampleModule/outputs/* INTO $upstream", "sensorToSampleModule": "FROM /messages/modules/SimulatedTemperatureSensor/outputs/temperatureOutput INTO BrokeredEndpoint(\"/modules/SampleModule/inputs/input1\")", "MotionToIoTHub": "FROM /messages/modules/Motion/outputs/* INTO $upstream", "scheduleToIoTHub": "FROM /messages/modules/schedule/outputs/* INTO $upstream", "VehicleDetectToIoTHub": "FROM /messages/modules/VehicleDetect/outputs/* INTO $upstream", "WebhookToIoTHub": "FROM /messages/modules/Webhook/outputs/* INTO $upstream"}, "storeAndForwardConfiguration": {"timeToLiveSecs": 7200}}}}}