# Base image
FROM amd64/python:3.13-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy the model files
COPY car_nocar_1_2.pt .
COPY car_bike_empty_1_3.pt .

# Copy the Python files
COPY caryapa.py .
COPY car_detector_module.py .

# Set environment variable for debugging
ENV PYTHONUNBUFFERED=1
ENV LOG_LEVEL=DEBUG

# Verify files after copying
#RUN ls -la /app

# Entrypoint
CMD ["python", "-u", "car_detector_module.py"]
