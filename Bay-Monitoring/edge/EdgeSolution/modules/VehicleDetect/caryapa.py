from ultralytics import YOLO
from PIL import Image
import io
from datetime import datetime
import os
import logging
import torch

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    logger.info("Loading YOLO models...")
    # Use torch's context manager to temporarily allow the required class
    with torch.serialization.safe_globals(['ultralytics.nn.tasks.ClassificationModel']):
        model = YOLO('./car_nocar_1_2.pt')
        fullmodel = YOLO('./car_bike_empty_1_3.pt')
    logger.info("YOLO models loaded successfully")
except Exception as e:
    logger.error(f"Failed to load YOLO models: {e}")
    raise

def car_or_nocar(imageBytes):
    try:
        logger.info("Starting car detection...")
        car_bike_empty(imageBytes)
        
        logger.debug("Writing temporary image file...")
        with open("tempyapacar.jpg", "wb") as f:
            f.write(imageBytes)
            
        logger.debug("Opening image with PIL...")
        image = Image.open(io.BytesIO(imageBytes))
        
        logger.debug("Running YOLO model...")
        results = model(image, verbose=False)

        if results and results[0].probs is not None:
            probs = results[0].probs.data.tolist()
            class_names = model.names
            
            car_prob = None
            bike_prob = None
            
            for i, prob in enumerate(probs):
                if class_names[i].lower() == "car":
                    car_prob = prob
                    logger.info(f"Car probability: {car_prob:.2f}")
                elif class_names[i].lower() == "bike":
                    bike_prob = prob
                    logger.info(f"Bike probability: {bike_prob:.2f}")

            if car_prob is None or car_prob < 0.5:
                if bike_prob is not None and bike_prob > 0.5:
                    logger.info(f"Returning bike probability: {bike_prob:.2f}")
                    return bike_prob
                else:
                    logger.info("No vehicle detected")
                    return None
            
            logger.info(f"Returning car probability: {car_prob:.2f}")
            return car_prob
        else:
            logger.warning("No predictions found")
            return None
    except Exception as e:
        logger.error(f"Car detection error: {e}", exc_info=True)
        return None


def car_bike_empty(imageBytes):
    try:
        image = Image.open(io.BytesIO(imageBytes))
        results = fullmodel(image, verbose=False)
        if results and results[0].probs is not None:
            # Convert tensor to list
            probs = results[0].probs.data.tolist()
            class_names = fullmodel.names
            output_str = ""
            car_prob = None
            bike_prob = None
            empty_prob = None
            # Build output string and capture probability for 'car'
            for i, prob in enumerate(probs):
                output_str += f"{class_names[i]} {prob:.2f}, "
                if class_names[i].lower() == "car":
                    car_prob = prob
                elif class_names[i].lower() == "bike":
                    bike_prob = prob
                elif class_names[i].lower() == "empty":
                    empty_prob = prob
            output_str = output_str[:-2]  # Remove the last comma and space
            print(output_str)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            car_dir = os.path.join("training_data", "car")
            bike_dir = os.path.join("training_data", "bike")
            empty_dir = os.path.join("training_data", "empty")

            os.makedirs(car_dir, exist_ok=True)
            os.makedirs(bike_dir, exist_ok=True)
            os.makedirs(empty_dir, exist_ok=True)

            if car_prob is not None and car_prob > 0.5:
                print("\nCar detected.")
                # file_path = os.path.join(car_dir, f"{timestamp}.jpg")
                # with open(file_path, "wb") as f:
                #     f.write(imageBytes)
            if bike_prob is not None and bike_prob > 0.5:
                print("\nBike detected.")
                # file_path = os.path.join(bike_dir, f"{timestamp}.jpg")
                # with open(file_path, "wb") as f:
                #     f.write(imageBytes)
            if empty_prob is not None and empty_prob > 0.5:
                print("\nEmpty detected.")
                # file_path = os.path.join(empty_dir, f"{timestamp}.jpg")
                # with open(file_path, "wb") as f:
                #     f.write(imageBytes)
            
            print("--------------\n")
            
            # Return the higher probability between car and bike
            if car_prob is not None and bike_prob is not None:
                if car_prob > bike_prob:
                    logger.info(f"Car probability ({car_prob:.2f}) is higher than bike probability ({bike_prob:.2f})")
                    return car_prob
                else:
                    logger.info(f"Bike probability ({bike_prob:.2f}) is higher than car probability ({car_prob:.2f})")
                    return bike_prob
            elif car_prob is not None:
                logger.info(f"Only car probability ({car_prob:.2f}) is available")
                return car_prob
            elif bike_prob is not None:
                logger.info(f"Only bike probability ({bike_prob:.2f}) is available")
                return bike_prob
            else:
                logger.info("Neither car nor bike probability is available")
                return None
        else:
            print("No predictions found.")
            return None
    except Exception as e:
        print("Ride crashed ---------------------------------")
        print(e)
        print("---------------------------------")
        return None
