# car_detector_module.py
import asyncio
import signal
import json
import time
import logging
import sys
import os
from typing import Dict, Any, Optional
from datetime import datetime

# --- Logging ---
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s  %(levelname)-8s | %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("EdgeCarDetectorModule")

# Add debug logging for current directory and files
logger.info(f"Current working directory: {os.getcwd()}")
logger.info(f"Files in current directory: {os.listdir('.')}")

# Third-party libraries
import httpx
import pytz
from azure.iot.device.aio import IoTHubModuleClient
from asyncio_mqtt import Client as MQTTClient, MqttError

# Custom detection logic (ensure this file is in the same directory or installable)
try:
    # Assuming car_or_nocar is synchronous and takes image bytes
    from caryapa import car_bike_empty
    logger.info("Successfully imported car_bike_empty function")
except ImportError as e:
    logger.error(f"Failed to import 'car_bike_empty' from 'caryapa': {str(e)}")
    logger.error("Ensure caryapa.py exists and all its dependencies are installed.")
    # Define a dummy function to allow the module to load, but log errors
    def car_bike_empty(image_bytes: bytes) -> float:
        logger.error("caryapa.car_bike_empty function is not available!")
        return 1.0 # Default to car if function is missing

# ────────────────────────────────────────────────────────────
# Configuration (Defaults, Overridable by Environment/Twin)
# ────────────────────────────────────────────────────────────

# --- MQTT Configuration ---
MQTT_BROKER = os.getenv("MQTT_BROKER", "***********")
MQTT_PORT = int(os.getenv("MQTT_PORT", 1883))
MQTT_TOPIC = os.getenv("MQTT_TOPIC", "motion/#") # Subscribe to motion topic hierarchy
MQTT_CLIENT_ID = "EdgeCarDetectorModule"

# --- API Configuration ---
API_SERVER_IP = os.getenv("API_SERVER_IP", "***********") # For camera list & event increment
PLATE_RECOGNIZER_IP = os.getenv("PLATE_RECOGNIZER_IP", API_SERVER_IP) # Default to same IP
WEBHOOK_IP = os.getenv("WEBHOOK_IP", "***********") # IP for the no-car webhook

CAMERA_API_URL = f"http://{API_SERVER_IP}:8000/cameras/"
EVENTS_API_URL_TPL = f"http://{API_SERVER_IP}:8000/cameras/{{serial}}/events/increment" # Use template
PLATE_RECOGNIZER_API_URL = f"http://{PLATE_RECOGNIZER_IP}:8080/v1/plate-reader/"
WEBHOOK_URL = f"http://{WEBHOOK_IP}:8082/webhook"

# Camera credentials (Consider using secrets management)
CAMERA_USERNAME  = os.getenv("CAMERA_USERNAME", "root")
CAMERA_PASSWORD  = os.getenv("CAMERA_PASSWORD", "M1ecrdry1!")

# --- Processing Logic Configuration ---
# Minimum time (seconds) between processing the same camera+preset
PRESET_LOCK_DURATION = int(os.getenv("PRESET_LOCK_DURATION", 10))
# Probability threshold to consider a car detected
CAR_DETECTION_THRESHOLD = float(os.getenv("CAR_DETECTION_THRESHOLD", 0.50))
# Flag to save images when no car is detected (for training)
SAVE_NOCAR_IMAGES = os.getenv("SAVE_NOCAR_IMAGES", "False").lower() == "true"
TRAINING_DATA_PATH = "/app/training_data/no_car" # Default path inside container

# ────────────────────────────────────────────────────────────
# Global State
# ────────────────────────────────────────────────────────────
camera_ip_map: Dict[str, str] = {} # serial -> ip_address
# Tracks the timestamp of the last successful processing for a camera+preset
camera_preset_last_processed: Dict[str, float] = {} # "serial_preset" -> unix_timestamp
# Tracks which cameras are currently being processed (simple flag)
camera_processing_active: Dict[str, bool] = {} # serial -> True/False

# ────────────────────────────────────────────────────────────
# Configuration Apply Function
# ────────────────────────────────────────────────────────────
def apply_config(cfg: dict):
    """Applies configuration settings from twin desired properties."""
    global MQTT_BROKER, MQTT_PORT, MQTT_TOPIC, API_SERVER_IP, PLATE_RECOGNIZER_IP, WEBHOOK_IP, \
           CAMERA_USERNAME, CAMERA_PASSWORD, PRESET_LOCK_DURATION, CAR_DETECTION_THRESHOLD, \
           SAVE_NOCAR_IMAGES, CAMERA_API_URL, EVENTS_API_URL_TPL, PLATE_RECOGNIZER_API_URL, WEBHOOK_URL

    updated_settings = []

    # --- MQTT ---
    new_broker = cfg.get("mqttBrokerIp", MQTT_BROKER)
    if new_broker != MQTT_BROKER: MQTT_BROKER = new_broker; updated_settings.append("MQTT_BROKER")
    new_port = int(cfg.get("mqttPort", MQTT_PORT))
    if new_port != MQTT_PORT: MQTT_PORT = new_port; updated_settings.append("MQTT_PORT")
    new_topic = cfg.get("mqttTopic", MQTT_TOPIC)
    if new_topic != MQTT_TOPIC: MQTT_TOPIC = new_topic; updated_settings.append("MQTT_TOPIC")

    # --- APIs ---
    new_api_ip = cfg.get("apiServerIp", API_SERVER_IP)
    if new_api_ip != API_SERVER_IP:
        API_SERVER_IP = new_api_ip
        CAMERA_API_URL = f"http://{API_SERVER_IP}:8000/cameras/" # Rebuild URL
        EVENTS_API_URL_TPL = f"http://{API_SERVER_IP}:8000/cameras/{{serial}}/events/increment"
        updated_settings.append("API_SERVER_IP")
    new_plate_ip = cfg.get("plateRecognizerIp", PLATE_RECOGNIZER_IP)
    if new_plate_ip != PLATE_RECOGNIZER_IP:
        PLATE_RECOGNIZER_IP = new_plate_ip
        PLATE_RECOGNIZER_API_URL = f"http://{PLATE_RECOGNIZER_IP}:8080/v1/plate-reader/"
        updated_settings.append("PLATE_RECOGNIZER_IP")
    new_webhook_ip = cfg.get("webhookIp", WEBHOOK_IP)
    if new_webhook_ip != WEBHOOK_IP:
        WEBHOOK_IP = new_webhook_ip
        WEBHOOK_URL = f"http://{WEBHOOK_IP}:8082/webhook"
        updated_settings.append("WEBHOOK_IP")

    # --- Camera ---
    new_cam_user = cfg.get("cameraUsername", CAMERA_USERNAME)
    if new_cam_user != CAMERA_USERNAME: CAMERA_USERNAME = new_cam_user; updated_settings.append("CAMERA_USERNAME")
    new_cam_pass = cfg.get("cameraPassword", CAMERA_PASSWORD) # Be careful logging/handling passwords
    if new_cam_pass != CAMERA_PASSWORD: CAMERA_PASSWORD = new_cam_pass; updated_settings.append("CAMERA_PASSWORD")

    # --- Logic ---
    new_lock_dur = int(cfg.get("presetLockDuration", PRESET_LOCK_DURATION))
    if new_lock_dur != PRESET_LOCK_DURATION: PRESET_LOCK_DURATION = new_lock_dur; updated_settings.append("PRESET_LOCK_DURATION")
    new_car_thresh = float(cfg.get("carDetectionThreshold", CAR_DETECTION_THRESHOLD))
    if new_car_thresh != CAR_DETECTION_THRESHOLD: CAR_DETECTION_THRESHOLD = new_car_thresh; updated_settings.append("CAR_DETECTION_THRESHOLD")
    new_save_flag = str(cfg.get("saveNoCarImages", str(SAVE_NOCAR_IMAGES))).lower() == "true"
    if new_save_flag != SAVE_NOCAR_IMAGES: SAVE_NOCAR_IMAGES = new_save_flag; updated_settings.append("SAVE_NOCAR_IMAGES")

    if updated_settings:
        logger.info(f"🛠 Config updated via twin: {', '.join(updated_settings)}")
    else:
        logger.info("🛠 Received config update, but no values changed or no recognized keys found.")
    # Log current state (excluding password)
    logger.info(f"🛠 Current effective config → MQTT={MQTT_BROKER}:{MQTT_PORT} (Topic:{MQTT_TOPIC}), API_IP={API_SERVER_IP}, PLATE_IP={PLATE_RECOGNIZER_IP}, WEBHOOK_IP={WEBHOOK_IP}, CAM_USER={CAMERA_USERNAME}, LOCK_DUR={PRESET_LOCK_DURATION}s, CAR_THRESH={CAR_DETECTION_THRESHOLD}, SAVE_NOCAR={SAVE_NOCAR_IMAGES}")

# ────────────────────────────────────────────────────────────
# Async HTTP Functions (using httpx)
# ────────────────────────────────────────────────────────────

async def initialize_camera_ip_map(client: httpx.AsyncClient):
    """Fetches camera list and populates the global camera_ip_map."""
    global camera_ip_map
    logger.info(f"Attempting camera discovery from {CAMERA_API_URL}...")
    try:
        response = await client.get(CAMERA_API_URL, timeout=15)
        response.raise_for_status()
        cameras = response.json()
        new_map = {cam['serial']: cam['camera_ip'] for cam in cameras if 'serial' in cam and 'camera_ip' in cam}
        if not new_map:
             logger.warning("Camera discovery returned no valid cameras.")
        camera_ip_map = new_map
        logger.info(f"✅ Camera IP mapping initialized/updated successfully. Found {len(camera_ip_map)} cameras.")
    except httpx.RequestError as e:
        logger.error(f"❌ Error initializing camera IP map (Request Error): {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"❌ Error initializing camera IP map (HTTP Status {e.response.status_code}): {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected error initializing camera IP map: {e}", exc_info=True)

async def send_no_car_webhook(client: httpx.AsyncClient, serial_preset_token: str):
    """Sends a webhook notification when no car is detected."""
    timestamp = datetime.now().isoformat() # Use ISO format
    payload = {
        "hook": {"target": None, "id": None, "event": "no_car_detected"}, # Added event type
        "data": {
            "filename": None, "timestamp": timestamp, "camera_id": serial_preset_token,
            "results": [], "usage": None, "processing_time": None
        }
    }
    logger.info(f"Sending 'no car detected' webhook for {serial_preset_token} to {WEBHOOK_URL}")
    try:
        response = await client.post(WEBHOOK_URL, json=payload, timeout=10)
        response.raise_for_status()
        logger.info(f"Successfully sent no-car webhook for {serial_preset_token}")
    except httpx.RequestError as e:
        logger.error(f"❌ Failed to send no-car webhook for {serial_preset_token} (Request Error): {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"❌ Failed to send no-car webhook for {serial_preset_token} (HTTP Status {e.response.status_code}): {e}")
    except Exception as e:
        logger.error(f"❌ Exception sending no-car webhook for {serial_preset_token}: {e}", exc_info=True)

async def send_snapshot_to_plate_recognizer(client: httpx.AsyncClient, image_content: bytes, camera_id_with_preset: str):
    """Sends the captured image snapshot to the Plate Recognizer API."""
    logger.info(f"Sending snapshot for {camera_id_with_preset} to Plate Recognizer at {PLATE_RECOGNIZER_API_URL}")
    try:
        files = {"upload": ("snapshot.jpg", image_content, "image/jpeg")}
        # camera_id field is used by Plate Rec SDK/Webhook Agent
        data = {"camera_id": camera_id_with_preset}
        response = await client.post(PLATE_RECOGNIZER_API_URL, files=files, data=data, timeout=20) # Increased timeout
        response.raise_for_status()
        logger.info(f"✅ Successfully sent snapshot to Plate Recognizer API for {camera_id_with_preset}")
    except httpx.RequestError as e:
        logger.error(f"❌ Failed to send snapshot to Plate Recognizer API for {camera_id_with_preset} (Request Error): {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"❌ Failed to send snapshot to Plate Recognizer API for {camera_id_with_preset} (HTTP Status {e.response.status_code}): {e}")
        logger.debug(f"Plate Recognizer Response: {e.response.text}")
    except Exception as e:
        logger.error(f"❌ Exception sending snapshot to Plate Recognizer API for {camera_id_with_preset}: {e}", exc_info=True)

async def capture_and_process_snapshot(camera_client: httpx.AsyncClient,
                                       api_client: httpx.AsyncClient, # Client for Plate Rec/Webhook
                                       camera_ip: str, serial: str, preset_token: str):
    """Captures snapshot, runs detection, sends to Plate Recognizer or Webhook."""
    snapshot_url = f"http://{camera_ip}/axis-cgi/jpg/image.cgi?resolution=1280x720&compression=35"
    serial_preset_key = f"{serial}_{preset_token}"
    logger.info(f"Capturing snapshot from camera {serial} (IP: {camera_ip}) for preset {preset_token}...")
    try:
        response = await camera_client.get(snapshot_url, timeout=15) # Timeout for capture
        response.raise_for_status()
        image_content = response.content
        logger.info(f"✅ Successfully captured snapshot ({len(image_content)} bytes) from camera {serial}")

        # Run synchronous detection in executor thread
        loop = asyncio.get_running_loop()
        logger.debug(f"Running car detection for {serial_preset_key}...")
        start_time = time.monotonic()
        car_prob = await loop.run_in_executor(None, car_bike_empty, image_content)
        detection_duration = time.monotonic() - start_time
        logger.info(f"Car detection for {serial_preset_key} completed in {detection_duration:.3f}s. Result: {car_prob}")

        if car_prob is not None and isinstance(car_prob, float) and car_prob > CAR_DETECTION_THRESHOLD:
            logger.info(f"Car probability {car_prob:.2f} > {CAR_DETECTION_THRESHOLD}. Sending snapshot for {serial_preset_key}")
            await send_snapshot_to_plate_recognizer(api_client, image_content, serial_preset_key)
        else:
            logger.info(f"Car probability {car_prob} <= {CAR_DETECTION_THRESHOLD}. Sending no-car webhook for {serial_preset_key}")
            await send_no_car_webhook(api_client, serial_preset_key)

            # Save image for training if enabled and detection ran successfully
            if SAVE_NOCAR_IMAGES and car_prob is not None:
                try:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3] # Add milliseconds
                    os.makedirs(TRAINING_DATA_PATH, exist_ok=True)
                    file_path = os.path.join(TRAINING_DATA_PATH, f"nocar_{serial_preset_key}_{timestamp}.jpg")
                    with open(file_path, "wb") as f:
                        f.write(image_content)
                    logger.info(f"Saved no-car image for training: {file_path}")
                except Exception as save_err:
                    logger.error(f"Failed to save no-car image for {serial_preset_key}: {save_err}")
            elif not SAVE_NOCAR_IMAGES:
                 logger.debug(f"Skipping no-car image save (disabled) for {serial_preset_key}")

    except httpx.RequestError as e:
        logger.error(f"❌ Failed to capture snapshot from camera {serial} (Request Error): {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"❌ Failed to capture snapshot from camera {serial} (HTTP Status {e.response.status_code}): {e}")
    except Exception as e:
        logger.error(f"❌ Exception capturing/processing snapshot for camera {serial}: {e}", exc_info=True)

async def increment_camera_events(client: httpx.AsyncClient, serial: str):
    """Increments the event counter for the specified camera via API."""
    url = EVENTS_API_URL_TPL.format(serial=serial)
    logger.debug(f"Incrementing event count for camera {serial} at {url}")
    try:
        response = await client.post(url, timeout=5)
        response.raise_for_status()
        data = response.json()
        logger.info(f"Incremented events for camera {serial}. New count: {data.get('events')}")
    except httpx.RequestError as e:
        logger.error(f"❌ Failed to increment events for camera {serial} (Request Error): {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"❌ Failed to increment events for camera {serial} (HTTP Status {e.response.status_code}): {e}")
    except Exception as e:
        logger.error(f"❌ Error incrementing events for camera {serial}: {e}", exc_info=True)

# ────────────────────────────────────────────────────────────
# MQTT Message Processing Logic
# ────────────────────────────────────────────────────────────
async def process_mqtt_message(payload: Dict[str, Any], clients: Dict[str, httpx.AsyncClient]):
    """Processes a single decoded MQTT message."""
    global camera_preset_last_processed, camera_processing_active

    try:
        serial = payload.get("serial")
        preset_token = payload.get("message", {}).get("source", {}).get("PresetToken")
        on_preset = payload.get("message", {}).get("data", {}).get("on_preset")
        
        if not serial:
            logger.warning("Serial not found in message.")
            return
            
        if preset_token is None:
            logger.warning("PresetToken not found in message.")
            return
        
        # Handle moving camera differently - we want to discard these messages completely
        is_moving = preset_token == "-1" or on_preset == "0"
        if is_moving:
            logger.info(f"Camera {serial} is moving (preset={preset_token}, on_preset={on_preset}); discarding message")
            return  # Skip moving camera messages entirely

        # --- Locking and Debouncing ---
        camera_preset_key = f"{serial}_{preset_token}"
        current_time = time.monotonic() # Use monotonic clock for intervals

        # 1. Check if camera is already being processed (avoids concurrent runs for *same* camera)
        if camera_processing_active.get(serial, False):
            logger.info(f"Camera {serial} is already being processed. Ignoring new motion event for preset {preset_token}.")
            return

        # 2. Check preset-specific lock duration (debounce)
        last_processed_time = camera_preset_last_processed.get(camera_preset_key, 0)
        elapsed = current_time - last_processed_time
        if elapsed < PRESET_LOCK_DURATION:
            remaining = PRESET_LOCK_DURATION - elapsed
            logger.info(f"Preset lock active for {camera_preset_key}: Processed {elapsed:.1f}s ago. Enforcing {remaining:.1f}s more lock.")
            return

        # --- Passed Checks: Process the Event ---
        logger.info(f"Processing motion event for camera {serial}, preset {preset_token}.")

        # Mark camera as active *before* starting async operations
        camera_processing_active[serial] = True
        # Update last processed time *before* starting
        camera_preset_last_processed[camera_preset_key] = current_time

        try:
            # Get camera IP
            camera_ip = camera_ip_map.get(serial)
            if not camera_ip:
                logger.error(f"Cannot process {camera_preset_key}: Camera IP not found in map.")
                return # Cannot proceed

            # Increment event counter first
            await increment_camera_events(clients["api_client"], serial)

            # Capture, detect, and send snapshot/webhook
            await capture_and_process_snapshot(
                clients["camera_client"],
                clients["api_client"], # Use the general API client
                camera_ip,
                serial,
                preset_token
            )
            # Update timestamp again *after* successful processing? Or keep the start time?
            # Keeping start time enforces minimum interval *between starts*
            # camera_preset_last_processed[camera_preset_key] = time.monotonic()

        finally:
            # Always mark camera as inactive, even if errors occurred during processing
            if serial in camera_processing_active:
                 del camera_processing_active[serial]
            logger.debug(f"Processing finished for {camera_preset_key}. Camera marked inactive.")

    except Exception as e:
        logger.error(f"!!! Unexpected error in process_mqtt_message: {e}", exc_info=True)
        # Ensure camera is marked inactive if top-level error occurs
        if serial and serial in camera_processing_active:
             del camera_processing_active[serial]


# ────────────────────────────────────────────────────────────
# MQTT Listener (using asyncio-mqtt)
# ────────────────────────────────────────────────────────────
async def mqtt_listener(clients: Dict[str, httpx.AsyncClient]):
    """Listens for MQTT messages and schedules processing."""
    reconnect_delay = 5 # Seconds
    while True:
        try:
            async with MQTTClient(
                hostname=MQTT_BROKER,
                port=MQTT_PORT,
                client_id=MQTT_CLIENT_ID,
                keepalive=60
            ) as mqtt:
                logger.info(f"Attempting to connect to MQTT broker: {MQTT_BROKER}:{MQTT_PORT}")
                # No need to manually call connect, context manager handles it.
                await mqtt.subscribe(MQTT_TOPIC)
                logger.info(f"✅ Successfully connected and subscribed to MQTT topic '{MQTT_TOPIC}'")

                # Use the appropriate message iterator
                message_iterator = None
                if hasattr(mqtt, 'unfiltered_messages'): # Common pattern
                    message_iterator = mqtt.unfiltered_messages()
                else:
                    logger.critical("Cannot determine MQTT message iteration method!")
                    raise RuntimeError("MQTT message iteration method unknown")

                async with message_iterator as messages:
                    async for message in messages:
                        # Ignore retained messages
                        if message.retain:
                            logger.debug("Ignoring retained message.")
                            continue
                        try:
                            payload_str = message.payload.decode(errors='replace')
                            logger.debug(f"Raw MQTT message received: Topic='{message.topic}', Payload='{payload_str}'")
                            payload = json.loads(payload_str)
                            # Schedule processing, don't await it here
                            asyncio.create_task(process_mqtt_message(payload, clients))
                        except json.JSONDecodeError as json_exc:
                             logger.error(f"MQTT JSON decode failed: {json_exc}. Payload: '{payload_str}'")
                        except Exception as proc_exc:
                            logger.error(f"Error handling raw MQTT message: {proc_exc}. Payload: {payload_str}", exc_info=True)

        except MqttError as mqtt_err:
             logger.error(f"MQTT Error: {mqtt_err}. Reconnecting in {reconnect_delay} seconds...")
        except ConnectionRefusedError:
            logger.error(f"MQTT Connection Refused by {MQTT_BROKER}:{MQTT_PORT}. Check broker/firewall. Reconnecting in {reconnect_delay} seconds...")
        except OSError as os_err:
             logger.error(f"MQTT OS Error connecting to {MQTT_BROKER}:{MQTT_PORT}: {os_err}. Reconnecting in {reconnect_delay} seconds...")
        except Exception as e:
             logger.error(f"Unexpected error in MQTT listener scope: {e}", exc_info=True)

        logger.info(f"Waiting {reconnect_delay} seconds before attempting MQTT reconnection...")
        await asyncio.sleep(reconnect_delay)


# ────────────────────────────────────────────────────────────
# Main IoT Edge Module Function
# ────────────────────────────────────────────────────────────
async def main():
    """Main execution function: Initializes connections, clients, and starts tasks."""
    iot_client = None # Define outside try block for finally
    http_clients: Dict[str, httpx.AsyncClient] = {} # Store clients for cleanup

    try:
        # 1. Connect to IoT Edge Hub
        logger.info("Initializing IoT Edge client...")
        iot_client = IoTHubModuleClient.create_from_edge_environment()
        await iot_client.connect()
        logger.info("🔗 Connected to IoT Edge Hub")

        # 2. Initial Twin Load & Config Apply
        logger.info("Fetching initial device twin...")
        twin = await iot_client.get_twin()
        initial_config = twin.get("desired", {}).get("config", {})
        if initial_config:
             logger.info("Applying initial configuration from twin 'desired.config'...")
             apply_config(initial_config)
        else:
             logger.warning("⚠️ No 'config' section found in initial twin's desired properties. Using defaults.")
             apply_config({}) # Apply defaults

        # 3. Register Twin Patch Handler
        async def twin_patch_handler(patch: Dict[str, Any]):
            """Handles incoming twin patches."""
            logger.info(f"⚙️ Received raw twin patch content:\n{json.dumps(patch, indent=2)}")
            config_to_apply = None
            if "config" in patch and isinstance(patch.get("config"), dict):
                logger.info("Detected 'config' section in patch.")
                config_to_apply = patch["config"]
            # Check for relevant flat keys
            elif any(key in patch for key in ["mqttBrokerIp", "mqttPort", "mqttTopic", "apiServerIp",
                                             "plateRecognizerIp", "webhookIp", "cameraUsername",
                                             "cameraPassword", "presetLockDuration",
                                             "carDetectionThreshold", "saveNoCarImages"]):
                logger.info("Detected recognized config keys at top level of patch.")
                config_to_apply = patch
            else:
                logger.info("Twin patch received, but no relevant config keys found.")
                return
            if config_to_apply:
                apply_config(config_to_apply)
                # Re-initialize camera map if API server IP changed? Optional.
                if "apiServerIp" in config_to_apply and http_clients.get("api_client"):
                     logger.info("API Server IP changed, re-initializing camera map...")
                     # Ensure client exists before calling
                     await initialize_camera_ip_map(http_clients["api_client"])


        iot_client.on_twin_desired_properties_patch_received = twin_patch_handler
        logger.info("👂 Registered twin patch handler.")

        # 4. Initialize HTTP Clients
        logger.info("Initializing HTTP clients...")
        # Client for camera snapshots (Digest Auth)
        http_clients["camera_client"] = httpx.AsyncClient(
            auth=httpx.DigestAuth(CAMERA_USERNAME, CAMERA_PASSWORD),
            timeout=20.0, # Longer default timeout for camera interactions
            verify=False # Often needed for direct IP access to cameras
        )
        # General client for Plate Recognizer, Webhook, Events API
        http_clients["api_client"] = httpx.AsyncClient(timeout=15.0)
        logger.info("HTTP clients initialized.")

        # 5. Initial Camera Discovery
        await initialize_camera_ip_map(http_clients["api_client"])

        # 6. Handle Graceful Shutdown
        loop = asyncio.get_running_loop()
        stop_event = asyncio.Event()
        shutdown_signal_received = False
        def signal_handler(*args):
            nonlocal shutdown_signal_received
            if not shutdown_signal_received:
                logger.info("👋 Termination signal received. Initiating shutdown...")
                shutdown_signal_received = True
                stop_event.set()
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.add_signal_handler(sig, signal_handler, sig)

        # 7. Start Background Tasks
        logger.info("Starting MQTT listener task...")
        listener_task = asyncio.create_task(mqtt_listener(http_clients), name="MQTTListener")

        logger.info("Edge Car Detector Module running. Waiting for termination signal...")
        await stop_event.wait() # Wait here until shutdown is triggered

        # --- Shutdown Sequence ---
        logger.info("Starting graceful shutdown...")
        # Remove signal handlers
        for sig in (signal.SIGTERM, signal.SIGINT): loop.remove_signal_handler(sig)

        # Cancel MQTT listener
        logger.info("Cancelling MQTT listener task...")
        listener_task.cancel()
        try:
            await asyncio.wait_for(listener_task, timeout=5.0)
        except asyncio.CancelledError: logger.info("MQTT listener task cancelled.")
        except asyncio.TimeoutError: logger.warning("MQTT listener task did not cancel within timeout.")
        except Exception as e: logger.error(f"Error during MQTT listener task cancellation: {e}")

        # Cancel any running processing tasks? Difficult, maybe let them finish.

        # Close HTTP clients
        logger.info("Closing HTTP clients...")
        for name, client in http_clients.items():
            try:
                await client.aclose()
                logger.info(f"Closed HTTP client: {name}")
            except Exception as e:
                logger.error(f"Error closing HTTP client {name}: {e}")

        # Disconnect IoT Client
        logger.info("Disconnecting from IoT Edge Hub...")
        if iot_client:
            await iot_client.disconnect()
            logger.info("Disconnected from IoT Edge Hub.")

        logger.info("Shutdown complete.")

    except Exception as e:
        logger.critical(f"💥 Unhandled exception in main: {e}", exc_info=True)
    finally:
        # Ensure IoT client disconnects even if error happens before shutdown sequence
        if iot_client and iot_client.connected:
            logger.warning("Disconnecting IoT client due to error in main.")
            await iot_client.disconnect()
        # Ensure HTTP clients are closed
        for client in http_clients.values():
             if not client.is_closed:
                  await client.aclose()


if __name__ == "__main__":
    # Check Python version compatibility if needed
    # if not sys.version_info >= (3, 7): ...

    logger.info(f"🚀 Starting Edge Car Detector Module (Log Level: {LOG_LEVEL})...")
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logger.info("Received KeyboardInterrupt or SystemExit.")
    except Exception as e:
        logger.critical(f"💥 Unhandled top-level exception during startup/runtime: {e}", exc_info=True)
    finally:
        logger.info("🏁 Edge Car Detector Module has stopped.")
