# Base image
FROM amd64/python:3.13-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY alerts_module.py .
COPY firebase_handler.py .

# Entrypoint
CMD ["python", "-u", "alerts_module.py"]


#below is for the manifest file build options
#"createOptions": "{\"Env\":[\"AZURE_AD_CLIENT_ID=<GUID>\",\"AZURE_AD_TENANT_ID=<GUID>\",\"AZURE_AD_USERNAME=<USERNAME>\",\"AZURE_AD_PASSWORD=<SECRET>\"]}"