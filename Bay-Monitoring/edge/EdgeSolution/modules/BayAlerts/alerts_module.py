# alerts_module.py
import asyncio
import json
import logging
import os
import random
import ssl
from functools import partial
from typing import Optional, Dict, Any
import base64
from firebase_handler import initialize_firebase, upload_to_firebase_storage

import aiohttp
from azure.iot.device.aio import IoTHubModuleClient
from azure.iot.device import Message, MethodResponse
from asyncio_mqtt import Client as MQTTClient, MqttError
from msal import PublicClientApplication
import urllib3
import pytz # Added
from datetime import datetime, timedelta # Added
from collections import defaultdict # Added
from difflib import SequenceMatcher # For plate similarity

# Suppress InsecureRequestWarning for self-signed certs if needed
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# -----------------------------------------------------------------------------
# Configuration from environment variables (fallback to sensible dev defaults)
# -----------------------------------------------------------------------------
MQTT_PORT     = int(os.getenv("MQTT_PORT", 1883))
MQTT_BROKER   = os.getenv("MQTT_BROKER", "***********")
MQTT_TOPIC    = os.getenv("MQTT_TOPIC", "update") # Default from Code A, Code B uses 'update'
DASHBOARD_IP  = os.getenv("DASHBOARD_IP", "***********") # IP for Dashboard/Cloud API
API_IP        = os.getenv("API_IP", "localhost")       # IP for 'local' API updates (from Code B)
CLIENT_ID     = os.getenv("AZURE_AD_CLIENT_ID")
TENANT_ID     = os.getenv("AZURE_AD_TENANT_ID")
USERNAME      = os.getenv("AZURE_AD_USERNAME")
PASSWORD      = os.getenv("AZURE_AD_PASSWORD") # KEEP USING ENV VARS!

LOG_LEVEL     = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s  %(levelname)-8s | %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("bay‑monitor‑module")

# SSL context for potentially self-signed certs on the dashboard API
ssl_ctx = ssl.create_default_context()
ssl_ctx.check_hostname = False
ssl_ctx.verify_mode = ssl.CERT_NONE   # WARNING: Only use for trusted internal networks

# Timezone and Rate Limiting (from Code B)
sydney_tz = pytz.timezone("Australia/Sydney")
# Track last alert per cardholder and bay so the same person in the same bay
# only triggers one alert per day.
alert_registry = defaultdict(dict)  # Structure: {cardholder_upper: {bay_id: timestamp_of_last_alert}}

# -----------------------------------------------------------------------------
# Rate Limiting Helpers (from Code B)
# -----------------------------------------------------------------------------
def is_alert_allowed(cardholder: str, bay_id: str) -> bool:
    """
    Check if an alert is allowed for this **cardholder** and bay combination.
    Returns True if an alert has not been sent for this cardholder in this bay
    within the last day.
    """
    current_time = datetime.now()  # Use system time (assumed ~sync with reality)

    cardholder_key = (cardholder or "").upper()
    if bay_id not in alert_registry[cardholder_key]:
        return True  # No record, allow alert

    last_alert_time = alert_registry[cardholder_key][bay_id]
    time_since_last_alert = current_time - last_alert_time

    # Allow if last alert was more than 24 hours ago
    if time_since_last_alert > timedelta(days=1):
        return True

    logger.info(
        f"Alert for cardholder {cardholder_key} in bay {bay_id} suppressed - last alert was {time_since_last_alert} ago"
    )
    return False

def register_alert(cardholder: str, bay_id: str):
    """Register that an alert has been sent for this cardholder+bay combination."""
    cardholder_key = (cardholder or "").upper()
    alert_registry[cardholder_key][bay_id] = datetime.now()
    logger.info(f"Registered alert for cardholder {cardholder_key} in bay {bay_id}")


def calculate_plate_similarity(plate1: str, plate2: str) -> float:
    if not plate1 or not plate2:
        return 0.0
    plate1 = plate1.upper().replace(" ", "").replace("-", "")
    plate2 = plate2.upper().replace(" ", "").replace("-", "")
    if not plate1 or not plate2: # After normalization
        return 0.0

    longer_len = max(len(plate1), len(plate2))
    if longer_len == 0 : return 1.0 # both empty after normalization

    return SequenceMatcher(None, plate1, plate2).ratio()

# ------------------------------------------------------------------
# Configuration Helper (Updated)
# ------------------------------------------------------------------
def apply_config(cfg: dict):
    """
    Applies configuration settings found in the input dictionary 'cfg'
    to the corresponding global variables. Only updates settings that
    are present in 'cfg' and have valid values/types.
    """
    global MQTT_BROKER, MQTT_PORT, MQTT_TOPIC, DASHBOARD_IP, API_IP # Added API_IP
    updated_settings = []

    if "mqttBrokerIp" in cfg:
        new_val = cfg["mqttBrokerIp"]
        if isinstance(new_val, str) and new_val != MQTT_BROKER:
            MQTT_BROKER = new_val
            updated_settings.append(f"MQTT_BROKER={MQTT_BROKER}")
        elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for mqttBrokerIp: {type(new_val)}")
    if "mqttPort" in cfg:
        new_val = cfg["mqttPort"]
        try:
            new_port = int(new_val)
            if new_port != MQTT_PORT:
                MQTT_PORT = new_port
                updated_settings.append(f"MQTT_PORT={MQTT_PORT}")
        except (ValueError, TypeError): logger.warning(f"Ignoring invalid value for mqttPort: {new_val}")
    if "mqttTopic" in cfg:
         new_val = cfg["mqttTopic"]
         if isinstance(new_val, str) and new_val != MQTT_TOPIC:
            MQTT_TOPIC = new_val
            updated_settings.append(f"MQTT_TOPIC={MQTT_TOPIC}")
         elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for mqttTopic: {type(new_val)}")
    if "dashboardIp" in cfg:
         new_val = cfg["dashboardIp"]
         if isinstance(new_val, str) and new_val != DASHBOARD_IP:
            DASHBOARD_IP = new_val
            updated_settings.append(f"DASHBOARD_IP={DASHBOARD_IP}")
         elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for dashboardIp: {type(new_val)}")
    # --- Added API_IP ---
    if "apiIp" in cfg:
         new_val = cfg["apiIp"]
         if isinstance(new_val, str) and new_val != API_IP:
            API_IP = new_val
            updated_settings.append(f"API_IP={API_IP}")
         elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for apiIp: {type(new_val)}")
    # --- End Added ---

    if updated_settings:
         logger.info(f"🛠  Config updated via twin: {', '.join(updated_settings)}")
    else:
         logger.info("🛠  Received config update, but no values changed or no recognized keys found.")
    logger.info(
        f"🛠  Current effective config → broker={MQTT_BROKER}:{MQTT_PORT}, topic={MQTT_TOPIC}, dashboard={DASHBOARD_IP}, api={API_IP}" # Added API_IP
    )

# -----------------------------------------------------------------------------
# Azure AD Helpers (Keep Code A's async implementation)
# -----------------------------------------------------------------------------
async def get_access_token(loop: asyncio.AbstractEventLoop) -> Optional[str]:
    """Acquires Azure AD token using username/password flow."""
    if not all([CLIENT_ID, TENANT_ID, USERNAME, PASSWORD]):
        logger.error("❌ Missing Azure AD configuration (CLIENT_ID, TENANT_ID, USERNAME, PASSWORD)")
        return None
    def _blocking_msal_call(): # Renamed inner function for clarity
        scope = [f"api://{CLIENT_ID}/.default" if CLIENT_ID.startswith('api://') else f"{CLIENT_ID}/.default"]
        authority = f"https://login.microsoftonline.com/{TENANT_ID}"
        app = PublicClientApplication(client_id=CLIENT_ID, authority=authority)
        logger.info(f"Attempting token acquisition for user {USERNAME} via {authority}")
        result = app.acquire_token_by_username_password(username=USERNAME, password=PASSWORD, scopes=scope)
        if "error" in result:
            error_details = result.get('error_description', 'No description')
            logger.error(f"MSAL error: {result['error']} – {error_details}")
            raise RuntimeError(f"MSAL error: {result['error']} – {result['error_description']}")
        if "access_token" not in result:
             logger.error(f"MSAL response did not contain access_token. Response: {result}")
             raise RuntimeError("Failed to acquire token, 'access_token' not in result.")
        return result["access_token"]
    try:
        token = await loop.run_in_executor(None, _blocking_msal_call)
        logger.info("✅ Obtained Azure AD token")
        return token
    except Exception as exc:
        logger.error(f"❌ Failed during token acquisition process: {exc}", exc_info=True)
        return None

async def token_refresher(loop: asyncio.AbstractEventLoop, token_holder: dict, interval_minutes: int = 50):
    """Periodically refreshes the Azure AD token before it expires."""
    while True:
        await asyncio.sleep(interval_minutes * 60)
        logger.info(f"🔄 Refreshing Azure AD token (interval: {interval_minutes} mins)...")
        try:
            new_token = await get_access_token(loop)
            if new_token:
                token_holder["access_token"] = new_token
                logger.info("✅ Token refreshed successfully.")
            else:
                logger.warning("⚠️ Failed to refresh token, will retry next cycle.")
        except Exception as e:
            logger.error(f"❌ Unexpected error during token refresh: {e}", exc_info=True)

#
# Functions ofr getting teh latest bay image from API request
#
async def get_bay_info(bay_id: str) -> Optional[Dict[str, Any]]:
    """
    Calls the API to get bay information.
    This version targets the /api/bays/search endpoint and expects bay_id as a query param.
    It creates its own ClientSession.
    
    Args:
        bay_id: The bay ID to search for
        
    Returns:
        Dict containing bay information, or None if failed or not found.
    """
    url = f'http://{MQTT_BROKER}:8000/api/bays/search' # Corrected URL
    params = {'bay_id': bay_id} # Corrected params
    
    logger.info(f"Calling bay search API for bay_id: {bay_id} at {url}")
    
    try:
        async with aiohttp.ClientSession() as session: # Creates its own session
            async with session.get(url, params=params, ssl=None, timeout=10) as response: # ssl=None for HTTP
                if response.status == 200:
                    bay_data = await response.json()
                    
                    # Handle both list and direct object responses
                    if isinstance(bay_data, list):
                        if not bay_data:
                            logger.warning(f"Bay {bay_id} not found by API (empty list returned).")
                            return None
                        bay_info = bay_data[0]  # Take first item if it's a list
                    else:
                        bay_info = bay_data  # Use directly if it's already an object
                    
                    # Create a copy of the bay data for logging, with image field redacted
                    log_data = bay_info.copy() if bay_info else {}
                    if 'image' in log_data:
                        log_data['image'] = '[BASE64_IMAGE_DATA_REDACTED]'
                    
                    logger.info(f"Successfully retrieved bay information for bay_id: {bay_id}. Data: {log_data}")
                    return bay_info
                else:
                    logger.warning(f"Bay {bay_id} search API returned status code {response.status}.")
                    return None
    except aiohttp.ClientConnectionError as e:
        logger.error(f"Connection error retrieving bay info for bay_id {bay_id}: {e}")
        return None
    except asyncio.TimeoutError:
        logger.error(f"Timeout error retrieving bay info for bay_id {bay_id}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error retrieving bay info for bay_id {bay_id}: {e}", exc_info=True)
        return None

async def upload_bay_image_to_firebase(bay_info: Dict[str, Any], bay_id: str) -> Optional[str]:
    """
    Uploads the encoded image from bay_info to Firebase and returns the public URL.
    
    Args:
        bay_info: Dictionary containing bay information including 'image' field with base64 data
        bay_id: The bay ID for naming the file
        
    Returns:
        Firebase public URL of the uploaded image, or None if failed
    """
    try:
        # Get the encoded image from bay_info
        encoded_image = bay_info.get('image')
        if not encoded_image:
            logger.error("No image data found in bay_info")
            return None
        
        # Decode the base64 image
        try:
            image_bytes = base64.b64decode(encoded_image)
        except Exception as decode_error:
            logger.error(f"Failed to decode base64 image: {decode_error}")
            return None
        
        # Generate a unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        destination_blob_name = f"bay_images/{bay_id}_{timestamp}.jpg"
        
        # Initialize Firebase if not already done
        if not initialize_firebase():
            logger.error("Failed to initialize Firebase")
            return None
        
        # Upload to Firebase
        firebase_url = upload_to_firebase_storage(image_bytes, destination_blob_name)
        
        if firebase_url:
            logger.info(f"Successfully uploaded bay image to Firebase: {firebase_url}")
            return firebase_url
        else:
            logger.error("Failed to upload image to Firebase Storage")
            return None
            
    except Exception as e:
        logger.error(f"Unexpected error uploading bay image to Firebase: {e}", exc_info=True)
        return None
# -----------------------------------------------------------------------------
# API Call Functions (Converted/Merged from Code B)
# -----------------------------------------------------------------------------

async def add_event_cloud(session: aiohttp.ClientSession, data: Dict[str, Any], bay: Dict[str, Any], headers: Dict[str, str]) -> bool:
    """Asynchronously add an event to the system cloud API."""
    url = f'https://{DASHBOARD_IP}:8000/bayEvents'
    CameraName = "Bay-" + bay.get('bay_id', 'Unknown')
    payload = {
        "MessageType" : bay.get('status', 'Unknown').capitalize(),
        "Type" : "Bay Event",
        "CameraName": CameraName,
        "Serial": bay.get('serial'),
        "PtzId": bay.get('ptz_id'),
        "BayId": bay.get('bay_id'),
        "Plate": bay.get('plate'),
        "Status": bay.get('status'),
        "TimeParked": bay.get('time_parked'), 
        "TenantName": data.get('tenantName'), # Parked tenant
        "BayTenant": bay.get('tenant'), # Bay's assigned tenant
        "ParkedTenant": data.get('tenantName'), # Tenant from API lookup (or None if vacant/not found)
        "TenantID": data.get('tenantID'),
        "CardholderName": data.get('cardholderName'),
        "Timestamp": bay.get('timestamp'),
        "VehicleImage" : bay.get('image_url'),
        "Image" : bay.get('cropped_url')
    }
    # Remove None values from payload if necessary for the API
    payload_cleaned = {k: v for k, v in payload.items() if v is not None}

    logger.debug(f"Adding event to cloud: {json.dumps(payload_cleaned)}")
    try:
        async with session.post(url, headers=headers, json=payload_cleaned, ssl=ssl_ctx, timeout=10) as response:
            response_text = await response.text() # Read text for logging on error
            response.raise_for_status() # Raise ClientResponseError for 4xx/5xx
            logger.info(f"Successfully added event to the cloud system for bay {bay.get('bay_id')}")
            # Assuming success if no exception, Code B checked response.json().get('data')
            # For simplicity, return True on 2xx status. Adapt if specific data needed.
            return True
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error adding cloud event (Status {e.status}) for bay {bay.get('bay_id')}: {e.message}")
        logger.error(f"Response text: {response_text}")
        return False
    except aiohttp.ClientConnectionError as e:
        logger.error(f"Connection error adding cloud event for bay {bay.get('bay_id')}: {e}")
        return False
    except asyncio.TimeoutError:
        logger.error(f"Timeout error adding cloud event for bay {bay.get('bay_id')}")
        return False
    except Exception as e:
        logger.error(f"Unexpected exception adding cloud event for bay {bay.get('bay_id')}: {e}", exc_info=True)
        return False

async def update_bay_local(session: aiohttp.ClientSession, data: Dict[str, Any], bay: Dict[str, Any], headers: Dict[str, str]) -> bool:
    """Asynchronously update bay info on the 'local' API system."""
    # Note: Assuming API_IP uses HTTP, not HTTPS. Adjust if needed.
    if not bay.get('serial'):
         logger.error(f"Cannot update local bay {bay.get('bay_id')}: Missing 'serial' in bay data.")
         return False
    url = f'http://{API_IP}:8000/cameras/{bay["serial"]}/bays'
    
    # Include all the information that Webhook was previously adding
    params = {
        # Bay identification
        'ptz_id': bay.get('ptz_id'),
        
        # Status information
        'status': bay.get('status', 'occupied'),  # Default to 'occupied' if not specified
        'plate': bay.get('plate'),  # License plate from the bay data
        'time_parked': bay.get('time_parked'),  # Time when vehicle was detected
        
        # Tenant information from API lookup
        'parked_tenant': data.get('tenantName'),
        'tenantID': data.get('tenantID'),
        'cardholderName': data.get('cardholderName'),
        
        # Keep the assigned tenant of the bay
        'tenant': bay.get('tenant')
    }
    
    # Filter out None params
    params_cleaned = {k: v for k, v in params.items() if v is not None}

    logger.debug(f"Updating local bay {bay.get('bay_id')} with params: {params_cleaned}")
    try:
        # Using PATCH with params (query string). Pass ssl=None for HTTP.
        async with session.patch(url, params=params_cleaned, ssl=None, timeout=10) as response:
            response_text = await response.text() # Read text for logging on error
            response.raise_for_status() # Raise ClientResponseError for 4xx/5xx
            logger.info(f"Successfully updated local bay {bay['bay_id']} with complete information")
            # Assuming success if no exception. Adapt if response data needed.
            return True
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error updating local bay (Status {e.status}) {bay['bay_id']}: {e.message}")
        logger.error(f"Response text: {response_text}")
        return False
    except aiohttp.ClientConnectionError as e:
        logger.error(f"Connection error updating local bay {bay['bay_id']}: {e}")
        return False
    except asyncio.TimeoutError:
        logger.error(f"Timeout error updating local bay {bay['bay_id']}")
        return False
    except Exception as e:
        logger.error(f"Unexpected exception updating local bay {bay['bay_id']}: {e}", exc_info=True)
        return False

async def update_bay(session: aiohttp.ClientSession, data: Dict[str, Any], bay: Dict[str, Any], headers: Dict[str, str]) -> bool:
    """Update bay info on both cloud and local systems."""
    cloud_success = await add_event_cloud(session, data, bay, headers)
    local_success = await update_bay_local(session, data, bay, headers)
    # Return overall success (both must succeed, or adjust as needed)
    return cloud_success and local_success

async def add_alert(session: aiohttp.ClientSession, data: Dict[str, Any], bay: Dict[str, Any], headers: Dict[str, str]) -> bool:
    """Asynchronously add an alert to the system (using Code B's endpoint/payload)."""
    url = f'https://{DASHBOARD_IP}:8000/alertMessage' # Endpoint from Code B
    dateTime = datetime.now(pytz.utc) # Use UTC directly
    time_alert = dateTime.strftime("%Y-%m-%dT%H:%M:%S.%fZ")

    payload = { # Payload structure from Code B
        "Id": str(random.randint(10000, 99999)),
        "Type": "BayViolation",
        "CardholderName": data.get('cardholderName', 'N/A'),
        "CardholderID": data.get('cardholderID', 'N/A'),
        "TenantName": data.get('tenantName'), # Parked tenant
        "BayTenant": bay.get('tenant'), # Bay's assigned tenant
        "ParkedTenant": data.get('tenantName'), # Tenant from API lookup (or None if vacant/not found)
        "TenantID": data.get('tenantID', 'N/A'),
        "Alert": "BayViolation",
        "Plate": data.get('plate', 'N/A'),
        "Description": f"Parked in {bay.get('tenant', 'Unknown')} bay-{bay.get('bay_id', 'Unknown')}",
        "Timestamp": time_alert,
        "GallagherID": "N/A", # Consider if this can be populated
        "CameraID": "Bay", # Consider if specific camera ID is available
        "CameraName": "Bay Monitoring", # Consider if specific camera name is available
        "Image": bay.get('cropped_url'), # Use bay data passed in
        "VehicleImage" : bay.get('image_url') # Use bay data passed in
    }
    payload_cleaned = {k: v for k, v in payload.items() if v is not None}

    logger.debug(f"Adding alert: {json.dumps(payload_cleaned)}")
    try:
        async with session.post(url, headers=headers, json=payload_cleaned, ssl=ssl_ctx, timeout=10) as response:
            response_text = await response.text() # Read text for logging on error
            response.raise_for_status()
            logger.info(f"Successfully added alert to the system for plate {data.get('plate')}")
            # Assuming success if no exception. Adapt if response data needed.
            return True
    except aiohttp.ClientResponseError as e:
        logger.error(f"Error adding alert (Status {e.status}) for plate {data.get('plate')}: {e.message}")
        logger.error(f"Response text: {response_text}")
        return False
    except aiohttp.ClientConnectionError as e:
        logger.error(f"Connection error adding alert for plate {data.get('plate')}: {e}")
        return False
    except asyncio.TimeoutError:
        logger.error(f"Timeout error adding alert for plate {data.get('plate')}")
        return False
    except Exception as e:
        logger.error(f"Unexpected exception adding alert for plate {data.get('plate')}: {e}", exc_info=True)
        return False

async def check_access_control(session: aiohttp.ClientSession, plate: str, headers: Dict[str, str]) -> Optional[Dict[str, Any]]:
    """
    Asynchronously checks the license plate against the access control API.
    Returns the parsed JSON *data* dictionary on success (200 OK with valid JSON),
    otherwise returns None.
    """
    if not plate:
        logger.warning("Attempted to check access control for an empty plate.")
        return None
    # Construct URL using DASHBOARD_IP as per Code B's usage in on_message
    url = f'https://{DASHBOARD_IP}:8000/carpark/searchCarpark/{plate.upper()}'
    logger.debug(f"Checking access control for plate {plate.upper()} at {url}")

    try:
        async with session.get(url, headers=headers, ssl=ssl_ctx, timeout=10) as response:
            response_text = await response.text() # Read text for logging
            if response.status == 200:
                try:
                    full_data = json.loads(response_text)
                    # Code B expects the data nested under a 'data' key
                    api_data = full_data.get('data')
                    if api_data:
                        logger.debug(f"Access control check successful for plate {plate.upper()}. Data found.")
                        return api_data # Return the nested data dict
                    else:
                        logger.warning(f"Access control check OK (200) for plate {plate.upper()}, but 'data' key missing or empty in response: {response_text}")
                        return None
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode JSON response (Status 200) from access control API. Plate: {plate.upper()}")
                    logger.error(f"Response text: {response_text}")
                    return None
            elif response.status == 404:
                 logger.info(f"Plate {plate.upper()} not found in access control system (404).")
                 return None # Treat 404 as data not found
            else:
                # Log other non-200 errors
                logger.warning(f"Access control API returned status code {response.status} for plate {plate.upper()}.")
                logger.warning(f"Response text: {response_text}")
                return None
    except aiohttp.ClientConnectionError as e:
        logger.error(f"Connection error checking access control for plate {plate.upper()}: {e}")
        return None
    except asyncio.TimeoutError:
        logger.error(f"Timeout error checking access control for plate {plate.upper()}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error checking access control for plate {plate.upper()}: {e}", exc_info=True)
        return None

# -----------------------------------------------------------------------------
# MQTT consumer (Keep Code A's async structure)
# -----------------------------------------------------------------------------
async def mqtt_listener(token_holder: dict, iot_client: IoTHubModuleClient):
    """Listens to MQTT topic, processes messages, and handles reconnections."""
    while True:
        mqtt_client = None
        try:
            logger.info(f"Attempting to connect to MQTT broker: {MQTT_BROKER}:{MQTT_PORT}")
            async with MQTTClient(MQTT_BROKER, port=MQTT_PORT) as mqtt:
                mqtt_client = mqtt
                await mqtt_client.subscribe(MQTT_TOPIC)
                logger.info(f"✅ Successfully connected and subscribed to MQTT topic '{MQTT_TOPIC}' on {MQTT_BROKER}:{MQTT_PORT}")

                # Use the correct message iterator method (adjust if needed based on prior debugging)
                message_iterator = None
                if hasattr(mqtt_client, 'unfiltered_messages'):
                    message_iterator = mqtt_client.unfiltered_messages()
                # Add other potential methods here if needed, e.g.
                # elif hasattr(mqtt_client, 'messages'):
                #     message_iterator = mqtt_client.messages # If it's an attribute iterator
                else:
                    logger.critical("Cannot determine MQTT message iteration method for asyncio-mqtt client!")
                    raise RuntimeError("MQTT Client message iteration method unknown")

                async with aiohttp.ClientSession() as session: # Create session once
                    async with message_iterator as messages:
                         async for message in messages:
                            try:
                                payload_str = message.payload.decode(errors='replace')
                                logger.info(f"+++ RECEIVED RAW MQTT MESSAGE on topic '{message.topic}': {payload_str}")
                                payload = json.loads(payload_str)

                                # --- Call the updated processing logic ---
                                await process_bay_update(
                                    payload, session, token_holder, iot_client
                                )
                                # --- End call ---

                            except json.JSONDecodeError as json_exc:
                                 logger.error(f"!!! FAILED TO PARSE MQTT JSON: {json_exc}. Raw payload was: '{payload_str}'")
                            except KeyError as key_err: # Should be caught within process_bay_update now
                                logger.error(f"!!! UNEXPECTED KEY ERROR processing MQTT message: {key_err}. This might indicate an issue in process_bay_update logic.", exc_info=True)
                            except Exception as proc_exc: # Catch errors during processing
                                logger.error(f"!!! UNEXPECTED ERROR processing MQTT message: {proc_exc}.", exc_info=True) # Log traceback

        except MqttError as mqtt_err:
             logger.error(f"MQTT Error: {mqtt_err}. Will retry connection...")
        except ConnectionRefusedError:
            logger.error(f"MQTT Connection Refused by {MQTT_BROKER}:{MQTT_PORT}. Check broker status/firewall. Retrying...")
        except OSError as os_err: # Catches socket errors, etc.
             logger.error(f"MQTT OS Error connecting to {MQTT_BROKER}:{MQTT_PORT}: {os_err}. Retrying...")
        except AttributeError as attr_err:
             logger.critical(f"MQTT Client object is missing expected method/attribute: {attr_err}. Check asyncio-mqtt version compatibility!", exc_info=True)
        except Exception as e:
             logger.error(f"Unexpected error in MQTT listener scope: {e}", exc_info=True)
        finally:
            logger.info("Attempting MQTT cleanup/disconnect (if connected)...")

        retry_delay = 15
        logger.info(f"Disconnected from MQTT or failed to connect. Retrying connection in {retry_delay} seconds...")
        await asyncio.sleep(retry_delay)


# -----------------------------------------------------------------------------
# Core Business Logic (Adapted from Code B's on_message)
# -----------------------------------------------------------------------------
async def process_bay_update(payload: dict, session: aiohttp.ClientSession, token_holder: dict, iot_client: IoTHubModuleClient):
    """Processes a single bay update message from MQTT, incorporating Code B's logic."""
    # Validate required fields
    if 'bay_id' not in payload:
        logger.error("Payload missing required 'bay_id' field. Cannot process.")
        return
    
    # Minimal essential logging
    logger.info(f"Processing Bay Update for Bay ID: {payload.get('bay_id')} - Status: {payload.get('status')} - Plate: {payload.get('plate')}")
    
    # Ensure access token is available
    access_token = token_holder.get("access_token")
    if not access_token:
         logger.error("!!! Access token not found in token_holder. Cannot proceed with API calls.")
         return

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    bay_status = payload.get('status')
    new_plate = payload.get('plate') # Can be None. This is the plate from the new event.
    bay_id = payload['bay_id'] # Known to exist due to check above
    new_event_timestamp_str = payload.get('timestamp') # Timestamp of the new event

    try:
        if bay_status == 'occupied':
            # Fetch Current Bay Occupancy Data
            current_bay_details = await get_bay_info(bay_id)
            if current_bay_details is None:
                logger.warning(f"Could not retrieve current details for bay {bay_id}. Proceeding with new event logic only.")
                # Fall through to original processing logic for the new event if API fails

            if not new_plate:
                logger.warning(f"Bay {bay_id} is occupied but new plate is missing/null.")
                update_data = {'tenantName': None, 'tenantID': None, 'cardholderName': None} # No specific user
                await update_bay(session, update_data, payload, headers)
                return

            # Check access control for new plate
            api_data_new_event = await check_access_control(session, new_plate, headers) # Details for the *new* plate
            new_cardholder_id = api_data_new_event.get('tenant_id') if api_data_new_event else None

            # RULE IMPLEMENTATION - KEEP THESE LOGS
            if current_bay_details and current_bay_details.get("status") == 'occupied':
                existing_cardholder_id = current_bay_details.get("tenant_id")
                existing_plate = current_bay_details.get("plate")
                
                # Log the current state for debugging - KEEP THESE LOGS
                logger.info(f"RULE CHECK - Current bay state: Cardholder={existing_cardholder_id}, Plate={existing_plate}")
                logger.info(f"RULE CHECK - New event state: Cardholder={new_cardholder_id}, Plate={new_plate}")
                
                # RULE 1: If existing is unknown and new event has a cardholder, replace the event
                if (not existing_cardholder_id or existing_cardholder_id == "Unknown") and new_cardholder_id:
                    logger.info(f"RULE 1 APPLIED: Replacing unknown user with known user {new_cardholder_id} in bay {bay_id}")
                    # Continue with update (fall through)
                
                # RULE 2: If existing is cardholder and new is same cardholder, don't send new update
                elif existing_cardholder_id and new_cardholder_id and existing_cardholder_id.upper() == new_cardholder_id.upper():
                    logger.info(f"RULE 2 APPLIED: Same cardholder {new_cardholder_id} detected in bay {bay_id}. Suppressing update.")
                    return  # Suppress event
                
                # RULE 3: If existing is cardholder and new is unknown, check plate similarity
                elif existing_cardholder_id and not new_cardholder_id and existing_plate and new_plate:
                    similarity = calculate_plate_similarity(new_plate, existing_plate)
                    logger.info(f"RULE 3 CHECK: Plate similarity between existing plate ('{existing_plate}') and new plate ('{new_plate}') is {similarity*100:.2f}%.")
                    if similarity >= 0.80:
                        logger.info(f"RULE 3 APPLIED: New unknown user event has plate '{new_plate}' which is >=80% similar to existing plate '{existing_plate}'. Suppressing event.")
                        return  # Suppress event
                
                logger.info("NO RULES APPLIED: Proceeding with normal update.")
            
            # Original Logic Flow (for the new event)
            if api_data_new_event is None: # Plate not found or API error for new plate
                update_data = {'tenantName': 'Unknown', 'tenantID': None, 'cardholderName': 'Unknown'}
                await update_bay(session, update_data, payload, headers)
            else:
                # New plate was found by access control
                api_tenant_new_event = api_data_new_event.get('tenantName')
                bay_tenant_assignment = payload.get('tenant') # The tenant assigned to the physical bay
                
                await update_bay(session, api_data_new_event, payload, headers) # Update bay with new event's data

                # Violation Check (based on new event's plate and bay's assignment)
                if bay_tenant_assignment and api_tenant_new_event and bay_tenant_assignment.lower() != api_tenant_new_event.lower():
                    logger.warning(f"🚨 VIOLATION DETECTED: Plate {new_plate} (Tenant: {api_tenant_new_event}) parked in bay {bay_id} designated for tenant {bay_tenant_assignment}.")
                    if is_alert_allowed(new_cardholder_id, bay_id): # Use new_cardholder_id for rate limiting
                        alert_trigger_data = {
                             **api_data_new_event,
                             'plate': new_plate
                        }
                        alert_result = await add_alert(session, alert_trigger_data, payload, headers)
                        if alert_result:
                            register_alert(new_cardholder_id, bay_id)
                        else:
                            logger.error(f"Failed to create bay violation alert for plate {new_plate}")
                else:
                    logger.info(f"✅ No violation for new plate: Bay Tenant='{bay_tenant_assignment}', Vehicle Tenant='{api_tenant_new_event}'. Plate: {new_plate}")

        elif bay_status == 'vacant':
            logger.info(f"Bay {bay_id} is now vacant. Updating status.")
            # Data to send for a vacant event (clearing parked tenant info)
            vacant_data = {
                "tenantName": None, # Send null/None to clear
                "tenantID": None,
                "cardholderName": None
            }
            await update_bay(session, vacant_data, payload, headers)

        else:
            logger.warning(f"Received unknown bay status '{bay_status}' for bay {bay_id}. Payload: {payload}")

    except Exception as e:
        logger.error(f"!!! Unexpected error during process_bay_update for bay {payload.get('bay_id', 'N/A')}: {e}", exc_info=True)
        # Decide if partial updates should occur or just log and stop.


# -----------------------------------------------------------------------------
# IoT Edge Module Initialization and Main Loop (Keep Code A's structure)
# -----------------------------------------------------------------------------
async def main():
    """Main execution function: Initializes connections and starts tasks."""
    loop = asyncio.get_running_loop()

    # ----- Check Credentials -----
    if not all([CLIENT_ID, TENANT_ID, USERNAME, PASSWORD]):
         logger.critical("❌ FATAL: Missing required Azure AD environment variables. Module cannot function.")
         raise SystemExit("Missing Azure AD Credentials")
    else:
         logger.info("Azure AD environment variables seem present.")

    # ----- 1. Initial Azure AD Token -----
    logger.info("Attempting initial Azure AD token acquisition...")
    token_holder = {"access_token": None} # Use dict to allow updates
    initial_token = await get_access_token(loop)
    if not initial_token:
        logger.critical("❌ Cannot start module without initial Azure AD token. Check credentials and AAD app registration.")
        raise SystemExit("Failed to obtain initial Azure AD token.")
    token_holder["access_token"] = initial_token

    logger.info("Initializing Firebase...")
    if not initialize_firebase():
        logger.critical("❌ Failed to initialize Firebase. Image upload functionality will not work.")

    # ----- 2. Connect to IoT Edge Hub -----
    try:
        iot_client = IoTHubModuleClient.create_from_edge_environment()
        await iot_client.connect()
        logger.info("🔗 Connected to IoT Edge Hub")
    except Exception as e:
        logger.critical(f"❌ Failed to connect to IoT Edge Hub: {e}", exc_info=True)
        raise SystemExit("Connection to IoT Edge Hub failed.")

    # ----- 3. Initial Twin Load -----
    try:
        twin = await iot_client.get_twin()
        logger.info("⚙️ Fetched initial device twin.")
        initial_config = twin.get("desired", {}).get("config", {})
        if initial_config:
             logger.info("Applying initial configuration from twin 'desired.config'...")
             apply_config(initial_config)
        else:
             logger.warning("⚠️ No 'config' section found in initial twin's desired properties. Using defaults.")
             apply_config({}) # Apply defaults explicitly
    except Exception as e:
        logger.error(f"❌ Failed to get or process initial twin: {e}. Using default config.", exc_info=True)
        apply_config({}) # Ensure defaults are applied on error

    # ----- 4. Register Twin Patch Handler -----
    async def twin_patch_handler(patch):
        """Handles incoming twin patches, detects config structure, and calls apply_config."""
        logger.info(f"⚙️ Received raw twin patch content:\n{json.dumps(patch, indent=2)}")
        config_to_apply = None
        if "config" in patch and isinstance(patch.get("config"), dict):
            logger.info("Detected 'config' section in patch. Applying settings from within.")
            config_to_apply = patch["config"]
        elif any(key in patch for key in ["mqttBrokerIp", "mqttPort", "mqttTopic", "dashboardIp", "apiIp"]): # Added apiIp
            logger.info("Detected recognized config keys at top level of patch. Applying directly.")
            config_to_apply = patch
        else:
            logger.info("Twin patch received, but no 'config' section or recognized config keys found at top level.")
            return
        if config_to_apply is not None:
            apply_config(config_to_apply)
        else:
            logger.warning("No configuration data identified to apply in the received patch.")

    try:
        iot_client.on_twin_desired_properties_patch_received = twin_patch_handler
        logger.info("👂 Registered twin patch handler.")
    except Exception as e:
        logger.error(f"❌ Failed to register twin patch handler: {e}")

    # Define behavior for handling methods
    async def method_request_handler(method_request):
    # Determine how to respond to the method request based on the method name
        if method_request.name == "cmGetBay":
            try:
                data = method_request.payload
                logger.info(f"Received method request: {data}")
                
                # Extract bay_id from the payload
                bay_id = data.get('bay_id')
                if not bay_id:
                    payload = {
                        "status": False,
                        "Message": "bay_id is required in the payload"
                    }
                    status = 400
                else:
                    # Call the API to get bay information with image
                    # This get_bay_info is the one that fetches image for Firebase, not the one modified for suppression logic.
                    # The subtask was to correct the get_bay_info used by suppression logic,
                    # this one should remain as it was if it worked for cmGetBay.
                    # The current get_bay_info is now the one for /api/bays/search.
                    # This means cmGetBay might be broken if it relied on a different get_bay_info.
                    # However, the subtask is to correct get_bay_info for the suppression logic.
                    # Assuming the get_bay_info used here for cmGetBay should also be the one that now points to /api/bays/search
                    # and that its response (first item of list) is compatible with upload_bay_image_to_firebase.
                    bay_info_for_image = await get_bay_info(bay_id) # Uses the corrected get_bay_info
                    
                    if bay_info_for_image is None:
                        payload = {
                            "status": False,
                            "Message": f"Failed to retrieve bay information for bay_id: {bay_id}"
                        }
                        status = 404
                    else:
                        # Upload image to Firebase and get URL
                        firebase_url = await upload_bay_image_to_firebase(bay_info, bay_id)
                        
                        if firebase_url:
                            payload = {
                                "status": "success",
                                "imageUrl": firebase_url
                            }
                            status = 200
                        else:
                            payload = {
                                "status": False,
                                "Message": "Failed to upload image to Firebase"
                            }
                            status = 500

            except Exception as ex:
                logger.error(f"Unexpected error in cmGetBay: {ex}", exc_info=True)
                payload = {"status": False, "Message": str(ex)}
                status = 500
        else:
            payload = {"status": False, "Message": "Method not found"}
            status = 404
        
        # Send the response
        method_response = MethodResponse.create_from_method_request(
            method_request, status, payload)
        await iot_client.send_method_response(method_response)

    try:
        iot_client.on_method_request_received = method_request_handler
        logger.info("👂 Registered request patch handler.")
    except Exception as e:
        logger.error(f"❌ Failed to register request patch handler: {e}")
    

    # ----- 5. Start Concurrent Tasks -----
    logger.info("Starting main tasks: MQTT Listener and Token Refresher...")
    listener_task = asyncio.create_task(mqtt_listener(token_holder, iot_client), name="MQTTListener")
    refresher_task = asyncio.create_task(token_refresher(loop, token_holder), name="TokenRefresher") # Pass loop

    # Keep tasks running, handle graceful shutdown
    tasks = {listener_task, refresher_task}
    done, pending = await asyncio.wait(
        tasks,
        return_when=asyncio.FIRST_COMPLETED,
    )

    for task in done: # Log completion/errors of finished tasks
        task_name = task.get_name()
        try:
            task.result()
            logger.warning(f"Task '{task_name}' completed unexpectedly without error.")
        except asyncio.CancelledError: logger.info(f"Task '{task_name}' was cancelled.")
        except Exception as e: logger.error(f"Task '{task_name}' exited with error: {e}", exc_info=True)

    for task in pending: # Cancel pending tasks
        task_name = task.get_name()
        logger.info(f"Cancelling pending task: '{task_name}'...")
        task.cancel()
        try: await task # Allow task to handle cancellation
        except asyncio.CancelledError: logger.info(f"Task '{task_name}' cancelled successfully.")
        except Exception as e: logger.error(f"Error during cancellation/cleanup of task '{task_name}': {e}", exc_info=True)

    # ----- 6. Disconnect IoT Client -----
    logger.info("Disconnecting from IoT Edge Hub...")
    try:
        await iot_client.disconnect()
        logger.info("Disconnected from IoT Edge Hub.")
    except Exception as e:
        logger.error(f"Error during IoT client disconnection: {e}")


if __name__ == "__main__":
    logger.info(f"🚀 Starting Bay Monitor Module (Log Level: {LOG_LEVEL})...")
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit) as e:
        if isinstance(e, SystemExit): logger.warning(f"SystemExit called: {e}")
        logger.info("👋 Module shutdown requested.")
    except Exception as e:
        logger.critical(f"💥 Unhandled top-level exception during module execution: {e}", exc_info=True)
    finally:
        logger.info("🏁 Bay Monitor Module has stopped.")
