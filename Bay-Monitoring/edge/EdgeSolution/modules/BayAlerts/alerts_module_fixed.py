# alerts_module.py
import asyncio
import json
import logging
import os
import random
import ssl
from functools import partial
from typing import Optional

import aiohttp
from azure.iot.device.aio import IoTHubModuleClient
from asyncio_mqtt import Client as MQTTClient, MqttError
from msal import PublicClientApplication
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# -----------------------------------------------------------------------------
# Configuration from environment variables (fallback to sensible dev defaults)
# -----------------------------------------------------------------------------
MQTT_PORT     = int(os.getenv("MQTT_PORT", 1883))
MQTT_BROKER   = os.getenv("MQTT_BROKER", "localhost")
MQTT_TOPIC    = os.getenv("MQTT_TOPIC", "bay/updates")
DASHBOARD_IP  = os.getenv("DASHBOARD_IP", "localhost")
CLIENT_ID     = os.getenv("AZURE_AD_CLIENT_ID")
TENANT_ID     = os.getenv("AZURE_AD_TENANT_ID")
USERNAME      = os.getenv("AZURE_AD_USERNAME")        # ⚠ never store creds in code
PASSWORD      = os.getenv("AZURE_AD_PASSWORD")

LOG_LEVEL     = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s  %(levelname)-8s | %(name)s | %(message)s",
)
logger = logging.getLogger("bay‑monitor‑module")

ssl_ctx = ssl.create_default_context()
ssl_ctx.check_hostname = False
ssl_ctx.verify_mode = ssl.CERT_NONE   # internal self‑signed lab certs

# ------------------------------------------------------------------
# helper – apply settings to the in‑memory globals
# ------------------------------------------------------------------
def apply_config(cfg: dict):
    global MQTT_BROKER, MQTT_TOPIC, DASHBOARD_IP
    MQTT_BROKER  = cfg.get("mqttBrokerIp",  MQTT_BROKER)
    MQTT_TOPIC   = cfg.get("mqttTopic",     MQTT_TOPIC)
    DASHBOARD_IP = cfg.get("dashboardIp",   DASHBOARD_IP)
    logger.info(
        f"🛠  Config set → broker={MQTT_BROKER}, topic={MQTT_TOPIC}, dashboard={DASHBOARD_IP}"
    )

# -----------------------------------------------------------------------------
# Azure AD helpers (token acquisition is blocking – wrap it in executor)
# -----------------------------------------------------------------------------
async def get_access_token(loop: asyncio.AbstractEventLoop) -> Optional[str]:
    def _blocking():
        scope = [f"{CLIENT_ID}/.default"]
        app = PublicClientApplication(
            client_id=CLIENT_ID,
            authority=f"https://login.microsoftonline.com/{TENANT_ID}",
        )
        result = app.acquire_token_by_username_password(
            USERNAME, PASSWORD, scopes=scope
        )
        if "error" in result:
            raise RuntimeError(
                f"MSAL error: {result['error']} – {result['error_description']}"
            )
        return result["access_token"]

    try:
        token = await loop.run_in_executor(None, _blocking)
        logger.info("✅ Obtained Azure AD token")
        return token
    except Exception as exc:
        logger.error(f"❌ Failed to get token: {exc}")
        return None


async def token_refresher(loop, token_holder: dict, interval_minutes: int = 50):
    """Refresh the bearer token periodically."""
    while True:
        await asyncio.sleep(interval_minutes * 60)
        logger.info("🔄 Refreshing Azure AD token …")
        token = await get_access_token(loop)
        if token:
            token_holder["access_token"] = token


# -----------------------------------------------------------------------------
# REST calls
# -----------------------------------------------------------------------------
async def add_alert(session, data, bay, token):
    url = f"https://{DASHBOARD_IP}:8000/alerts/addAlert/"
    hdrs = {"Authorization": f"Bearer {token}", "Content‑Type": "application/json"}

    payload = {
        "id": str(random.randint(10, 9999)),
        "cardholderName": data["cardholderName"],
        "cardholderID": data["cardholderID"],
        "tenantName": data["tenantName"],
        "tenantID": data["tenantID"],
        "alertType": "Bay violation",
        "licensePlate": data["plate"],
        "description": f"Parked in {bay['tenant']} bay‑{bay['bay_id']}",
        "timestamp": bay["time_parked"],
        "acknowledged": False,
        "repeatOffender": False,
        "gallagherID": "N/A",
        "cameraID": "9",
        "cameraName": "Bay Monitoring",
        "imageURL": "dummy_image_url",
    }

    async with session.post(url, headers=hdrs, json=payload, ssl=ssl_ctx) as resp:
        if resp.status < 300:
            logger.info("✅ Alert added")
            return await resp.json()
        logger.error(f"❌ addAlert failed – status {resp.status}: {await resp.text()}")
        return None


async def check_access_control(session, plate: str, token: str):
    url = f"https://{DASHBOARD_IP}:8000/carpark/searchCarpark/{plate}"
    hdrs = {"Authorization": f"Bearer {token}"}
    async with session.get(url, headers=hdrs, ssl=ssl_ctx) as resp:
        return resp.status, await resp.json()


# -----------------------------------------------------------------------------
# MQTT consumer
# -----------------------------------------------------------------------------
async def mqtt_listener(token_holder: dict, iot_client: IoTHubModuleClient):
    # Reuse TLS‑less connection to on‑prem broker (adjust if broker uses TLS)
    async with MQTTClient(MQTT_BROKER, port=MQTT_PORT) as mqtt:
        await mqtt.subscribe(MQTT_TOPIC)
        logger.info(f"📡 Subscribed to MQTT topic '{MQTT_TOPIC}'")

        async with mqtt.messages() as messages:
            async with aiohttp.ClientSession() as session:
                async for message in messages:
                    try:
                        payload = json.loads(message.payload.decode())
                        await process_bay_update(
                            payload, session, token_holder, iot_client
                        )
                    except Exception as exc:  # broad‑catch so one bad msg doesn't kill us
                        logger.error(f"MQTT message processing failed: {exc}")


# -----------------------------------------------------------------------------
# Core business logic
# -----------------------------------------------------------------------------
async def process_bay_update(
    payload: dict, session: aiohttp.ClientSession, token_holder: dict, iot_client
):
    logger.info(f"➡ Bay update: {json.dumps(payload, indent=0)}")

    if payload.get("status") != "occupied":
        logger.info("Bay vacant – skipping access‑control check")
        return

    plate = payload["plate"].upper()
    status, result = await check_access_control(
        session, plate, token_holder["access_token"]
    )

    if status != 200 or not result.get("data"):
        logger.info(f"Plate {plate} is unassigned or API returned {status}")
        return

    api_tenant = result["data"]["tenantName"]
    bay_tenant = payload.get("tenant")
    logger.info(f"Plate {plate} → tenant '{api_tenant}' (bay tenant '{bay_tenant}')")

    if bay_tenant and api_tenant and bay_tenant.lower() != api_tenant.lower():
        logger.warning("🚨 Wrong tenant parked – raising alert")
        bay_data = {
            "tenant": bay_tenant,
            "bay_id": payload["bay_id"],
            "time_parked": payload["time_parked"],
        }
        await add_alert(session, result["data"], bay_data, token_holder["access_token"])

        # Fan‑out to downstream IoT Edge graph (e.g. to dashboard module)
        msg = json.dumps(
            {
                "event": "bay_violation",
                "payload": {**payload, "correctTenant": api_tenant},
            }
        )
        await iot_client.send_message_to_output(msg, "output1")
        logger.info("📤 Violation pushed to Edge output 'output1'")


# -----------------------------------------------------------------------------
# Entry‑point
# -----------------------------------------------------------------------------
async def main():
    loop = asyncio.get_running_loop()
    token = await get_access_token(loop)
    if not token:
        raise SystemExit("Cannot start module without Azure AD token")

    token_holder = {"access_token": token}

    # Connect to Edge Hub
    iot_client = IoTHubModuleClient.create_from_edge_environment()
    await iot_client.connect()
    logger.info("🔗 Connected to Edge Hub")

    # ----- 2. INITIAL twin load -----
    twin = await iot_client.get_twin()                  # <-- first fetch
    apply_config(twin["desired"].get("config", {}))     # <-- populate globals

    # ----- 3. register live‑patch handler -----
    async def twin_patch_handler(patch):
        if "config" in patch:
            apply_config(patch["config"])

    iot_client.on_twin_desired_properties_patch_received = twin_patch_handler

    # Run MQTT listener + token refresh concurrently; exit gracefully on SIGINT/SIGTERM
    await asyncio.gather(
        mqtt_listener(token_holder, iot_client),
        token_refresher(loop, token_holder),
    )


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logger.info("👋 Module stopped")
