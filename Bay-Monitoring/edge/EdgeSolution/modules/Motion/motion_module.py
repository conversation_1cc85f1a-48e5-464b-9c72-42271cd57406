# motion_module.py
import asyncio
import json
import logging
import os
import signal
import ssl
import time
from typing import Dict, Any, Optional # Added Any, Optional

import aiohttp
from azure.iot.device.aio import IoTHubModuleClient
from asyncio_mqtt import Client as MQTTClient, MqttError
import httpx # Keep httpx for the guard tour logic

# ────────────────────────────────────────────────────────────
# Config – overridable by twin afterwards
# ────────────────────────────────────────────────────────────
MQTT_BROKER  = os.getenv("MQTT_BROKER", "***********")
MQTT_PORT    = int(os.getenv("MQTT_PORT", 1883))
MQTT_TOPIC   = os.getenv("MQTT_TOPIC", "motion")

CAMERA_API_IP = os.getenv("CAMERA_API_IP", "***********")   # twin can overwrite
API_URL       = f"http://{CAMERA_API_IP}:8000/cameras/" # Base URL for camera discovery

# Constants for motion processing
FILTER_PREFIX    = "axis:CameraApplicationPlatform/VMD/Camera" # Topic filter prefix
TIME_THRESHOLD_MS = 12_000 # Time threshold for motion clustering
MOTION_COUNT_THRESHOLD = 3 # Count threshold to trigger guard tour

# Camera credentials (Consider using secrets management)
CAMERA_USERNAME  = os.getenv("CAMERA_USERNAME", "root")
CAMERA_PASSWORD  = os.getenv("CAMERA_PASSWORD", "M1ecrdry1!")

CLIENT_ID = "motion_listener" # MQTT Client ID

logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s  %(levelname)-8s | %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S" # Added date format
)
logger = logging.getLogger("motion-module")

# SSL context (if needed for CAMERA_API_IP, though URL is HTTP)
ssl_ctx = ssl.create_default_context()
ssl_ctx.check_hostname = False
ssl_ctx.verify_mode = ssl.CERT_NONE

# State variables
camera_info: Dict[str, dict] = {}   # serial → {ip, username, password}
# Motion cluster state: serial -> {last_time_ms, count}
motion_clusters: Dict[str, Dict[str, int]] = {}


# ────────────────────────────────────────────────────────────
# Helpers to apply twin config (Robust Version)
# ────────────────────────────────────────────────────────────
def apply_config(cfg: dict):
    """
    Applies configuration settings found in the input dictionary 'cfg'
    to the corresponding global variables. Only updates settings that
    are present in 'cfg' and have valid values/types.
    """
    global MQTT_BROKER, MQTT_PORT, MQTT_TOPIC, CAMERA_API_IP, API_URL # Added API_URL update
    updated_settings = []

    if "mqttBrokerIp" in cfg:
        new_val = cfg["mqttBrokerIp"]
        if isinstance(new_val, str) and new_val != MQTT_BROKER:
            MQTT_BROKER = new_val
            updated_settings.append(f"MQTT_BROKER={MQTT_BROKER}")
        elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for mqttBrokerIp: {type(new_val)}")
    if "mqttPort" in cfg: # Added port update
        new_val = cfg["mqttPort"]
        try:
            new_port = int(new_val)
            if new_port != MQTT_PORT:
                MQTT_PORT = new_port
                updated_settings.append(f"MQTT_PORT={MQTT_PORT}")
        except (ValueError, TypeError): logger.warning(f"Ignoring invalid value for mqttPort: {new_val}")
    if "mqttTopic" in cfg:
         new_val = cfg["mqttTopic"]
         if isinstance(new_val, str) and new_val != MQTT_TOPIC:
            MQTT_TOPIC = new_val
            updated_settings.append(f"MQTT_TOPIC={MQTT_TOPIC}")
         elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for mqttTopic: {type(new_val)}")
    # --- Camera API Specific ---
    if "cameraApiIp" in cfg:
         new_val = cfg["cameraApiIp"]
         if isinstance(new_val, str) and new_val != CAMERA_API_IP:
            CAMERA_API_IP = new_val
            API_URL = f"http://{CAMERA_API_IP}:8000/cameras/" # Update full URL
            updated_settings.append(f"CAMERA_API_IP={CAMERA_API_IP}")
         elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for cameraApiIp: {type(new_val)}")
    # --- End Camera API Specific ---

    if updated_settings:
         logger.info(f"🛠  Config updated via twin: {', '.join(updated_settings)}")
    else:
         logger.info("🛠  Received config update, but no values changed or no recognized keys found.")
    # Log effective config
    logger.info(
        f"🛠  Current effective config → broker={MQTT_BROKER}:{MQTT_PORT}, topic={MQTT_TOPIC}, cameraAPI={API_URL}"
    )


# ────────────────────────────────────────────────────────────
# Camera discovery (Using aiohttp)
# ────────────────────────────────────────────────────────────
async def fetch_camera_info():
    """Fetches camera details from the configured API_URL."""
    global camera_info
    logger.info(f"Attempting camera discovery from {API_URL}...")
    try:
        # Assuming API uses HTTP, pass ssl=None. If API uses HTTPS with self-signed cert, use ssl=ssl_ctx
        ssl_param = ssl_ctx if API_URL.startswith("https") else None
        async with aiohttp.ClientSession() as session:
            async with session.get(API_URL, ssl=ssl_param, timeout=15) as resp: # Increased timeout
                resp.raise_for_status() # Raise error for non-2xx status
                cameras_data = await resp.json()
                logger.debug(f"Raw camera data from API: {cameras_data}")

        # Process camera data - ensure keys exist before accessing
        valid_cameras = {}
        for cam in cameras_data:
            serial = cam.get("serial")
            ip = cam.get("camera_ip")
            if serial and ip:
                valid_cameras[serial] = {
                    "ip": ip,
                    "username": CAMERA_USERNAME, # Use global/env var credentials
                    "password": CAMERA_PASSWORD,
                }
            else:
                logger.warning(f"Skipping camera entry due to missing serial or IP: {cam}")

        camera_info = valid_cameras
        logger.info(f"✅ Discovered and loaded info for {len(camera_info)} cameras from API.")

    except aiohttp.ClientConnectionError as e:
         logger.error(f"❌ Camera discovery failed (Connection Error): {e}")
         camera_info = {} # Clear cache on error
    except asyncio.TimeoutError:
         logger.error(f"❌ Camera discovery failed (Timeout Error)")
         camera_info = {}
    except aiohttp.ClientResponseError as e:
         logger.error(f"❌ Camera discovery failed (HTTP Status {e.status}): {e.message}")
         camera_info = {}
    except Exception as exc:
        logger.error(f"❌ Camera discovery failed (Unexpected Error): {exc}", exc_info=True)
        camera_info = {} # Clear cache on any error


# ────────────────────────────────────────────────────────────
# Guard‑tour logic (Using httpx for Digest Auth)
# ────────────────────────────────────────────────────────────
async def trigger_guard_tours(serial: str, iot_client: IoTHubModuleClient):
    """Triggers the guard tour sequence on a specific camera."""
    cam = camera_info.get(serial)
    if not cam:
        logger.error(f"Cannot trigger guard tour: No camera info found for serial {serial}")
        return

    ip = cam.get("ip")
    username = cam.get("username")
    password = cam.get("password")

    if not all([ip, username, password]):
         logger.error(f"Cannot trigger guard tour for {serial}: Missing IP or credentials.")
         return

    logger.info(f"[{serial}] Attempting to trigger guard tours on camera {ip}...")
    auth = httpx.DigestAuth(username, password)

    # Increased timeout for camera interactions
    async with httpx.AsyncClient(auth=auth, timeout=20.0, verify=False) as client: # Disable SSL verify for camera IPs
        # Optional: Pre-authentication handshake (often helpful for Digest)
        # try:
        #     pre_auth_url = f"http://{ip}/axis-cgi/param.cgi?action=list&group=Network"
        #     await client.get(pre_auth_url)
        #     logger.debug(f"[{serial}] Pre-authentication handshake successful.")
        # except Exception as pre_auth_err:
        #     logger.warning(f"[{serial}] Pre-authentication failed (may proceed anyway): {pre_auth_err}")
        #     # Don't necessarily stop here, some cameras might work without it

        group = 0
        max_groups_to_try = 10 # Prevent infinite loop if camera behaves unexpectedly
        tours_started_count = 0
        while group < max_groups_to_try:
            group_param = f"GuardTour.G{group}"
            start_url = f"http://{ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=yes"
            stop_url = f"http://{ip}/axis-cgi/param.cgi?action=update&{group_param}.Running=no"

            try:
                logger.debug(f"[{serial}] Attempting to start {group_param}...")
                response = await client.get(start_url)
                response_text = response.text

                # Check common Axis error responses
                if response.status_code != 200 or "Error" in response_text or "failed" in response_text.lower():
                     # Log specific error if possible, otherwise assume group doesn't exist
                     if response.status_code != 404: # Don't log error for non-existent groups
                         logger.warning(f"[{serial}] Failed to start {group_param} (Status: {response.status_code}, Response: '{response_text.strip()}'). Assuming end of tours.")
                     else:
                          logger.debug(f"[{serial}] Guard tour group G{group} not found (404). Assuming end of tours.")
                     break # Stop trying further groups

                logger.info(f"[{serial}] Started {group_param} successfully.")
                tours_started_count += 1

                # Let it run for the configured duration (e.g., 2 minutes)
                run_duration = 120
                logger.info(f"[{serial}] Letting {group_param} run for {run_duration} seconds...")
                await asyncio.sleep(run_duration)

                # Stop the tour
                logger.debug(f"[{serial}] Attempting to stop {group_param}...")
                stop_response = await client.get(stop_url)
                stop_response_text = stop_response.text
                if stop_response.status_code != 200 or "Error" in stop_response_text:
                    logger.error(f"[{serial}] Failed to stop {group_param} (Status: {stop_response.status_code}, Response: '{stop_response_text.strip()}'). Manual intervention might be needed.")
                    # Decide whether to continue to next group or stop
                else:
                    logger.info(f"[{serial}] Stopped {group_param} successfully.")

            except httpx.RequestError as req_err:
                logger.error(f"[{serial}] Network error during guard tour for G{group}: {req_err}")
                break # Stop sequence on network error
            except httpx.HTTPStatusError as status_err:
                logger.error(f"[{serial}] HTTP status error during guard tour for G{group}: {status_err}")
                break # Stop sequence on HTTP error
            except Exception as exc:
                logger.error(f"[{serial}] Unexpected error during guard tour for G{group}: {exc}", exc_info=True)
                break # Stop sequence on unexpected error

            group += 1 # Move to the next group

        logger.info(f"[{serial}] Guard tour sequence finished. Started {tours_started_count} tours up to group G{group-1}.")

    # Notify downstream module via IoT Edge output route
    try:
        await iot_client.send_message_to_output(
            json.dumps({"event": "guardTourTriggered", "serial": serial, "toursStarted": tours_started_count}),
            "output1",
        )
        logger.info(f"[{serial}] Sent guardTourTriggered event to output1.")
    except Exception as send_exc:
        logger.error(f"[{serial}] Failed to send guardTourTriggered event to output1: {send_exc}")


# ────────────────────────────────────────────────────────────
# Handle each motion event (Motion Clustering Logic)
# ────────────────────────────────────────────────────────────
async def process_motion(payload: Dict[str, Any], iot_client: IoTHubModuleClient):
    """Processes a single motion event message from MQTT."""
    # logger.debug(f"Processing motion payload: {payload}") # Debug log if needed

    topic_field = payload.get("topic", "")
    if not topic_field.startswith(FILTER_PREFIX):
        # logger.debug(f"Ignoring message with topic '{topic_field}' (does not match prefix '{FILTER_PREFIX}')")
        return # Ignore messages not matching the VMD topic prefix

    serial = payload.get("serial")
    # Timestamp expected in milliseconds from Axis VMD events
    ts_ms_str = payload.get("timestamp")

    if serial is None or ts_ms_str is None:
        logger.warning(f"Ignoring motion event: Missing 'serial' or 'timestamp'. Payload: {payload}")
        return

    try:
        # Convert timestamp string (potentially with fractions) to integer milliseconds
        ts_ms = int(float(ts_ms_str))
    except (ValueError, TypeError):
         logger.warning(f"Ignoring motion event: Invalid timestamp format '{ts_ms_str}'. Payload: {payload}")
         return

    # Current time in milliseconds for comparison
    now_ms = ts_ms

    # Get or initialize cluster state for this camera serial
    cluster = motion_clusters.setdefault(serial, {"last_time_ms": now_ms, "count": 0})

    # Check time difference since last event for this camera
    if now_ms - cluster["last_time_ms"] > TIME_THRESHOLD_MS:
        # If time difference is large, reset the count (start new cluster)
        logger.debug(f"[{serial}] New motion cluster detected (time diff > {TIME_THRESHOLD_MS}ms).")
        cluster["count"] = 1
    else:
        # If within time threshold, increment the count
        cluster["count"] += 1

    # Update the last event time for this camera
    cluster["last_time_ms"] = now_ms
    logger.info(f"[{serial}] Motion event received. Cluster count → {cluster['count']}")

    # Check if the count exceeds the threshold
    if cluster["count"] > MOTION_COUNT_THRESHOLD:
        logger.warning(f"[{serial}] Motion count threshold ({MOTION_COUNT_THRESHOLD}) exceeded. Triggering guard tours...")
        # Reset count immediately to prevent rapid re-triggering
        cluster["count"] = 0
        # Reset last_time as well? Maybe not, let the threshold naturally apply again
        # cluster["last_time_ms"] = 0
        # Trigger guard tours asynchronously
        asyncio.create_task(trigger_guard_tours(serial, iot_client))


# ────────────────────────────────────────────────────────────
# MQTT listener (Using robust async approach)
# ────────────────────────────────────────────────────────────
async def mqtt_listener(iot_client: IoTHubModuleClient):
    """Listens to MQTT topic, processes messages, and handles reconnections."""
    while True:
        mqtt_client = None
        try:
            logger.info(f"Attempting to connect to MQTT broker: {MQTT_BROKER}:{MQTT_PORT}")
            async with MQTTClient(
                hostname=MQTT_BROKER,
                port=MQTT_PORT,
                client_id=CLIENT_ID,
                # Add keepalive if needed: keepalive=60
            ) as mqtt:
                mqtt_client = mqtt
                await mqtt_client.subscribe(MQTT_TOPIC)
                logger.info(f"✅ Successfully connected and subscribed to MQTT topic '{MQTT_TOPIC}' on {MQTT_BROKER}:{MQTT_PORT}")

                # --- Use the working message iteration pattern ---
                message_iterator = None
                if hasattr(mqtt_client, 'unfiltered_messages'):
                    message_iterator = mqtt_client.unfiltered_messages()
                # Add elif checks for other patterns if needed based on your specific asyncio-mqtt version
                # elif hasattr(mqtt_client, 'messages'): # Example for attribute iterator
                #    message_iterator = mqtt_client.messages
                else:
                    logger.critical("Cannot determine MQTT message iteration method for asyncio-mqtt client!")
                    raise RuntimeError("MQTT Client message iteration method unknown")
                # --- End message iteration pattern ---

                async with message_iterator as messages:
                    async for msg in messages:
                        try:
                            payload_str = msg.payload.decode(errors='replace')
                            # logger.debug(f"Raw MQTT message: Topic='{msg.topic}', Payload='{payload_str}'") # Verbose
                            payload = json.loads(payload_str)

                            # Schedule motion processing as a separate task
                            asyncio.create_task(process_motion(payload, iot_client))

                        except json.JSONDecodeError as json_exc:
                             logger.error(f"MQTT JSON decode failed: {json_exc}. Payload: '{payload_str}'")
                        except Exception as proc_exc: # Catch errors during task creation or initial processing
                            logger.error(f"Error handling MQTT message: {proc_exc}. Payload: {payload_str}", exc_info=True)

        except MqttError as mqtt_err:
             logger.error(f"MQTT Error: {mqtt_err}. Will retry connection...")
        except ConnectionRefusedError:
            logger.error(f"MQTT Connection Refused by {MQTT_BROKER}:{MQTT_PORT}. Check broker status/firewall. Retrying...")
        except OSError as os_err:
             logger.error(f"MQTT OS Error connecting to {MQTT_BROKER}:{MQTT_PORT}: {os_err}. Retrying...")
        except AttributeError as attr_err: # Catch errors related to message iteration method
             logger.critical(f"MQTT Client object is missing expected method/attribute for message iteration: {attr_err}. Check asyncio-mqtt version!", exc_info=True)
        except Exception as e:
             logger.error(f"Unexpected error in MQTT listener scope: {e}", exc_info=True)
        finally:
            logger.info("Attempting MQTT cleanup/disconnect (if connected)...")

        retry_delay = 5 # Shortened retry delay from 15s
        logger.info(f"Disconnected from MQTT or failed to connect. Retrying connection in {retry_delay} seconds...")
        await asyncio.sleep(retry_delay)


# ────────────────────────────────────────────────────────────
# Main entry point (IoT Edge Module Structure)
# ────────────────────────────────────────────────────────────
async def main():
    """Main execution function: Initializes connections and starts tasks."""
    # 1. Connect to IoT Edge Hub
    try:
        iot_client = IoTHubModuleClient.create_from_edge_environment()
        await iot_client.connect()
        logger.info("🔗 Connected to IoT Edge Hub")
    except Exception as e:
        logger.critical(f"❌ Failed to connect to IoT Edge Hub: {e}", exc_info=True)
        raise SystemExit("Connection to IoT Edge Hub failed.")

    # 2. Initial Twin Load & Config Apply
    try:
        twin = await iot_client.get_twin()
        logger.info("⚙️ Fetched initial device twin.")
        initial_config = twin.get("desired", {}).get("config", {})
        if initial_config:
             logger.info("Applying initial configuration from twin 'desired.config'...")
             apply_config(initial_config)
        else:
             logger.warning("⚠️ No 'config' section found in initial twin's desired properties. Using defaults.")
             apply_config({}) # Apply defaults explicitly
    except Exception as e:
        logger.error(f"❌ Failed to get or process initial twin: {e}. Using default config.", exc_info=True)
        apply_config({}) # Ensure defaults are applied on error

    # 3. Register Twin Patch Handler (Robust Version)
    async def twin_patch_handler(patch: Dict[str, Any]):
        """Handles incoming twin patches, detects config structure, and calls apply_config."""
        logger.info(f"⚙️ Received raw twin patch content:\n{json.dumps(patch, indent=2)}")
        config_to_apply = None
        # Check for nested "config" structure
        if "config" in patch and isinstance(patch.get("config"), dict):
            logger.info("Detected 'config' section in patch. Applying settings from within.")
            config_to_apply = patch["config"]
        # Check for recognized flat keys
        elif any(key in patch for key in ["mqttBrokerIp", "mqttPort", "mqttTopic", "cameraApiIp"]):
            logger.info("Detected recognized config keys at top level of patch. Applying directly.")
            config_to_apply = patch
        else:
            logger.info("Twin patch received, but no 'config' section or recognized config keys found at top level.")
            return # Nothing to apply
        if config_to_apply is not None:
            apply_config(config_to_apply)
        else:
            logger.warning("No configuration data identified to apply in the received patch.")

    try:
        iot_client.on_twin_desired_properties_patch_received = twin_patch_handler
        logger.info("👂 Registered twin patch handler.")
    except Exception as e:
        logger.error(f"❌ Failed to register twin patch handler: {e}")

    # 4. Initial Camera Discovery
    await fetch_camera_info()
    # Optional: Schedule periodic rediscovery?
    # asyncio.create_task(periodic_camera_discovery(interval_hours=1))

    # 5. Handle SIGTERM/SIGINT for graceful shutdown
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()
    shutdown_signal_received = False # Flag to prevent double handling

    def signal_handler(*args):
        nonlocal shutdown_signal_received
        if not shutdown_signal_received:
            logger.info("👋 Termination signal received. Initiating shutdown...")
            shutdown_signal_received = True
            stop_event.set() # Trigger the shutdown sequence
        else:
            logger.info("Termination signal received again, already shutting down.")

    for sig in (signal.SIGTERM, signal.SIGINT):
        try:
            loop.add_signal_handler(sig, signal_handler)
        except NotImplementedError: # For Windows compatibility
             signal.signal(sig, signal_handler)

    # 6. Run Listener Task until Shutdown Signal
    logger.info("Starting MQTT listener task...")
    listener_task = asyncio.create_task(mqtt_listener(iot_client), name="MQTTListener")

    logger.info("Motion module running. Waiting for termination signal...")
    await stop_event.wait() # Wait until signal_handler sets the event

    # --- Shutdown Sequence ---
    logger.info("Starting graceful shutdown...")

    # Cancel the main listener task
    logger.info("Cancelling MQTT listener task...")
    listener_task.cancel()
    try:
        await asyncio.wait_for(listener_task, timeout=5.0) # Wait briefly for cancellation
    except asyncio.CancelledError:
        logger.info("MQTT listener task cancelled successfully.")
    except asyncio.TimeoutError:
        logger.warning("MQTT listener task did not cancel within timeout.")
    except Exception as e:
        logger.error(f"Error during MQTT listener task cancellation: {e}", exc_info=True)

    # Cancel any running guard tour tasks? This is complex, maybe let them finish or timeout.

    # Disconnect IoT Client
    logger.info("Disconnecting from IoT Edge Hub...")
    try:
        await iot_client.disconnect()
        logger.info("Disconnected from IoT Edge Hub.")
    except Exception as e:
        logger.error(f"Error during IoT client disconnection: {e}", exc_info=True)

    logger.info("Shutdown complete.")

if __name__ == "__main__":
    logger.info(f"🚀 Starting Motion Monitor Module...")
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit) as e:
         # SystemExit is now handled within main() via stop_event
         # KeyboardInterrupt triggers the signal handler
         logger.info("Received KeyboardInterrupt or SystemExit outside main loop.")
    except Exception as e:
        logger.critical(f"💥 Unhandled top-level exception: {e}", exc_info=True)
    finally:
        logger.info("🏁 Motion Monitor Module has stopped.")