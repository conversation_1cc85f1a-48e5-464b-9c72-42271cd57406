import asyncio
import unittest
from unittest.mock import MagicMock, patch, call
from datetime import datetime

# Assuming webhook_module is in the same directory or accessible via PYTHONPATH
import webhook_module 
from webhook_module import process_webhook_data, calculate_distance_from_edge

class TestWebhookLogic(unittest.TestCase):

    def test_calculate_distance_from_edge(self):
        # Test cases for calculate_distance_from_edge
        # (width=1280, height=720)
        # Box perfectly in center
        box_center = {"xmin": 500, "ymin": 300, "xmax": 700, "ymax": 400} # distances: 500, 580, 300, 320. min: 300
        self.assertEqual(calculate_distance_from_edge(box_center, 1280, 720), 300)

        # Box touching left edge
        box_left_edge = {"xmin": 0, "ymin": 100, "xmax": 100, "ymax": 200} # min: 0
        self.assertEqual(calculate_distance_from_edge(box_left_edge, 1280, 720), 0)

        # Box touching top-right corner
        box_top_right_corner = {"xmin": 1180, "ymin": 0, "xmax": 1280, "ymax": 100} # min: 0 (right edge) or 0 (top edge)
        self.assertEqual(calculate_distance_from_edge(box_top_right_corner, 1280, 720), 0)
        
        # Box from example: "CLOSE1"
        box_close1 = {"xmin": 10, "ymin": 10, "xmax": 100, "ymax": 100} # d_left=10, d_right=1180, d_top=10, d_bottom=620. min: 10
        self.assertEqual(calculate_distance_from_edge(box_close1, 1280, 720), 10)

        # Box from example: "FARAWAY2"
        box_faraway2 = {"xmin": 600, "ymin": 300, "xmax": 700, "ymax": 400} # d_left=600, d_right=580, d_top=300, d_bottom=320. min: 300
        self.assertEqual(calculate_distance_from_edge(box_faraway2, 1280, 720), 300)
        
        # Box from example: "CLOSE3"
        box_close3 = {"xmin": 1200, "ymin": 650, "xmax": 1270, "ymax": 710} # d_left=1200, d_right=10, d_top=650, d_bottom=10. min: 10
        self.assertEqual(calculate_distance_from_edge(box_close3, 1280, 720), 10)

        # Invalid box (xmax < xmin)
        invalid_box = {"xmin": 200, "ymin": 100, "xmax": 100, "ymax": 200}
        self.assertIsNone(calculate_distance_from_edge(invalid_box, 1280, 720))
        
        # Box outside image bounds
        outside_box = {"xmin": 1300, "ymin": 100, "xmax": 1400, "ymax": 200}
        self.assertIsNone(calculate_distance_from_edge(outside_box, 1280, 720))

    @patch('webhook_module.update_bay')
    @patch('webhook_module.requests.get')
    @patch('webhook_module.firebase_handler.upload_to_firebase_storage')
    @patch('webhook_module.crop_detected_objects') # crop_detected_objects is called internally
    def test_process_webhook_data_selects_furthest_plate(self, 
                                                         mock_crop_objects, 
                                                         mock_firebase_upload, 
                                                         mock_requests_get, 
                                                         mock_update_bay):
        # Mock return values for dependencies
        mock_requests_get.return_value.json.return_value = {
            "camera_ip": "*******",
            "bays": [{"ptz_id": "1", "status": "vacant", "plate": None}] 
            # Make sure the bay exists for the ptz_id in test data
        }
        mock_requests_get.return_value.status_code = 200
        mock_firebase_upload.return_value = "http://mocked_url_for_image.jpg"
        # crop_detected_objects returns (cropped_plate_bytes, cropped_vehicle_bytes)
        mock_crop_objects.return_value = (b"fake_plate_bytes", b"fake_vehicle_bytes")


        # Mock input arguments for process_webhook_data
        image_content = b"fake_image_content" # Not None, so upload logic is triggered
        p_mqtt_client_mock = MagicMock()
        api_server_ip_mock = "********"
        sydney_tz_mock = MagicMock() # Not directly used in selection but required by function
        
        # Sample webhook data
        webhook_payload = {
            "data": {
                "camera_id": "testcam_1", # serial_ptz format
                "results": [
                    {"box": {"xmin": 10, "ymin": 10, "xmax": 100, "ymax": 100}, "plate": "CLOSE1", "region": {"code": "gb-eng"}, "vehicle": {"score": 0.81}, "score": 0.91},
                    {"box": {"xmin": 600, "ymin": 300, "xmax": 700, "ymax": 400}, "plate": "FARAWAY2", "region": {"code": "gb-wls"}, "vehicle": {"score": 0.82}, "score": 0.92}, # Expected
                    {"box": {"xmin": 1200, "ymin": 650, "xmax": 1270, "ymax": 710}, "plate": "CLOSE3", "region": {"code": "gb-sct"}, "vehicle": {"score": 0.83}, "score": 0.93}
                ]
            }
        }

        # Run the function
        asyncio.run(process_webhook_data(webhook_payload, 
                                         image_content, 
                                         p_mqtt_client_mock, 
                                         api_server_ip_mock, 
                                         sydney_tz_mock))

        # Assertions
        mock_update_bay.assert_called_once()
        
        # Get the arguments passed to update_bay
        args, kwargs = mock_update_bay.call_args
        
        # bay_to_update (first positional arg), plate_number (second positional arg)
        # print(f"update_bay called with args: {args}") # For debugging
        
        # Check the selected plate number
        self.assertEqual(args[1], "FARAWAY2") # plate_number is the second argument to update_bay
        
        # Check the vehicle score associated with the selected plate
        # vehicle_score is the third argument to update_bay
        self.assertEqual(args[2], 0.82) 

        # Verify crop_detected_objects was called with the selected result
        mock_crop_objects.assert_called_once()
        crop_args, crop_kwargs = mock_crop_objects.call_args
        # The second argument to crop_detected_objects is data_info (or a subset of it)
        # print(f"crop_detected_objects called with: {crop_args[1]}") # For debugging
        self.assertEqual(len(crop_args[1]['results']), 1)
        self.assertEqual(crop_args[1]['results'][0]['plate'], "FARAWAY2")

        # Verify firebase upload was called for plate and vehicle (if plate detected)
        # Based on the logic, if detected_plate is not None and image_content is present,
        # two uploads will happen: one for plate, one for vehicle.
        expected_firebase_calls = [
            call(b"fake_plate_bytes", "plate-images/FARAWAY2_WLS_{}.jpeg".format(unittest.mock.ANY)), # ANY for timestamp
            call(b"fake_image_content", "vehicle-images/FARAWAY2_WLS_{}.jpeg".format(unittest.mock.ANY))
        ]
        # This part of assertion might be tricky due to timestamp in filename.
        # We can check the number of calls and parts of the filename.
        self.assertEqual(mock_firebase_upload.call_count, 2)
        
        # Check that the plate image filename contains the correct plate
        first_call_args = mock_firebase_upload.call_args_list[0][0] # Args of first call
        self.assertTrue(first_call_args[1].startswith("plate-images/FARAWAY2_WLS_"))
        self.assertEqual(first_call_args[0], b"fake_plate_bytes")

        second_call_args = mock_firebase_upload.call_args_list[1][0] # Args of second call
        self.assertTrue(second_call_args[1].startswith("vehicle-images/FARAWAY2_WLS_"))
        self.assertEqual(second_call_args[0], b"fake_image_content")


    @patch('webhook_module.update_bay')
    @patch('webhook_module.requests.get')
    @patch('webhook_module.firebase_handler.upload_to_firebase_storage')
    @patch('webhook_module.crop_detected_objects')
    def test_process_webhook_data_no_valid_plate_selected(self, 
                                                          mock_crop_objects, 
                                                          mock_firebase_upload, 
                                                          mock_requests_get, 
                                                          mock_update_bay):
        # Scenario: results are present, but none yield a valid box for selection
        # or all boxes are invalid / too close to edge to be selected based on some threshold (if applicable)
        # or simply no plate string in the selected box.
        mock_requests_get.return_value.json.return_value = {
            "camera_ip": "*******",
            "bays": [{"ptz_id": "1", "status": "occupied", "plate": "OLDPLATE"}] 
        }
        mock_requests_get.return_value.status_code = 200
        mock_firebase_upload.return_value = "http://mocked_url_for_image.jpg"
        mock_crop_objects.return_value = (None, None) # No plate cropped

        image_content = b"fake_image_content_no_plate"
        p_mqtt_client_mock = MagicMock()
        api_server_ip_mock = "********"
        sydney_tz_mock = MagicMock()
        
        webhook_payload = {
            "data": {
                "camera_id": "testcam_1",
                "results": [
                    # Invalid box (xmax < xmin)
                    {"box": {"xmin": 200, "ymin": 10, "xmax": 100, "ymax": 100}, "plate": "INVALIDBOX1", "region": {"code": "gb-eng"}, "vehicle": {"score": 0.81}, "score": 0.91},
                    # Box with no plate string
                    {"box": {"xmin": 600, "ymin": 300, "xmax": 700, "ymax": 400}, "plate": None, "region": {"code": "gb-wls"}, "vehicle": {"score": 0.82}, "score": 0.92},
                ]
            }
        }

        asyncio.run(process_webhook_data(webhook_payload, image_content, p_mqtt_client_mock, api_server_ip_mock, sydney_tz_mock))
        
        mock_update_bay.assert_called_once()
        args, _ = mock_update_bay.call_args
        
        # Expect detected_plate to be None, so status becomes 'vacant' (or remains 'occupied' if that's the logic for no plate)
        # The bay status was 'occupied' with 'OLDPLATE'. Now no plate is detected. So it should become 'vacant'.
        self.assertIsNone(args[1]) # plate_number is None
        
        # crop_detected_objects should not be called if no valid plate is ultimately selected for cropping,
        # which is the case when detected_plate is None.
        mock_crop_objects.assert_not_called()


        # Firebase upload for plate should not happen. Vehicle image (full) should be uploaded with a generic name.
        self.assertEqual(mock_firebase_upload.call_count, 1)
        upload_args, _ = mock_firebase_upload.call_args
        self.assertEqual(upload_args[0], image_content) # Full image
        self.assertTrue(upload_args[1].startswith("vehicle-images/unknown_plate_testcam_1_"))


    @patch('webhook_module.update_bay')
    @patch('webhook_module.requests.get')
    @patch('webhook_module.firebase_handler.upload_to_firebase_storage')
    @patch('webhook_module.crop_detected_objects')
    def test_process_webhook_data_empty_results(self, 
                                                mock_crop_objects, 
                                                mock_firebase_upload, 
                                                mock_requests_get, 
                                                mock_update_bay):
        # Scenario: 'results' list is empty
        mock_requests_get.return_value.json.return_value = {
            "camera_ip": "*******",
            "bays": [{"ptz_id": "1", "status": "occupied", "plate": "OLDPLATE"}] 
        }
        mock_requests_get.return_value.status_code = 200
        # No crop should happen, no specific plate/vehicle image to upload other than generic one if image_content exists
        mock_firebase_upload.return_value = "http://mocked_url_for_generic_image.jpg"


        image_content = b"fake_image_content_empty_results" # Content for generic vehicle image
        p_mqtt_client_mock = MagicMock()
        api_server_ip_mock = "********"
        sydney_tz_mock = MagicMock()
        
        webhook_payload = {
            "data": {
                "camera_id": "testcam_1",
                "results": [] # Empty results
            }
        }

        asyncio.run(process_webhook_data(webhook_payload, image_content, p_mqtt_client_mock, api_server_ip_mock, sydney_tz_mock))
        
        mock_update_bay.assert_called_once()
        args, _ = mock_update_bay.call_args
        self.assertIsNone(args[1]) # No plate detected, so plate_number is None
        
        mock_crop_objects.assert_not_called() # crop_detected_objects should not be called if results list is empty
        
        # Only generic vehicle image upload if image_content is present
        self.assertEqual(mock_firebase_upload.call_count, 1)
        upload_args, _ = mock_firebase_upload.call_args
        self.assertEqual(upload_args[0], image_content)
        self.assertTrue(upload_args[1].startswith("vehicle-images/unknown_plate_testcam_1_"))

if __name__ == '__main__':
    unittest.main()
