import asyncio
from aiohttp import web
import json
import logging
import requests
from datetime import datetime, timedelta
import pytz
import os
import errno
from PIL import Image, UnidentifiedImageError
import io
from typing import Union, Tuple
import time
import paho.mqtt.client as mqtt # Re-added for direct MQTT publishing

# Azure IoT Edge specific imports
from azure.iot.device.aio import IoTHubModuleClient
# from azure.iot.device import Message # No longer sending Edge messages

# Import Firebase handler
import firebase_handler

first_ptz_seen = {}

LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s  %(levelname)-8s | %(name)s | %(message)s",
)
logger = logging.getLogger("webhook_module")

sydney_tz = pytz.timezone("Australia/Sydney")

HOST_NAME = os.getenv("WEBHOOK_HOST_NAME", '0.0.0.0')
PORT_NUMBER = int(os.getenv("WEBHOOK_PORT", 8082))
API_SERVER_IP = os.getenv("API_SERVER_IP", '***********')
MQTT_BROKER = os.getenv("MQTT_BROKER", '***********') # For direct MQTT
MQTT_PORT = int(os.getenv("MQTT_PORT", 1883))      # For direct MQTT
MQTT_TOPIC = os.getenv("MQTT_TOPIC", 'update')       # For direct MQTT

iot_module_client = None # Renamed to avoid conflict with paho mqtt_client
mqtt_client = None       # Paho MQTT client instance

def crop_detected_objects(image_content: bytes, results_json: dict) -> Tuple[Union[bytes, None], Union[bytes, None]]:
    cropped_plate_bytes = None
    cropped_vehicle_bytes = None
    if not image_content: return None, None
    if not results_json: return None, None
    try:
        original_image = Image.open(io.BytesIO(image_content))
        img_width, img_height = original_image.size
    except Exception as img_err:
        logger.error(f"Error opening image: {img_err}", exc_info=True)
        return None, None

    results = results_json.get('results')
    if not results: return None, None
    first_result = results[0]
    if not isinstance(first_result, dict): return None, None

    try:
        plate_box = first_result.get('box')
        if plate_box and isinstance(plate_box, dict):
            coords = {k: plate_box.get(k) for k in ('xmin', 'ymin', 'xmax', 'ymax')}
            if None not in coords.values():
                xmin, ymin, xmax, ymax = map(int, coords.values())
                if 0 <= xmin < xmax <= img_width and 0 <= ymin < ymax <= img_height:
                    crop_area = (xmin, ymin, xmax, ymax)
                    cropped_plate_image = original_image.crop(crop_area)
                    byte_buffer = io.BytesIO()
                    cropped_plate_image.save(byte_buffer, format='JPEG', quality=90)
                    cropped_plate_bytes = byte_buffer.getvalue()
    except Exception as e:
        logger.error(f"Error during plate cropping: {e}", exc_info=True)

    try:
        vehicle_info = first_result.get('vehicle')
        if vehicle_info and isinstance(vehicle_info, dict):
            vehicle_box = vehicle_info.get('box')
            if vehicle_box and isinstance(vehicle_box, dict):
                coords = {k: vehicle_box.get(k) for k in ('xmin', 'ymin', 'xmax', 'ymax')}
                if None not in coords.values():
                    xmin, ymin, xmax, ymax = map(int, coords.values())
                    if 0 <= xmin < xmax <= img_width and 0 <= ymin < ymax <= img_height:
                        crop_area = (xmin, ymin, xmax, ymax)
                        cropped_vehicle_image = original_image.crop(crop_area)
                        byte_buffer = io.BytesIO()
                        cropped_vehicle_image.save(byte_buffer, format='JPEG', quality=85)
                        cropped_vehicle_bytes = byte_buffer.getvalue()
    except Exception as e:
        logger.error(f"Error during vehicle cropping: {e}", exc_info=True)
    return cropped_plate_bytes, cropped_vehicle_bytes

def calculate_distance_from_edge(box: dict, img_width: int, img_height: int) -> Union[float, None]:
    """
    Calculates the minimum distance of a bounding box from any edge of the image.
    Args:
        box: A dictionary with 'xmin', 'ymin', 'xmax', 'ymax' keys.
        img_width: The width of the image.
        img_height: The height of the image.
    Returns:
        The minimum distance from an edge, or None if box is invalid.
    """
    try:
        xmin = int(box['xmin'])
        ymin = int(box['ymin'])
        xmax = int(box['xmax'])
        ymax = int(box['ymax'])

        # Basic validation for coordinates
        if not (0 <= xmin < xmax <= img_width and 0 <= ymin < ymax <= img_height):
            logger.warning(f"Invalid box coordinates: {box} for image size {img_width}x{img_height}")
            return None

        distance_left = xmin
        distance_right = img_width - xmax
        distance_top = ymin
        distance_bottom = img_height - ymax

        return min(distance_left, distance_right, distance_top, distance_bottom)
    except (KeyError, TypeError, ValueError) as e:
        logger.error(f"Error calculating distance from edge: {e}, box: {box}", exc_info=True)
        return None

async def update_bay(bay_to_update, plate_number, vehicle_score, ptz_id, serial, current_api_server_ip, camera_data, current_sydney_tz, utc_time, image_url, cropped_url, p_mqtt_client):
    # p_mqtt_client is the paho mqtt client instance
    try:
        if plate_number is not None:
            # When a plate is detected - only publish to MQTT, don't update DB
            status = 'occupied'
            sydney_time = datetime.now(current_sydney_tz)
            time_parked = sydney_time.isoformat()
            
            # Skip database update for occupied bays
            logger.info(f"Plate {plate_number} detected for bay with PTZ ID {ptz_id}. Skipping DB update - BayAlerts will handle.")
            
            # Just publish to MQTT for BayAlerts to handle
            mqtt_message_payload = {
                'serial': serial, 
                'ptz_id': ptz_id, 
                'bay_id': bay_to_update.get('bay_id', bay_to_update.get('id')),
                'plate': plate_number, 
                'status': status, 
                'time_parked': time_parked,
                'tenant': bay_to_update.get('tenant'), 
                'timestamp': utc_time,
                'image_url': image_url, 
                'cropped_url': cropped_url
            }
            
            logger.info(f"Publishing to MQTT topic '{MQTT_TOPIC}': {json.dumps(mqtt_message_payload)}")
            if p_mqtt_client:
                result, mid = p_mqtt_client.publish(MQTT_TOPIC, json.dumps(mqtt_message_payload))
                if result == mqtt.MQTT_ERR_SUCCESS:
                    logger.info(f"Successfully published message to MQTT topic {MQTT_TOPIC}, mid: {mid}")
                else:
                    logger.error(f"Failed to publish message to MQTT topic {MQTT_TOPIC}, error code: {result}")
            else:
                logger.error("MQTT client not available for publishing.")
                
            # Still stop guard tours for occupied bays
            if camera_data.get('camera_ip'):
                await stop_guard_tours(camera_data.get('camera_ip'))
                
            return  # Skip the rest of the function
            
        else:
            # When no plate is detected - Webhook fully handles this case
            plate_number = None
            status = 'vacant'
            time_parked = None
            parked_tenant = None
            tenantID = None
            cardholderName = None
            
            # Continue with database update for vacant bays
            patch_data = {
                "plate": plate_number, 
                "ptz_id": str(ptz_id), 
                "status": status,
                "time_parked": time_parked, 
                "tenant": bay_to_update.get('tenant'),
                "parked_tenant": parked_tenant, 
                "tenantID": tenantID, 
                "cardholderName": cardholderName
            }
            
            patch_response = requests.patch(f'http://{current_api_server_ip}:8000/cameras/{serial}/bays', params=patch_data)
            
            if patch_response.status_code == 200:
                logger.info(f"Successfully updated bay {ptz_id} for camera {serial} to vacant via API")
                # Also publish vacant status to MQTT
                mqtt_message_payload = {
                    'serial': serial, 
                    'ptz_id': ptz_id, 
                    'bay_id': bay_to_update.get('bay_id', bay_to_update.get('id')),
                    'plate': plate_number, 
                    'status': status, 
                    'time_parked': time_parked,
                    'tenant': bay_to_update.get('tenant'), 
                    'parked_tenant': parked_tenant,
                    'tenantID': tenantID, 
                    'cardholderName': cardholderName, 
                    'timestamp': utc_time,
                    'image_url': image_url, 
                    'cropped_url': cropped_url
                }
                
                logger.info(f"Publishing vacant status to MQTT topic '{MQTT_TOPIC}'")
                if p_mqtt_client:
                    result, mid = p_mqtt_client.publish(MQTT_TOPIC, json.dumps(mqtt_message_payload))
                    if result == mqtt.MQTT_ERR_SUCCESS:
                        logger.info(f"Successfully published vacant status to MQTT topic {MQTT_TOPIC}, mid: {mid}")
                    else:
                        logger.error(f"Failed to publish vacant status to MQTT topic {MQTT_TOPIC}, error code: {result}")
                else:
                    logger.error("MQTT client not available for publishing.")
            else:
                logger.error(f"Failed to update bay API: {patch_response.status_code} {patch_response.text}")
    except Exception as e:
        logger.error(f"Error in update_bay: {e}", exc_info=True)

# Helper function to stop guard tours
async def stop_guard_tours(camera_ip):
    for guard_tour_index in range(5):
        try:
            auth_user = os.getenv("CAMERA_USERNAME", "root")
            auth_pass = os.getenv("CAMERA_PASSWORD", "M1ecrdry1!")
            requests.get(f'http://{camera_ip}/axis-cgi/param.cgi?action=update&GuardTour.G{guard_tour_index}.Running=no',
                         auth=requests.auth.HTTPDigestAuth(auth_user, auth_pass), timeout=5).raise_for_status()
            logger.info(f"Stopped guard tour G{guard_tour_index} on {camera_ip}")
        except requests.exceptions.HTTPError as http_err:
            if http_err.response.status_code == 404: 
                logger.debug(f"Guard tour G{guard_tour_index} not found.")
                break
            else: 
                logger.warning(f"Failed to stop G{guard_tour_index}: {http_err}")
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Error stopping G{guard_tour_index}: {req_err}")
            break

async def handle_webhook_request(request):
    global mqtt_client # Use the global paho mqtt client
    upload_to = "uploads"; image_content = None; json_data = None
    try:
        if request.method == "GET": return web.Response(text="POST only")
        if not os.path.exists(upload_to): os.makedirs(upload_to)

        if request.content_type.startswith('multipart/'):
            form_data = await request.post()
            for field_name, field in form_data.items():
                if field_name == 'json':
                    json_string = field.file.read().decode('utf-8') if hasattr(field, 'file') else field
                    json_data = json.loads(json_string)
                elif hasattr(field, 'filename') and field.filename:
                    image_content = field.file.read()
                    if image_content: open(os.path.join(upload_to, field.filename), 'wb').write(image_content)
        elif request.content_type == 'application/json':
            json_data = await request.json()
        else:
            return web.json_response({'status': 'error', 'message': 'Unsupported Content-Type'}, status=415)

        if json_data:
            await process_webhook_data(json_data, image_content, mqtt_client, API_SERVER_IP, sydney_tz)
            return web.json_response({'status': 'success'})
        else:
            return web.json_response({'status': 'error', 'message': 'No JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error in handle_webhook_request: {e}", exc_info=True)
        return web.json_response({'status': 'error', 'message': 'Internal Server Error'}, status=500)

async def process_webhook_data(data, image_content, p_mqtt_client, current_api_server_ip, current_sydney_tz):
    plate_image_url = None; vehicle_image_url = None
    try:
        data_info = data.get('data', {})
        camera_id = data_info.get('camera_id')
        if not camera_id or '_' not in camera_id: logger.error(f"Invalid camera_id: {camera_id}"); return
        serial, ptz_id = camera_id.split('_', 1)

        response = requests.get(f'http://{current_api_server_ip}:8000/cameras/{serial}', timeout=10)
        response.raise_for_status()
        camera_data = response.json()
        camera_ip = camera_data.get('camera_ip')

        current_time = datetime.utcnow()
        iso_time_string_utc = current_time.isoformat() + "Z"
        reset_interval = timedelta(seconds=60)

        if serial not in first_ptz_seen or (current_time - first_ptz_seen[serial]['timestamp']) > reset_interval:
            first_ptz_seen[serial] = {'ptz_id': ptz_id, 'timestamp': current_time, 'camera_ip': camera_ip}
        elif first_ptz_seen[serial]['ptz_id'] == ptz_id and camera_ip:
            logger.info(f"PTZ ID {ptz_id} cycled for {serial}. Stopping tours.")
            for idx in range(5): 
                try: 
                    auth_user = os.getenv("CAMERA_USERNAME", "root")
                    auth_pass = os.getenv("CAMERA_PASSWORD", "M1ecrdry1!")
                    requests.get(f'http://{camera_ip}/axis-cgi/param.cgi?action=update&GuardTour.G{idx}.Running=no', auth=requests.auth.HTTPDigestAuth(auth_user, auth_pass), timeout=5).raise_for_status() 
                except requests.exceptions.HTTPError as http_err:
                    if http_err.response.status_code == 404: break 
                    logger.warning(f"Failed to stop G{idx} during cycle detection: {http_err}") 
                except requests.exceptions.RequestException as req_err:
                    logger.error(f"Error stopping G{idx} during cycle detection: {req_err}")
                    break 
            if serial in first_ptz_seen: del first_ptz_seen[serial]

        results = data_info.get('results', [])
        detected_plate = None
        vehicle_score = 0.0
        region_code = "XX"
        selected_result = None
        max_distance = -1

        img_width, img_height = 1280, 720 # Assuming fixed dimensions as per requirement

        if results:
            for result in results:
                if not isinstance(result, dict):
                    logger.warning(f"Skipping invalid result item: {result}")
                    continue
                
                plate_box = result.get('box')
                if plate_box and isinstance(plate_box, dict):
                    distance = calculate_distance_from_edge(plate_box, img_width, img_height)
                    if distance is not None and distance > max_distance:
                        max_distance = distance
                        selected_result = result
                else:
                    logger.warning(f"Result missing 'box' or 'box' is not a dict: {result}")

            if selected_result:
                logger.info(f"Selected result for PTZ {ptz_id} with distance {max_distance}: {selected_result.get('plate')}")
                raw_plate = selected_result.get('plate')
                if raw_plate:
                    detected_plate = raw_plate.upper()
                
                region_info = selected_result.get('region')
                if region_info and region_info.get('code'):
                    region_code = region_info['code'].strip().upper()
                
                vehicle_info = selected_result.get('vehicle') # This will be used later
                if vehicle_info and 'score' in vehicle_info: # Redundant if only using selected_result for score
                    vehicle_score = vehicle_info['score'] # If vehicle_score should come from selected_result's vehicle
            else:
                logger.info(f"No suitable plate found for PTZ {ptz_id} after evaluating {len(results)} results.")
        else:
            logger.info(f"No results found in webhook data for PTZ {ptz_id}.")
        
        new_status = 'occupied' if detected_plate else 'vacant'
        bay_to_update = next((b for b in camera_data.get('bays', []) if str(b.get('ptz_id')) == str(ptz_id)), None)
        if not bay_to_update: logger.error(f"Bay PTZ {ptz_id} not found for cam {serial}"); return

        current_status = bay_to_update.get('status'); current_plate = bay_to_update.get('plate')
        should_update = new_status != current_status or (new_status == 'occupied' and detected_plate != current_plate)

        if should_update:
            reason = f"Status change ({current_status} -> {new_status})" if new_status != current_status else f"Plate change ({current_plate} -> {detected_plate})"
            logger.info(f"Update for Bay PTZ {ptz_id}: {reason}")
            # The crop_detected_objects call modification is for a subsequent step.
            # For now, we ensure that if detected_plate is None, no cropping/upload occurs for plate.
            # The vehicle image upload uses the full image_content, which is independent of selected_result.
            if detected_plate and image_content:
                # Pass the selected_result to crop_detected_objects (or a structure mimicking data_info if needed by crop_detected_objects)
                # For now, data_info is still passed, this will be addressed next.
                # The key is that detected_plate, region_code are now from selected_result.
                
                # Placeholder for crop_detected_objects modification:
                # For now, we are not changing its arguments, but it should eventually use selected_result.
                # This might mean passing selected_result directly or constructing a new dict for it.
                # temp_data_info_for_crop = {'results': [selected_result]} if selected_result else {'results': []}
                # cropped_plate_bytes, _ = crop_detected_objects(image_content, temp_data_info_for_crop)

                cropped_plate_bytes, _ = crop_detected_objects(image_content, {'results': [selected_result]} if selected_result else {'results': []})


                if cropped_plate_bytes:
                    state = region_code.split("-")[1].upper() if "-" in region_code else "UNKNOWN"
                    plate_filename = f"plate-images/{detected_plate}_{state}_{int(time.time())}.jpeg"
                    plate_image_url = firebase_handler.upload_to_firebase_storage(cropped_plate_bytes, plate_filename)
                
                # Vehicle image upload (full image) - this part remains largely the same as it uses the whole image_content
                # but the filename now uses the selected_result's plate and region
                if image_content: 
                    state_for_vehicle = region_code.split("-")[1].upper() if "-" in region_code else "UNKNOWN"
                    vehicle_filename = f"vehicle-images/{detected_plate}_{state_for_vehicle}_{int(time.time())}.jpeg"
                    vehicle_image_url = firebase_handler.upload_to_firebase_storage(image_content, vehicle_filename)
            elif not detected_plate and image_content: # Case where no plate is detected but we might still want the full image if logic allows
                logger.info(f"No plate detected for PTZ {ptz_id}, but image content present. Uploading vehicle image with generic name.")
                generic_filename_base = f"unknown_plate_{camera_id}_{int(time.time())}"
                # If there's a selected_result (e.g. vehicle detected but no plate), use its details if available for naming
                if selected_result and selected_result.get('vehicle'): # Example detail
                     # Potentially use vehicle type or other info if available in selected_result.get('vehicle') for filename
                     pass

                vehicle_filename = f"vehicle-images/{generic_filename_base}.jpeg"
                vehicle_image_url = firebase_handler.upload_to_firebase_storage(image_content, vehicle_filename)


            # Use vehicle_score from the selected_result if available
            final_vehicle_score = 0.0
            if selected_result:
                vehicle_info_from_selected = selected_result.get('vehicle')
                if vehicle_info_from_selected and 'score' in vehicle_info_from_selected:
                    final_vehicle_score = vehicle_info_from_selected['score']
                elif 'score' in selected_result: # Fallback if score is at result root for some reason
                    final_vehicle_score = selected_result['score'] 
            
            await update_bay(bay_to_update, detected_plate, final_vehicle_score, ptz_id, serial,
                             current_api_server_ip, camera_data, current_sydney_tz, iso_time_string_utc,
                             vehicle_image_url, plate_image_url, p_mqtt_client)
        else:
            logger.info(f"No update for Bay PTZ {ptz_id}: Status '{current_status}', Plate '{current_plate}'. Detected '{detected_plate}'.")
    except Exception as e:
        logger.error(f"Error processing webhook data for PTZ {ptz_id}: {e}", exc_info=True)

async def twin_patch_handler(patch):
    global API_SERVER_IP, HOST_NAME, PORT_NUMBER, MQTT_BROKER, MQTT_PORT, MQTT_TOPIC, mqtt_client
    logger.info(f"Twin patch received: {patch}")
    new_config = patch.get("config", patch) 
    
    old_mqtt_broker = MQTT_BROKER
    old_mqtt_port = MQTT_PORT

    API_SERVER_IP = new_config.get("API_SERVER_IP", API_SERVER_IP)
    HOST_NAME = new_config.get("WEBHOOK_HOST_NAME", HOST_NAME)
    PORT_NUMBER = int(new_config.get("WEBHOOK_PORT", PORT_NUMBER))
    MQTT_BROKER = new_config.get("MQTT_BROKER", MQTT_BROKER)
    MQTT_PORT = int(new_config.get("MQTT_PORT", MQTT_PORT))
    MQTT_TOPIC = new_config.get("MQTT_TOPIC", MQTT_TOPIC)
    logger.info("Applied twin config: API_SERVER_IP={}, HOST_NAME={}, PORT_NUMBER={}, MQTT_BROKER={}, MQTT_PORT={}, MQTT_TOPIC={}".format(API_SERVER_IP, HOST_NAME, PORT_NUMBER, MQTT_BROKER, MQTT_PORT, MQTT_TOPIC))

    if (MQTT_BROKER != old_mqtt_broker or MQTT_PORT != old_mqtt_port) and mqtt_client:
        logger.info(f"MQTT broker details changed. Reconnecting MQTT client to {MQTT_BROKER}:{MQTT_PORT}")
        try:
            if mqtt_client.is_connected(): # Check if connected before stopping/disconnecting
                mqtt_client.loop_stop() 
                mqtt_client.disconnect()
            mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)
            mqtt_client.loop_start()
            logger.info("MQTT client reconnected successfully.")
        except Exception as e:
            logger.error(f"Error reconnecting MQTT client: {e}", exc_info=True)

async def main_async():
    global iot_module_client, mqtt_client, API_SERVER_IP, HOST_NAME, PORT_NUMBER, MQTT_BROKER, MQTT_PORT, MQTT_TOPIC
    runner = None
    try:
        logger.info("Starting webhook IoT Edge module (MQTT direct publish mode)...")
        if not firebase_handler.initialize_firebase(): 
            logger.error("Firebase init failed.")
        else: logger.info("Firebase init success.")

        mqtt_client = mqtt.Client()
        def on_connect(client, userdata, flags, rc):
            logger.info(f"Connected to MQTT Broker {MQTT_BROKER} with result code {rc}")
        def on_disconnect(client, userdata, rc):
            logger.warning(f"Disconnected from MQTT Broker with result code {rc}")
        mqtt_client.on_connect = on_connect
        mqtt_client.on_disconnect = on_disconnect
        try:
            mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)
            mqtt_client.loop_start()
        except Exception as mqtt_e:
            logger.error(f"Initial MQTT connection/loop start failed: {mqtt_e}", exc_info=True)

        iot_module_client = IoTHubModuleClient.create_from_edge_environment()
        await iot_module_client.connect()
        logger.info("Connected to Edge Hub (for twin/methods).")

        twin = await iot_module_client.get_twin()
        if twin and "desired" in twin:
            desired_props = twin.get("desired", {})
            config_to_apply = desired_props.get("config", desired_props)
            API_SERVER_IP = config_to_apply.get("API_SERVER_IP", API_SERVER_IP)
            HOST_NAME = config_to_apply.get("WEBHOOK_HOST_NAME", HOST_NAME)
            PORT_NUMBER = int(config_to_apply.get("WEBHOOK_PORT", PORT_NUMBER))
            new_mqtt_broker = config_to_apply.get("MQTT_BROKER", MQTT_BROKER)
            new_mqtt_port = int(config_to_apply.get("MQTT_PORT", MQTT_PORT))
            MQTT_TOPIC = config_to_apply.get("MQTT_TOPIC", MQTT_TOPIC)
            logger.info("Initial twin config read.")

            if mqtt_client: # Ensure mqtt_client is initialized
                is_currently_connected = False
                try:
                    is_currently_connected = mqtt_client.is_connected()
                except AttributeError: # Older paho-mqtt might not have is_connected()
                    pass # Assume not connected or handle differently

                if (new_mqtt_broker != MQTT_BROKER or new_mqtt_port != MQTT_PORT) and is_currently_connected:
                    logger.info(f"MQTT broker details in twin differ. Reconnecting to {new_mqtt_broker}:{new_mqtt_port}")
                    MQTT_BROKER = new_mqtt_broker
                    MQTT_PORT = new_mqtt_port
                    mqtt_client.loop_stop()
                    mqtt_client.disconnect()
                    mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)
                    mqtt_client.loop_start()
                elif not is_currently_connected:
                    MQTT_BROKER = new_mqtt_broker
                    MQTT_PORT = new_mqtt_port
                    logger.info(f"MQTT client not connected, attempting from twin: {MQTT_BROKER}:{MQTT_PORT}")
                    try:
                        mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)
                        mqtt_client.loop_start()
                    except Exception as mqtt_e:
                        logger.error(f"MQTT connection from twin failed: {mqtt_e}")

        iot_module_client.on_twin_desired_properties_patch_received = twin_patch_handler
        
        app = web.Application()
        app.router.add_post('/webhook', handle_webhook_request)
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, HOST_NAME, PORT_NUMBER)
        await site.start()
        logger.info(f"HTTP server started at http://{HOST_NAME}:{PORT_NUMBER}/webhook")

        while True: await asyncio.sleep(3600)

    except Exception as e:
        logger.error(f"Unhandled exception in main_async: {e}", exc_info=True)
    finally:
        if iot_module_client: await iot_module_client.disconnect()
        if mqtt_client: 
            try:
                # Check if loop is active before stopping
                if mqtt_client._thread and mqtt_client._thread.is_alive():
                    mqtt_client.loop_stop()
                # Check if connected before disconnecting
                if mqtt_client.is_connected():
                    mqtt_client.disconnect()
            except Exception as final_mqtt_e:
                logger.error(f"Error during final MQTT cleanup: {final_mqtt_e}")
        if runner: await runner.cleanup()
        logger.info("Webhook module shut down.")

if __name__ == '__main__':
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("Module stopped by user.")
    except Exception as e:
        logger.error(f"Module failed: {e}", exc_info=True)
