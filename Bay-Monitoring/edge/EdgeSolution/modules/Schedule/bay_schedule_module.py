# bay_schedule_module.py
import asyncio
import json
import logging
import os
import signal
from collections import defaultdict
from datetime import datetime
from typing import List, Dict, Any # Keep List for internal processing

import aiohttp
import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from azure.iot.device.aio import IoTHubModuleClient

# ──────────────────────────────────────────────
# defaults (over-ridden by twin)
# ──────────────────────────────────────────────
API_BASE_URL   = os.getenv("API_BASE_URL", "http://10.0.71.132:8000")
# --- Changed Defaults to Comma-Separated Strings ---
VACANT_TIMES   = os.getenv("VACANT_TIMES", "09:00") # e.g., "09:00,10:30"
OCCUPIED_TIMES = os.getenv("OCCUPIED_TIMES", "22:00") # e.g., "18:00,22:00"
# --- End Change ---
TIME_ZONE      = os.getenv("TIME_ZONE", "Australia/Sydney")
API_TIMEOUT    = int(os.getenv("API_TIMEOUT", 30)) # Ensure timeout is int

LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s  %(levelname)-8s | %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("bay-scheduler")

# Global scheduler instance
scheduler = AsyncIOScheduler()

# ──────────────────────────────────────────────
# Job Scheduling Helper
# ──────────────────────────────────────────────
# --- Added tz parameter ---
def _add_job(time_str: str, status: str, tz: pytz.BaseTzInfo) -> bool:
    """Adds a single job to the scheduler using the specified timezone. Returns True if successful."""
    try:
        time_str = time_str.strip()
        hour, minute = map(int, time_str.split(":"))
        job_id = f"{status}-{time_str}"
        scheduler.add_job(
            check_bays,
            "cron",
            hour=hour,
            minute=minute,
            args=[status],
            id=job_id,
            replace_existing=True,
            misfire_grace_time=600,
            timezone=tz # --- Pass the timezone object here ---
        )
        # Use the timezone's string representation in the log
        logger.info(f"⏰ Job scheduled: '{status}' check @ {time_str} (ID: {job_id}, Timezone: {str(tz)})")
        return True
    except ValueError:
        logger.error(f"Invalid time format '{time_str}' for {status} job. Expected HH:MM.")
    except Exception as exc:
        logger.error(f"Failed to add {status} job for time '{time_str}': {exc}")
    return False

# ──────────────────────────────────────────────
# Reschedule All Jobs Helper (Updated)
# ──────────────────────────────────────────────
def reschedule_jobs():
    """Removes existing jobs and reschedules based on current global config."""
    global TIME_ZONE, VACANT_TIMES, OCCUPIED_TIMES # TIME_ZONE might be updated if invalid
    logger.info("🔄 Rescheduling jobs based on updated times/timezone...")

    if not scheduler.running:
        logger.debug("Scheduler not running. Jobs will be added correctly on start.")
        # No need to reschedule if not running, initial setup handles it.
        return

    scheduler.remove_all_jobs()

    # --- Determine the target timezone object ---
    target_tz = None
    try:
        target_tz = pytz.timezone(TIME_ZONE)
        logger.info(f"Using timezone for rescheduling: {TIME_ZONE}")
    except Exception as tz_exc:
        original_tz = TIME_ZONE # Store original for logging
        logger.warning(f"Invalid timezone '{original_tz}' specified: {tz_exc}. Falling back to UTC for rescheduling.")
        # Update global TIME_ZONE only if necessary? Or just use UTC locally?
        # Let's just use UTC for this reschedule run, but don't change the global setting yet.
        target_tz = pytz.utc
        # Keep global TIME_ZONE as the potentially invalid one so apply_config can retry later if twin updates it.
    # --- End timezone determination ---

    job_count = 0

    vacant_time_list = []
    if isinstance(VACANT_TIMES, str) and VACANT_TIMES.strip():
        vacant_time_list = [t.strip() for t in VACANT_TIMES.split(',') if t.strip()]
    # Log processing details
    logger.info(f"Processing VACANT_TIMES='{VACANT_TIMES}' -> Times: {vacant_time_list}")
    for t in vacant_time_list:
        if _add_job(t, "vacant", target_tz): # Pass timezone object
             job_count += 1

    occupied_time_list = []
    if isinstance(OCCUPIED_TIMES, str) and OCCUPIED_TIMES.strip():
        occupied_time_list = [t.strip() for t in OCCUPIED_TIMES.split(',') if t.strip()]
    logger.info(f"Processing OCCUPIED_TIMES='{OCCUPIED_TIMES}' -> Times: {occupied_time_list}")
    for t in occupied_time_list:
         if _add_job(t, "occupied", target_tz): # Pass timezone object
             job_count += 1

    if job_count > 0:
        jobs = scheduler.get_jobs()
        logger.info(f"✅ Rescheduling complete. {len(jobs)} jobs currently scheduled.")
        for job in jobs:
             logger.debug(f"  - Job ID: {job.id}, Next Run: {job.next_run_time}")
    else:
        logger.warning("⚠️ No jobs were scheduled. Check VACANT_TIMES and OCCUPIED_TIMES configuration strings.")


# ──────────────────────────────────────────────
# Apply Configuration Helper (Updated for string times)
# ──────────────────────────────────────────────
def apply_config(cfg: dict):
    """
    Applies configuration settings from 'cfg', rescheduling jobs only if needed.
    Expects vacantTimes and occupiedTimes to be comma-separated strings.
    """
    global API_BASE_URL, VACANT_TIMES, OCCUPIED_TIMES, TIME_ZONE
    updated_settings = []
    schedule_needs_update = False

    if "apiBaseUrl" in cfg:
        new_val = cfg["apiBaseUrl"]
        if isinstance(new_val, str) and new_val != API_BASE_URL:
            API_BASE_URL = new_val
            updated_settings.append(f"API_BASE_URL={API_BASE_URL}")
        elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for apiBaseUrl: {type(new_val)}")

    # --- Expect String for VACANT_TIMES ---
    if "vacantTimes" in cfg:
        new_val = cfg["vacantTimes"]
        if isinstance(new_val, str) and new_val != VACANT_TIMES:
            # Basic validation: check if it contains commas or HH:MM pattern? Optional.
            VACANT_TIMES = new_val # Store as string
            updated_settings.append(f"VACANT_TIMES='{VACANT_TIMES}'")
            schedule_needs_update = True
        elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for vacantTimes (expected string): {type(new_val)}")
    # --- End Change ---

    # --- Expect String for OCCUPIED_TIMES ---
    if "occupiedTimes" in cfg:
        new_val = cfg["occupiedTimes"]
        if isinstance(new_val, str) and new_val != OCCUPIED_TIMES:
            OCCUPIED_TIMES = new_val # Store as string
            updated_settings.append(f"OCCUPIED_TIMES='{OCCUPIED_TIMES}'")
            schedule_needs_update = True
        elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for occupiedTimes (expected string): {type(new_val)}")
    # --- End Change ---

    if "timeZone" in cfg:
        new_val = cfg["timeZone"]
        if isinstance(new_val, str) and new_val != TIME_ZONE:
            try:
                 pytz.timezone(new_val)
                 TIME_ZONE = new_val
                 updated_settings.append(f"TIME_ZONE={TIME_ZONE}")
                 schedule_needs_update = True
            except Exception as tz_exc:
                 logger.warning(f"Ignoring invalid timezone '{new_val}' from twin: {tz_exc}")
        elif not isinstance(new_val, str): logger.warning(f"Ignoring invalid type for timeZone: {type(new_val)}")

    if updated_settings:
        logger.info(f"🛠  Config updated via twin: {', '.join(updated_settings)}")
    else:
        logger.info("🛠  Received config update, but no values changed or no recognized keys found.")

    logger.info( # Log the string values
        f"🛠  Current effective config → API={API_BASE_URL}, "
        f"vacantTimes='{VACANT_TIMES}', occupiedTimes='{OCCUPIED_TIMES}', tz={TIME_ZONE}"
    )

    if schedule_needs_update:
        reschedule_jobs()

# ──────────────────────────────────────────────
# Bay‑check logic (No changes needed here)
# ──────────────────────────────────────────────
async def check_bays(status_filter: str):
    """Checks bays matching the status_filter via API calls."""
    try:
        current_tz = pytz.timezone(TIME_ZONE)
    except Exception:
        logger.warning(f"Using UTC for timestamp as configured timezone '{TIME_ZONE}' is invalid.")
        current_tz = pytz.utc
    run_ts = datetime.now(current_tz).strftime("%Y-%m-%d %H:%M")
    logger.info(f"▶ Running scheduled check for '{status_filter}' bays @ {run_ts} ({TIME_ZONE})")
    total_checked = 0
    try:
        # Create session with explicit SSL=False for HTTP URLs
        ssl_param = None if API_BASE_URL.startswith('http://') else True
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=API_TIMEOUT),
            connector=aiohttp.TCPConnector(ssl=ssl_param)
        ) as session:
            logger.debug(f"Fetching camera list from {API_BASE_URL}/cameras/")
            try:
                 cams_r = await session.get(f"{API_BASE_URL}/cameras/")
                 cams_r.raise_for_status()
                 cameras = await cams_r.json()
                 logger.debug(f"Fetched {len(cameras)} camera entries.")
            except Exception as cam_exc:
                 logger.error(f"Failed to fetch camera list: {cam_exc}. Aborting check run.")
                 return
            bays_to_check = []
            for cam in cameras:
                serial = cam.get("serial")
                if not serial: continue
                for bay in cam.get("bays", []):
                    bay_id = bay.get("bay_id")
                    current_status = bay.get("status")
                    if bay_id and current_status == status_filter:
                        bays_to_check.append({"bay_id": bay_id, "serial": serial})
            if not bays_to_check:
                 logger.info(f"No bays found with status '{status_filter}' to check.")
                 return
            logger.info(f"Found {len(bays_to_check)} bays with status '{status_filter}'. Starting checks...")
            processed_count = 0
            for bay_info in bays_to_check:
                bay_id = bay_info["bay_id"]
                serial = bay_info["serial"]
                logger.debug(f"→ Checking bay {bay_id} on cam {serial}")
                try:
                    resp = await session.get(
                        f"{API_BASE_URL}/api/bays/search",
                        params={"bay_id": bay_id}
                        # Remove the per-request timeout since we're using the session timeout
                    )
                    processed_count +=1
                    if resp.status == 200:
                        total_checked += 1
                        logger.debug(f"Bay {bay_id} check successful (Status 200).")
                        await asyncio.sleep(0.1)
                    else:
                        response_text = await resp.text()
                        logger.warning(f"Bay {bay_id} check returned non-200 status {resp.status}. Response: {response_text[:200]}...")
                except asyncio.TimeoutError: 
                    logger.error(f"Timeout checking bay {bay_id} on cam {serial}. API URL: {API_BASE_URL}/api/bays/search?bay_id={bay_id}")
                except aiohttp.ClientConnectionError as conn_err: 
                    logger.error(f"Connection error checking bay {bay_id} on cam {serial}: {conn_err}")
                except Exception as exc: 
                    logger.error(f"Unexpected error checking bay {bay_id} on cam {serial}: {exc}", exc_info=True)
                if processed_count % 10 == 0: logger.info(f"Progress: Checked {processed_count}/{len(bays_to_check)} bays for status '{status_filter}'.")
            logger.info(f"✅ Bay check run for '{status_filter}' finished. Processed {processed_count} API calls. Successful checks: {total_checked}.")
    except Exception as exc:
        logger.error(f"Bay check run for '{status_filter}' failed unexpectedly: {exc}", exc_info=True)

# ──────────────────────────────────────────────
# main life-cycle
# ──────────────────────────────────────────────
async def main():
    """Main execution function: Initializes connections, scheduler, and handles lifecycle."""
    global scheduler # Allow modification (start/shutdown)

    # 1. Edge Hub connect
    try:
        iot_client = IoTHubModuleClient.create_from_edge_environment()
        await iot_client.connect()
        logger.info("🔗 Connected to IoT Edge Hub")
    except Exception as e:
        logger.critical(f"❌ Failed to connect to IoT Edge Hub: {e}", exc_info=True)
        # Optional: depending on requirements, you might want to exit or retry
        raise SystemExit("Connection to IoT Edge Hub failed.")

    # 2. Initial Twin Config Load
    # Apply config first, but don't reschedule yet (scheduler not started)
    try:
        twin = await iot_client.get_twin()
        logger.info("⚙️ Fetched initial device twin.")
        # Safely get the 'config' dictionary, default to empty dict if missing
        initial_config = twin.get("desired", {}).get("config", {})
        if initial_config:
             logger.info("Applying initial configuration from twin 'desired.config'...")
             apply_config(initial_config) # This sets globals like TIME_ZONE, VACANT_TIMES etc.
        else:
             logger.warning("⚠️ No 'config' section found in initial twin's desired properties. Using defaults.")
             apply_config({}) # Apply defaults to globals
    except Exception as e:
        logger.error(f"❌ Failed to get or process initial twin: {e}. Using default config.", exc_info=True)
        apply_config({}) # Ensure defaults are applied on error

    # 3. Handle Live Twin Patches (using robust handler)
    async def twin_patch(patch: Dict[str, Any]):
        """Handles incoming twin patches, detects config structure, and calls apply_config."""
        logger.info(f"⚙️ Received raw twin patch content:\n{json.dumps(patch, indent=2)}")
        config_to_apply = None
        # Check for nested "config" structure
        if "config" in patch and isinstance(patch.get("config"), dict):
            logger.info("Detected 'config' section in patch. Applying settings from within.")
            config_to_apply = patch["config"]
        # Check for recognized flat keys relevant to this module
        elif any(key in patch for key in ["apiBaseUrl", "vacantTimes", "occupiedTimes", "timeZone"]):
            logger.info("Detected recognized config keys at top level of patch. Applying directly.")
            config_to_apply = patch
        else:
            logger.info("Twin patch received, but no 'config' section or recognized config keys found at top level.")
            return # Nothing to apply
        if config_to_apply is not None:
            # Call apply_config, which now calls reschedule_jobs internally if needed
            apply_config(config_to_apply)
        else:
            logger.warning("No configuration data identified to apply in the received patch.")

    try:
        iot_client.on_twin_desired_properties_patch_received = twin_patch
        logger.info("👂 Registered twin patch handler.")
    except Exception as e:
        logger.error(f"❌ Failed to register twin patch handler: {e}")
        # Decide if this is critical - module might continue with initial config

    # 4. Scheduler Start and Initial Job Setup
    try:
        logger.info("Setting up initial schedule based on loaded configuration...")
        # Call reschedule_jobs *once* here to populate the scheduler before starting it.
        # It will use the TIME_ZONE, VACANT_TIMES, OCCUPIED_TIMES globals set by apply_config above.
        reschedule_jobs()
        scheduler.start()
        logger.info("✅ Scheduler started.")
    except Exception as e:
        logger.critical(f"❌ Failed to start scheduler: {e}", exc_info=True)
        # Clean up IoT client connection before exiting if scheduler fails
        try:
            await iot_client.disconnect()
        except Exception as disconnect_err:
            logger.error(f"Error during disconnect after scheduler failure: {disconnect_err}")
        raise SystemExit("Scheduler failed to start.")

    # 5. Graceful Shutdown Handling
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()
    shutdown_signal_received = False # Flag to prevent double handling

    def signal_handler(*args):
        nonlocal shutdown_signal_received
        if not shutdown_signal_received:
            logger.info("👋 Termination signal received. Initiating shutdown...")
            shutdown_signal_received = True
            # Use call_soon_threadsafe if signal handler runs in a different thread (common)
            # loop.call_soon_threadsafe(stop_event.set)
            # If running in the same thread (less common for signals), direct set is fine:
            stop_event.set()
        else:
            logger.info("Termination signal received again, already shutting down.")

    # Register signal handlers
    for sig in (signal.SIGTERM, signal.SIGINT):
        try:
            # Add the signal handler in a way that's compatible with the running loop
            loop.add_signal_handler(sig, signal_handler, sig) # Pass sig as argument if needed by handler
        except NotImplementedError: # Fallback for environments like Windows
             signal.signal(sig, signal_handler)

    logger.info(f"Bay Scheduler module running (PID: {os.getpid()}). Waiting for termination signal.")
    await stop_event.wait() # Wait until signal_handler sets the event

    # --- Shutdown Sequence ---
    logger.info("Starting graceful shutdown...")

    # Remove signal handlers to prevent race conditions during shutdown
    for sig in (signal.SIGTERM, signal.SIGINT):
        try:
            loop.remove_signal_handler(sig)
        except NotImplementedError:
            pass # signal.signal doesn't have a direct removal mechanism

    # Shutdown scheduler (wait=False allows other cleanup)
    logger.info("Shutting down scheduler...")
    try:
         # Give running jobs a chance to finish? Set wait=True or add timeout?
         # For now, initiate shutdown without blocking indefinitely.
         scheduler.shutdown(wait=False)
         logger.info("Scheduler shutdown initiated.")
    except Exception as e:
         logger.error(f"Error during scheduler shutdown: {e}")

    # Disconnect IoT Client
    logger.info("Disconnecting from IoT Edge Hub...")
    try:
        await iot_client.disconnect()
        logger.info("Disconnected from IoT Edge Hub.")
    except Exception as e:
        logger.error(f"Error during IoT client disconnection: {e}")

    logger.info("Shutdown complete.")

if __name__ == "__main__":
    logger.info(f"🚀 Starting Bay Scheduler Module (Log Level: {LOG_LEVEL})...")
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit) as e:
        logger.info("Received KeyboardInterrupt or SystemExit outside main loop.")
    except Exception as e:
        logger.critical(f"💥 Unhandled top-level exception: {e}", exc_info=True)
    finally:
        logger.info("🏁 Bay Scheduler Module has stopped.")
