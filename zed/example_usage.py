#!/usr/bin/env python3
"""
Example usage script for ZED Camera Vehicle Detection with OpenGL Viewer

This script demonstrates different ways to use the enhanced ZED detection system
with the integrated OpenGL viewer.
"""

import sys
import os
import time

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

def example_basic_detection():
    """Example: Basic vehicle detection without viewer"""
    print("Example 1: Basic Vehicle Detection (Console Only)")
    print("=" * 50)
    
    try:
        from main import ZEDStreamingVehicleDetector
        
        # Create detector instance
        detector = ZEDStreamingVehicleDetector(stream_ip="*************", stream_port=30000)
        
        # Initialize camera (this will fail without actual ZED stream)
        print("Attempting to initialize camera...")
        if detector.initialize_streaming_camera():
            print("Camera initialized successfully!")
            
            # Run detection for 30 seconds
            detector.run_detection_loop(duration_seconds=30, display_output=True)
        else:
            print("Camera initialization failed (expected without ZED stream)")
            
        # Cleanup
        detector.close()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("This is expected if ZED SDK is not installed")

def example_viewer_detection():
    """Example: Vehicle detection with OpenGL viewer"""
    print("\nExample 2: Vehicle Detection with OpenGL Viewer")
    print("=" * 50)
    
    try:
        from main import ZEDStreamingVehicleDetector
        
        # Create detector instance
        detector = ZEDStreamingVehicleDetector(stream_ip="*************", stream_port=30000)
        
        # Check if viewer is available
        if not detector.viewer_available:
            print("OpenGL viewer not available. Install dependencies:")
            print("pip install PyOpenGL PyOpenGL_accelerate")
            return
        
        # Initialize camera (this will fail without actual ZED stream)
        print("Attempting to initialize camera...")
        if detector.initialize_streaming_camera():
            print("Camera initialized successfully!")
            
            # Run detection with viewer enabled
            detector.run_detection_loop(
                duration_seconds=60,  # Run for 1 minute
                display_output=False,  # Disable console output
                enable_viewer=True     # Enable 3D viewer
            )
        else:
            print("Camera initialization failed (expected without ZED stream)")
            
        # Cleanup
        detector.close()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("This is expected if ZED SDK is not installed")

def example_single_frame():
    """Example: Single frame detection"""
    print("\nExample 3: Single Frame Detection")
    print("=" * 50)
    
    try:
        from main import ZEDStreamingVehicleDetector
        
        # Create detector instance
        detector = ZEDStreamingVehicleDetector()
        
        # Initialize camera (this will fail without actual ZED stream)
        if detector.initialize_streaming_camera():
            print("Camera initialized successfully!")
            
            # Get single detection result
            result = detector.get_single_detection()
            
            if "error" not in result:
                print(f"Detected {len(result['vehicles'])} vehicles")
                for vehicle in result['vehicles']:
                    print(f"Vehicle {vehicle['id']}: {vehicle['label']}")
                    print(f"  Position: {vehicle['position']}")
                    print(f"  Dimensions: {vehicle['dimensions_3d']}")
            else:
                print(f"Detection error: {result['error']}")
        else:
            print("Camera initialization failed (expected without ZED stream)")
            
        # Cleanup
        detector.close()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("This is expected if ZED SDK is not installed")

def example_command_line_usage():
    """Example: Show command line usage"""
    print("\nExample 4: Command Line Usage")
    print("=" * 50)
    
    print("Basic usage:")
    print("python3 main.py --ip ************* --port 30000")
    print()
    
    print("With OpenGL viewer:")
    print("python3 main.py --ip ************* --port 30000 --viewer")
    print()
    
    print("Viewer with minimal console output:")
    print("python3 main.py --ip ************* --port 30000 --viewer --no-output")
    print()
    
    print("Test connection only:")
    print("python3 main.py --ip ************* --port 30000 --test")
    print()
    
    print("Limited duration (60 seconds):")
    print("python3 main.py --ip ************* --port 30000 --duration 60")
    print()
    
    print("All available options:")
    print("python3 main.py --help")

def main():
    """Run all examples"""
    print("ZED Camera Vehicle Detection - Usage Examples")
    print("=" * 60)
    print()
    print("Note: These examples will fail without a ZED camera stream,")
    print("but they demonstrate the API usage patterns.")
    print()
    
    # Run examples
    example_basic_detection()
    example_viewer_detection()
    example_single_frame()
    example_command_line_usage()
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print()
    print("To use with real ZED camera:")
    print("1. Set up ZED streaming sender on another device")
    print("2. Replace IP address with actual sender IP")
    print("3. Install ZED SDK and OpenGL dependencies")
    print("4. Run the examples or main script")
    print()
    print("Note: This version fixes the common error:")
    print("'numpy.ndarray' object has no attribute 'x'")
    print("by properly handling ZED SDK bounding box formats.")

if __name__ == "__main__":
    main()
