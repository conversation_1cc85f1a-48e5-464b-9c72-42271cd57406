#!/usr/bin/env python3
"""
Test script to verify the AGL viewer integration with the ZED detection system.
This script tests the import and basic functionality without requiring a ZED camera.
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test main module import
        from main import ZEDStreamingVehicleDetector
        print("✓ Main detector class imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import main detector: {e}")
        return False
    
    try:
        # Test viewer import
        sys.path.append(os.path.join(os.path.dirname(__file__), 'ogl_viewer'))
        from viewer import GL<PERSON>iewer
        print("✓ GLViewer imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import GLViewer: {e}")
        print("Note: This is expected if OpenGL dependencies are not installed")
    
    return True

def test_detector_initialization():
    """Test detector initialization without camera"""
    print("\nTesting detector initialization...")
    
    try:
        from main import ZEDStreamingVehicleDetector
        detector = ZEDStreamingVehicleDetector()
        print("✓ Detector instance created successfully")
        
        # Test viewer availability check
        print(f"✓ Viewer available: {detector.viewer_available}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to create detector: {e}")
        return False

def test_command_line_args():
    """Test command line argument parsing"""
    print("\nTesting command line arguments...")
    
    try:
        import argparse
        from main import main
        
        # Test help output (this will show all available arguments)
        print("Available command line arguments:")
        print("Run: python main.py --help")
        print("✓ Command line parsing should work")
        
        return True
    except Exception as e:
        print(f"✗ Failed to test command line args: {e}")
        return False

def main():
    """Run all tests"""
    print("ZED Viewer Integration Test")
    print("=" * 40)
    
    success = True
    
    success &= test_imports()
    success &= test_detector_initialization()
    success &= test_command_line_args()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed!")
        print("\nTo use the viewer:")
        print("1. Install OpenGL dependencies: pip install PyOpenGL PyOpenGL_accelerate")
        print("2. Run with viewer: python main.py --viewer --ip <stream_ip> --port <stream_port>")
    else:
        print("✗ Some tests failed. Check the output above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
