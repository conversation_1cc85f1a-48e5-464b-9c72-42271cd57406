# ZED Camera Vehicle Detection with OpenGL Viewer

This enhanced version of the ZED camera vehicle detection script now includes an integrated OpenGL viewer for real-time 3D visualization of detected vehicles.

## New Features

- **3D Visualization**: Real-time OpenGL rendering of detected vehicles with 3D bounding boxes
- **Interactive Viewer**: Pan, zoom, and interact with the 3D scene
- **Vehicle Tracking**: Visual tracking of vehicles with unique colors and IDs
- **Dimension Display**: Real-time display of vehicle dimensions in the 3D space

## Requirements

### Basic Requirements (same as before)
- ZED SDK Python API (pyzed)
- ZED camera streaming from another device (sender)
- Python 3.6+
- NVIDIA GPU with NVENC support for decoding

### Additional Requirements for Viewer
- OpenGL support
- PyOpenGL and PyOpenGL_accelerate
- GLUT (usually included with OpenGL)

## Installation

### Install OpenGL Dependencies
```bash
pip install PyOpenGL PyOpenGL_accelerate
```

### On Ubuntu/Debian
```bash
sudo apt-get install freeglut3-dev
```

### On Windows
OpenGL is usually included with graphics drivers.

## Usage

### Basic Usage (Console Output Only)
```bash
python3 main.py --ip ************* --port 30000
```

### With 3D Viewer
```bash
python3 main.py --ip ************* --port 30000 --viewer
```

### Viewer with Minimal Console Output
```bash
python3 main.py --ip ************* --port 30000 --viewer --no-output
```

### Test Stream Connection
```bash
python3 main.py --ip ************* --port 30000 --test
```

## Command Line Arguments

- `--ip`: IP address of ZED streaming sender (default: 127.0.0.1)
- `--port`: Port of ZED streaming sender (default: 30000)
- `--duration`: Duration in seconds (default: infinite)
- `--test`: Test stream connection only
- `--viewer`: Enable OpenGL 3D visualization
- `--no-output`: Disable console output (useful with viewer)

## Viewer Controls

- **Mouse**: Rotate and pan the 3D view
- **Scroll**: Zoom in/out
- **Q or ESC**: Quit the application
- **Window Close**: Stop detection and exit

## Viewer Features

### Visual Elements
- **Live Camera Feed**: Real-time video stream as background
- **3D Bounding Boxes**: Colored boxes around detected vehicles
- **Vehicle IDs**: Unique identifiers displayed above each vehicle
- **Tracking States**: Different colors for tracked vs. detected objects

### Color Coding
- **Tracked Vehicles**: Unique colors per vehicle ID
- **Newly Detected**: Standard class color (magenta for vehicles)
- **Lost Tracking**: Faded colors

## Troubleshooting

### Viewer Not Available
If you see "OpenGL viewer not available", install the dependencies:
```bash
pip install PyOpenGL PyOpenGL_accelerate
```

### Performance Issues
- Reduce camera resolution in the streaming sender
- Lower FPS settings
- Ensure NVIDIA GPU drivers are up to date

### Viewer Window Issues
- Make sure you have a display available (not running headless)
- Check OpenGL support: `glxinfo | grep OpenGL` (Linux)
- Try running with `--no-output` to reduce console overhead

## Integration Details

The viewer integration adds the following to the main detection class:

### New Methods
- `initialize_viewer()`: Set up OpenGL viewer with camera parameters
- `update_viewer()`: Update viewer with current frame and detections

### New Parameters
- `enable_viewer`: Boolean flag to enable/disable viewer
- Automatic viewer lifecycle management

### Thread Safety
The viewer uses mutex locks to ensure thread-safe updates between the detection loop and OpenGL rendering.

### Memory Management
The integration includes proper memory cleanup:
- **ZED Image Memory**: Automatically freed using `image.free(memory_type=sl.MEM.CPU)`
- **ZED Depth Memory**: Automatically freed using `depth.free(memory_type=sl.MEM.CPU)`
- **OpenGL Resources**: Textures and buffers are properly deleted on viewer close
- **Error Handling**: Safe cleanup with exception handling to prevent crashes

### ZED SDK Compatibility
The implementation handles different ZED SDK object formats:
- **2D Bounding Boxes**: Supports both numpy arrays and point objects
- **3D Bounding Boxes**: Handles numpy arrays with proper shape validation
- **Error Recovery**: Graceful handling of malformed bounding box data
- **Format Detection**: Automatic detection of bounding box format

## Example Output

### Console Mode
```
Starting vehicle detection from stream *************:30000
Press Ctrl+C to stop

--- Frame 1 (Stream: *************:30000) ---
Detected 2 vehicle(s)
Vehicle ID 1 (VEHICLE):
  Confidence: 85.2%
  Position: (5.23, -0.15, 12.45) m
  3D Dimensions - W: 1.85m, H: 1.52m, D: 4.12m
  Tracking: OK
```

### Viewer Mode
```
Starting vehicle detection from stream *************:30000
OpenGL viewer initialized successfully!
Press 'q' or 'ESC' in the viewer window to quit
```

The viewer window will show the live camera feed with 3D bounding boxes overlaid on detected vehicles.
