#!/usr/bin/env python3
"""
ZED Camera Vehicle Detection and Dimension Measurement Script (Streaming Version)

This script connects to a ZED camera via streaming, performs real-time object detection 
to identify vehicles, and calculates their 3D dimensions (width and height).

Requirements:
- ZED SDK Python API (pyzed)
- ZED camera streaming from another device (sender)
- Python 3.6+
- NVIDIA GPU with NVENC support for decoding

Author: Generated for ZED camera streaming integration
"""

import pyzed.sl as sl
import cv2
import numpy as np
import time
import math
import argparse
from typing import Dict, List, Tuple, Optional

class ZEDStreamingVehicleDetector:
    """
    ZED Camera Vehicle Detection and Dimension Measurement Class (Streaming Version)
    """
    
    def __init__(self, stream_ip: str = "127.0.0.1", stream_port: int = 30000):
        """
        Initialize the ZED streaming receiver and object detection parameters
        
        Args:
            stream_ip: IP address of the ZED streaming sender
            stream_port: Port of the ZED streaming sender
        """
        self.zed = sl.Camera()
        self.image = sl.Mat()
        self.depth = sl.Mat()
        self.objects = sl.Objects()
        self.is_initialized = False
        self.stream_ip = stream_ip
        self.stream_port = stream_port
        
        # Vehicle classes that we're interested in
        self.vehicle_classes = [
            sl.OBJECT_CLASS.VEHICLE,
        ]
        
    def initialize_streaming_camera(self, resolution=sl.RESOLUTION.HD720, fps=30):
        """
        Initialize the ZED camera from streaming source
        
        Args:
            resolution: Camera resolution (default: HD720)
            fps: Frames per second (default: 30)
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        # Set initialization parameters for streaming
        init_params = sl.InitParameters()
        
        # Configure for streaming input
        init_params.set_from_stream(self.stream_ip, self.stream_port)
        
        # Set other camera parameters
        init_params.camera_resolution = resolution
        init_params.camera_fps = fps
        init_params.depth_mode = sl.DEPTH_MODE.NEURAL  # High quality depth
        init_params.coordinate_units = sl.UNIT.METER  # Use meters for measurements
        init_params.depth_minimum_distance = 0.3  # Minimum detection distance
        init_params.depth_maximum_distance = 40.0  # Maximum detection distance
        
        print(f"Attempting to connect to ZED stream at {self.stream_ip}:{self.stream_port}")
        
        # Open the camera from stream
        zed_error = self.zed.open(init_params)
        if zed_error != sl.ERROR_CODE.SUCCESS:
            print(f"Failed to open ZED camera from stream: {zed_error}")
            print("Please ensure:")
            print("1. The ZED streaming sender is running")
            print("2. The IP address and port are correct")
            print("3. Network connectivity is available")
            return False
        
        print("Successfully connected to ZED stream!")
        
        # Enable positional tracking (required for object tracking)
        tracking_params = sl.PositionalTrackingParameters()
        zed_error = self.zed.enable_positional_tracking(tracking_params)
        if zed_error != sl.ERROR_CODE.SUCCESS:
            print(f"Failed to enable positional tracking: {zed_error}")
            return False
        
        # Configure object detection
        detection_params = sl.ObjectDetectionParameters()
        detection_params.enable_tracking = True  # Track objects between frames
        detection_params.enable_segmentation = False  # We don't need masks for dimensions
        detection_params.detection_model = sl.OBJECT_DETECTION_MODEL.MULTI_CLASS_BOX_ACCURATE
        
        # Enable object detection
        zed_error = self.zed.enable_object_detection(detection_params)
        if zed_error != sl.ERROR_CODE.SUCCESS:
            print(f"Failed to enable object detection: {zed_error}")
            return False
        
        # Get camera information for accurate calculations
        camera_info = self.zed.get_camera_information()
        print(f"Camera model: {camera_info.camera_model}")
        print(f"Stream resolution: {camera_info.camera_configuration.resolution.width}x{camera_info.camera_configuration.resolution.height}")
        print(f"FPS: {camera_info.camera_configuration.fps}")
        
        self.is_initialized = True
        print("ZED streaming camera initialized successfully!")
        return True
    
    def calculate_3d_dimensions(self, bbox_3d: np.ndarray) -> Tuple[float, float, float]:
        """
        Calculate width, height, and depth from 3D bounding box
        
        Args:
            bbox_3d: NumPy array of 8 3D points representing the bounding box
        
        Returns:
            Tuple of (width, height, depth) in meters
        """
        if len(bbox_3d) != 8:
            return 0.0, 0.0, 0.0
        
        # The points are already a NumPy array, so we can use them directly
        # Width: distance between front-left and front-right points
        width = np.linalg.norm(bbox_3d[0] - bbox_3d[1])
        
        # Height: distance between top and bottom points
        height = np.linalg.norm(bbox_3d[0] - bbox_3d[4])
        
        # Depth: distance between front and back points
        depth = np.linalg.norm(bbox_3d[0] - bbox_3d[3])
        
        return width, height, depth
    
    def calculate_2d_dimensions_from_depth(self, bbox_2d: List, depth_values: np.ndarray, camera_info) -> Tuple[float, float]:
        """
        Calculate real-world dimensions from 2D bounding box and depth information
        
        Args:
            bbox_2d: List of 4 2D points representing the bounding box
            depth_values: Depth map for the current frame
            camera_info: Camera calibration information
        
        Returns:
            Tuple of (width, height) in meters
        """
        if len(bbox_2d) != 4:
            return 0.0, 0.0
        
        # Get bounding box coordinates
        x1, y1 = int(bbox_2d[0].x), int(bbox_2d[0].y)
        x2, y2 = int(bbox_2d[2].x), int(bbox_2d[2].y)  # Bottom-right corner
        
        # Ensure coordinates are within image bounds
        height, width = depth_values.shape[:2]
        x1, x2 = max(0, min(x1, width-1)), max(0, min(x2, width-1))
        y1, y2 = max(0, min(y1, height-1)), max(0, min(y2, height-1))
        
        if x1 >= x2 or y1 >= y2:
            return 0.0, 0.0
        
        # Get average depth of the object
        roi_depth = depth_values[y1:y2, x1:x2]
        valid_depths = roi_depth[roi_depth > 0]  # Filter out invalid depth values
        
        if len(valid_depths) == 0:
            return 0.0, 0.0
        
        avg_depth = np.median(valid_depths)  # Use median for robustness
        
        # Get camera intrinsic parameters
        calibration = camera_info.camera_configuration.calibration_parameters.left_cam
        fx = calibration.fx
        fy = calibration.fy
        
        # Calculate real-world dimensions
        pixel_width = abs(x2 - x1)
        pixel_height = abs(y2 - y1)
        
        # Convert pixel dimensions to real-world dimensions using depth
        real_width = (pixel_width * avg_depth) / fx
        real_height = (pixel_height * avg_depth) / fy
        
        return real_width, real_height
    
    def process_frame(self) -> Dict:
        """
        Process a single frame and detect vehicles with their dimensions
        
        Returns:
            Dictionary containing detection results
        """
        if not self.is_initialized:
            return {"error": "Camera not initialized"}
        
        # Grab a new frame
        runtime_params = sl.RuntimeParameters()
        grab_status = self.zed.grab(runtime_params)
        
        if grab_status != sl.ERROR_CODE.SUCCESS:
            if grab_status == sl.ERROR_CODE.CAMERA_NOT_DETECTED:
                return {"error": "Stream connection lost"}
            else:
                return {"error": f"Failed to grab frame: {grab_status}"}
        
        # Retrieve the image and depth data
        self.zed.retrieve_image(self.image, sl.VIEW.LEFT)
        self.zed.retrieve_measure(self.depth, sl.MEASURE.DEPTH)
        
        # Retrieve detected objects
        detection_runtime_params = sl.ObjectDetectionRuntimeParameters()
        detection_runtime_params.detection_confidence_threshold = 50  # 50% confidence threshold
        
        self.zed.retrieve_objects(self.objects, detection_runtime_params)
        
        # Process detected objects
        frame_results = {
            "timestamp": self.objects.timestamp.get_milliseconds(),
            "vehicles": [],
            "total_detections": len(self.objects.object_list),
            "stream_info": {
                "ip": self.stream_ip,
                "port": self.stream_port
            }
        }
        
        # Get camera information for accurate calculations
        camera_info = self.zed.get_camera_information()
        
        for obj in self.objects.object_list:
            # Check if the detected object is a vehicle
            if obj.label in self.vehicle_classes:
                # Calculate 3D dimensions from 3D bounding box
                width_3d, height_3d, depth_3d = self.calculate_3d_dimensions(obj.bounding_box)
                
                # Get depth data as numpy array
                depth_array = self.depth.get_data()
                
                # Calculate 2D dimensions as backup/comparison
                width_2d, height_2d = self.calculate_2d_dimensions_from_depth(
                    obj.bounding_box_2d, depth_array, camera_info
                )
                
                vehicle_data = {
                    "id": obj.id,
                    "label": str(obj.label).split('.')[-1],  # Get label name
                    "confidence": obj.confidence,
                    "position": {
                        "x": obj.position.x,
                        "y": obj.position.y,
                        "z": obj.position.z
                    },
                    "dimensions_3d": {
                        "width": round(width_3d, 2),
                        "height": round(height_3d, 2),
                        "depth": round(depth_3d, 2)
                    },
                    "dimensions_2d_calculated": {
                        "width": round(width_2d, 2),
                        "height": round(height_2d, 2)
                    },
                    "tracking_state": str(obj.tracking_state).split('.')[-1],
                    "bounding_box_2d": [
                        {"x": int(p.x), "y": int(p.y)} for p in obj.bounding_box_2d
                    ]
                }
                
                frame_results["vehicles"].append(vehicle_data)
        
        return frame_results
    
    def run_detection_loop(self, duration_seconds: Optional[int] = None, display_output: bool = True):
        """
        Run the main detection loop
        
        Args:
            duration_seconds: How long to run (None for infinite)
            display_output: Whether to print detection results
        """
        if not self.is_initialized:
            print("Error: Camera not initialized. Call initialize_streaming_camera() first.")
            return
        
        print(f"Starting vehicle detection from stream {self.stream_ip}:{self.stream_port}")
        print("Press Ctrl+C to stop")
        
        start_time = time.time()
        frame_count = 0
        connection_errors = 0
        max_connection_errors = 10
        
        try:
            while True:
                # Check duration limit
                if duration_seconds and (time.time() - start_time) > duration_seconds:
                    break
                
                # Process frame
                results = self.process_frame()
                frame_count += 1
                
                if "error" in results:
                    connection_errors += 1
                    print(f"Frame {frame_count} - Error: {results['error']}")
                    
                    if connection_errors >= max_connection_errors:
                        print("Too many connection errors. Stopping detection.")
                        break
                    
                    time.sleep(1)  # Wait before retrying
                    continue
                else:
                    connection_errors = 0  # Reset error counter on successful frame
                
                if display_output and results["vehicles"]:
                    print(f"\n--- Frame {frame_count} (Stream: {self.stream_ip}:{self.stream_port}) ---")
                    print(f"Detected {len(results['vehicles'])} vehicle(s)")
                    
                    for vehicle in results["vehicles"]:
                        print(f"Vehicle ID {vehicle['id']} ({vehicle['label']}):")
                        print(f"  Confidence: {vehicle['confidence']:.1%}")
                        print(f"  Position: ({vehicle['position']['x']:.2f}, "
                              f"{vehicle['position']['y']:.2f}, {vehicle['position']['z']:.2f}) m")
                        print(f"  3D Dimensions - W: {vehicle['dimensions_3d']['width']}m, "
                              f"H: {vehicle['dimensions_3d']['height']}m, "
                              f"D: {vehicle['dimensions_3d']['depth']}m")
                        print(f"  2D Calculated - W: {vehicle['dimensions_2d_calculated']['width']}m, "
                              f"H: {vehicle['dimensions_2d_calculated']['height']}m")
                        print(f"  Tracking: {vehicle['tracking_state']}")
                elif display_output and frame_count % 30 == 0:  # Show status every 30 frames
                    print(f"Frame {frame_count} - No vehicles detected")
                
                # Small delay to prevent overwhelming output
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print(f"\nStopping detection. Processed {frame_count} frames.")
    
    def get_single_detection(self) -> Dict:
        """
        Get detection results from a single frame
        
        Returns:
            Dictionary containing detection results
        """
        return self.process_frame()
    
    def test_stream_connection(self) -> bool:
        """
        Test the stream connection without full initialization
        
        Returns:
            bool: True if stream is accessible, False otherwise
        """
        print(f"Testing connection to {self.stream_ip}:{self.stream_port}...")
        
        # Create a minimal initialization just to test connection
        test_zed = sl.Camera()
        init_params = sl.InitParameters()
        init_params.set_from_stream(self.stream_ip, self.stream_port)
        
        zed_error = test_zed.open(init_params)
        
        if zed_error == sl.ERROR_CODE.SUCCESS:
            print("✓ Stream connection successful!")
            test_zed.close()
            return True
        else:
            print(f"✗ Stream connection failed: {zed_error}")
            return False
    
    def close(self):
        """Close the camera and cleanup resources"""
        if self.is_initialized:
            self.zed.disable_object_detection()
            self.zed.disable_positional_tracking()
            self.zed.close()
            self.is_initialized = False
            print("ZED streaming camera closed.")

def main():
    """Main function to demonstrate the vehicle detection with streaming"""
    parser = argparse.ArgumentParser(description='ZED Streaming Vehicle Detection')
    parser.add_argument('--ip', default='127.0.0.1', help='IP address of ZED streaming sender (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=30000, help='Port of ZED streaming sender (default: 30000)')
    parser.add_argument('--duration', type=int, help='Duration in seconds (default: infinite)')
    parser.add_argument('--test', action='store_true', help='Test stream connection only')
    
    args = parser.parse_args()
    
    detector = ZEDStreamingVehicleDetector(stream_ip=args.ip, stream_port=args.port)
    
    try:
        # Test connection if requested
        if args.test:
            detector.test_stream_connection()
            return
        
        # Initialize the streaming camera
        if not detector.initialize_streaming_camera():
            print("Failed to initialize ZED streaming camera.")
            print("\nTroubleshooting tips:")
            print("1. Ensure the ZED streaming sender is running on the specified IP:PORT")
            print("2. Check network connectivity")
            print("3. Verify firewall settings allow the connection")
            print("4. Try using --test flag to test connection only")
            return
        
        # Run detection
        detector.run_detection_loop(duration_seconds=args.duration, display_output=True)
        
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        detector.close()

if __name__ == "__main__":
    main()
