#!/usr/bin/env python3
"""
Test script to verify memory cleanup functionality in the ZED detection system.
This script tests the memory management without requiring a ZED camera.
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

def test_memory_cleanup_without_zed():
    """Test memory cleanup functionality without ZED SDK"""
    print("Testing memory cleanup functionality...")
    
    try:
        from main import ZEDStreamingVehicleDetector
        
        # Create detector instance
        detector = ZEDStreamingVehicleDetector()
        print("✓ Detector instance created")
        
        # Test close method without initialization (should handle gracefully)
        print("Testing close() without initialization...")
        detector.close()
        print("✓ Close method handled gracefully without initialization")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error (expected without ZED SDK): {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_viewer_cleanup_without_opengl():
    """Test viewer cleanup functionality without OpenGL"""
    print("\nTesting viewer cleanup functionality...")
    
    try:
        # Test viewer import
        sys.path.append(os.path.join(os.path.dirname(__file__), 'ogl_viewer'))
        from viewer import ImageHandler
        
        # Create image handler instance
        handler = ImageHandler()
        print("✓ ImageHandler instance created")
        
        # Test close method (should handle gracefully even without OpenGL)
        print("Testing ImageHandler.close() without OpenGL...")
        handler.close()
        print("✓ ImageHandler close method handled gracefully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error (expected without OpenGL): {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_memory_cleanup_patterns():
    """Test memory cleanup patterns and best practices"""
    print("\nTesting memory cleanup patterns...")
    
    # Test hasattr pattern
    class MockObject:
        def __init__(self):
            self.image = None
            self.depth = None
    
    obj = MockObject()
    
    # Test the hasattr pattern used in the code
    try:
        if hasattr(obj, 'image') and obj.image:
            print("Image cleanup would be called")
        else:
            print("✓ Image cleanup skipped (no image)")
            
        if hasattr(obj, 'depth') and obj.depth:
            print("Depth cleanup would be called")
        else:
            print("✓ Depth cleanup skipped (no depth)")
            
        return True
        
    except Exception as e:
        print(f"✗ Error in cleanup pattern: {e}")
        return False

def show_memory_cleanup_best_practices():
    """Show memory cleanup best practices"""
    print("\nMemory Cleanup Best Practices:")
    print("=" * 40)
    
    print("1. ZED Image Memory:")
    print("   image.free(memory_type=sl.MEM.CPU)")
    print()
    
    print("2. ZED Depth Memory:")
    print("   depth.free(memory_type=sl.MEM.CPU)")
    print()
    
    print("3. OpenGL Textures:")
    print("   glDeleteTextures(1, [texture_id])")
    print()
    
    print("4. OpenGL Buffers:")
    print("   glDeleteBuffers(1, [buffer_id])")
    print()
    
    print("5. Safe Cleanup Pattern:")
    print("   try:")
    print("       if hasattr(self, 'resource') and self.resource:")
    print("           self.resource.free()")
    print("   except Exception as e:")
    print("       print(f'Warning: Failed to free resource: {e}')")
    print()
    
    print("6. Cleanup Order:")
    print("   - Close viewer first")
    print("   - Free image/depth memory")
    print("   - Disable ZED features")
    print("   - Close ZED camera")

def main():
    """Run all memory cleanup tests"""
    print("ZED Memory Cleanup Test")
    print("=" * 30)
    
    success = True
    
    success &= test_memory_cleanup_without_zed()
    success &= test_viewer_cleanup_without_opengl()
    success &= test_memory_cleanup_patterns()
    
    show_memory_cleanup_best_practices()
    
    print("\n" + "=" * 30)
    if success:
        print("✓ All memory cleanup tests passed!")
        print("\nThe memory cleanup implementation follows best practices:")
        print("- Safe attribute checking with hasattr()")
        print("- Exception handling for graceful degradation")
        print("- Proper cleanup order (viewer -> memory -> camera)")
        print("- OpenGL resource cleanup in viewer")
    else:
        print("✗ Some tests failed. Check the output above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
